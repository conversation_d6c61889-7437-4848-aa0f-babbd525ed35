"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/student/page",{

/***/ "(app-pages-browser)/./app/student/page.tsx":
/*!******************************!*\
  !*** ./app/student/page.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentApp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _lib_database_store__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/database-store */ \"(app-pages-browser)/./lib/database-store.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction StudentApp() {\n    _s();\n    const [currentStudent, setCurrentStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [studentEntries, setStudentEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [copiedQR, setCopiedQR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const entriesPerPage = 5;\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentApp.useEffect\": ()=>{\n            if (true) {\n                const studentLoggedIn = localStorage.getItem(\"studentLoggedIn\");\n                const studentId = localStorage.getItem(\"studentId\");\n                if (!studentLoggedIn || !studentId) {\n                    router.push(\"/\");\n                    return;\n                }\n                loadStudentData(studentId);\n            }\n        }\n    }[\"StudentApp.useEffect\"], [\n        router\n    ]);\n    // Auto-refresh student entries every 3 seconds for real-time updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentApp.useEffect\": ()=>{\n            if (!isAuthenticated || !currentStudent) return;\n            const interval = setInterval({\n                \"StudentApp.useEffect.interval\": ()=>{\n                    console.log(\"🔄 Auto-refreshing student entries...\");\n                    loadStudentEntries();\n                }\n            }[\"StudentApp.useEffect.interval\"], 3000) // 3 seconds for faster updates\n            ;\n            return ({\n                \"StudentApp.useEffect\": ()=>clearInterval(interval)\n            })[\"StudentApp.useEffect\"];\n        }\n    }[\"StudentApp.useEffect\"], [\n        isAuthenticated,\n        currentStudent\n    ]);\n    const loadStudentEntries = async ()=>{\n        if (!currentStudent) return;\n        try {\n            console.log(\"\\uD83D\\uDD0D Fetching entries for student: \".concat(currentStudent.name, \" (\").concat(currentStudent.application_number, \")\"));\n            // Try to get all entries and filter for this student\n            const entriesRes = await fetch('/api/entries');\n            if (entriesRes.ok) {\n                const allEntries = await entriesRes.json();\n                console.log(\"\\uD83D\\uDCCA Total entries in database: \".concat(allEntries.length));\n                // Filter entries for this student by both student_id and application_number\n                const studentEntries = allEntries.filter((entry)=>{\n                    const matchesId = entry.student_id === currentStudent.id;\n                    const matchesAppNumber = entry.application_number === currentStudent.application_number;\n                    const matchesName = entry.student_name === currentStudent.name;\n                    return matchesId || matchesAppNumber || matchesName;\n                });\n                // Sort by entry time (newest first)\n                studentEntries.sort((a, b)=>{\n                    const dateA = new Date(a.entry_time || a.entryTime || a.timestamp);\n                    const dateB = new Date(b.entry_time || b.entryTime || b.timestamp);\n                    return dateB.getTime() - dateA.getTime();\n                });\n                setStudentEntries(studentEntries);\n                console.log(\"✅ Found \".concat(studentEntries.length, \" entries for \").concat(currentStudent.name, \":\"), studentEntries);\n                // Debug: Check entry data structure\n                if (studentEntries.length > 0) {\n                    console.log(\"📊 Sample entry structure:\", studentEntries[0]);\n                    console.log(\"📊 Entry properties:\", Object.keys(studentEntries[0]));\n                }\n            } else {\n                console.error(\"❌ API error: \".concat(entriesRes.status));\n            }\n        } catch (error) {\n            console.error(\"❌ Error refreshing entries:\", error);\n        }\n    };\n    const loadStudentData = async (studentId)=>{\n        try {\n            setLoading(true);\n            setIsAuthenticated(true);\n            // Get student data from shared MongoDB via API\n            const studentsRes = await fetch('/api/students');\n            if (!studentsRes.ok) throw new Error('Failed to fetch students');\n            const students = await studentsRes.json();\n            const student = students.find((s)=>s.id === studentId);\n            if (student) {\n                setCurrentStudent(student);\n                // Get student's entry history from shared MongoDB\n                try {\n                    const entriesRes = await fetch(\"/api/entries?studentId=\".concat(student.id));\n                    if (entriesRes.ok) {\n                        const allEntries = await entriesRes.json();\n                        // Filter entries for this student\n                        const studentEntries = allEntries.filter((entry)=>entry.student_id === student.id || entry.application_number === student.application_number);\n                        setStudentEntries(studentEntries);\n                        console.log(\"✅ Loaded \".concat(studentEntries.length, \" entries for student \").concat(student.name));\n                    } else {\n                        console.log(\"⚠️ Could not fetch entries from API, using fallback\");\n                        const entries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_8__.dbStore.getStudentEntries(student.id);\n                        setStudentEntries(entries);\n                    }\n                } catch (entriesError) {\n                    console.log(\"⚠️ API error, using database fallback for entries\");\n                    const entries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_8__.dbStore.getStudentEntries(student.id);\n                    setStudentEntries(entries);\n                }\n            } else {\n                handleLogout();\n            }\n        } catch (error) {\n            console.error(\"Error loading student data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleLogout = ()=>{\n        if (true) {\n            localStorage.removeItem(\"studentLoggedIn\");\n            localStorage.removeItem(\"studentId\");\n            localStorage.removeItem(\"studentAppNumber\");\n        }\n        router.push(\"/\");\n    };\n    const handleRefresh = ()=>{\n        if (currentStudent) {\n            loadStudentData(currentStudent.id);\n        }\n    };\n    const generateSimpleQRCode = ()=>{\n        if (!currentStudent) return \"\";\n        return currentStudent.application_number;\n    };\n    const copyQRData = async ()=>{\n        try {\n            const qrData = generateSimpleQRCode();\n            await navigator.clipboard.writeText(qrData);\n            setCopiedQR(true);\n            setTimeout(()=>setCopiedQR(false), 2000);\n        } catch (error) {\n            alert(\"Failed to copy QR data\");\n        }\n    };\n    const formatTime = (date)=>{\n        if (!date) return \"N/A\";\n        const dateObj = typeof date === 'string' ? new Date(date) : date;\n        if (isNaN(dateObj.getTime())) return \"Invalid Date\";\n        return dateObj.toLocaleString(\"en-IN\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const formatDate = (date)=>{\n        if (!date) return \"N/A\";\n        const dateObj = typeof date === 'string' ? new Date(date) : date;\n        if (isNaN(dateObj.getTime())) return \"Invalid Date\";\n        return dateObj.toLocaleString(\"en-IN\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\"\n        });\n    };\n    const toggleSection = (section)=>{\n        setActiveSection(activeSection === section ? null : section);\n    };\n    if (!isAuthenticated || !currentStudent) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-blue-50 p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-700\",\n                        children: \"Loading student data...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                lineNumber: 205,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n            lineNumber: 204,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-green-50 to-blue-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"shadow-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: (currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.image_url) || \"/placeholder.svg?height=50&width=50\",\n                                            alt: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name,\n                                            className: \"w-10 h-10 rounded-full border-2 border-green-200 object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: [\n                                                        \"Welcome, \",\n                                                        currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"App No: \",\n                                                        currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.application_number\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 w-full sm:w-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleRefresh,\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            disabled: loading,\n                                            className: \"flex-1 sm:flex-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"mr-1 h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only sm:not-sr-only\",\n                                                    children: \"Refresh\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleLogout,\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"flex-1 sm:flex-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-1 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only sm:not-sr-only\",\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: ()=>router.push(\"/\"),\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"flex-1 sm:flex-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"mr-1 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only sm:not-sr-only\",\n                                                    children: \"Home\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"border border-blue-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>toggleSection(\"idCard\"),\n                                    className: \"w-full p-4 flex items-center justify-between text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-100 p-2 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-5 w-5 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Digital ID Card\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Show your QR code at security stations\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this),\n                                        activeSection === \"idCard\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this),\n                                activeSection === \"idCard\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"pt-0 px-4 pb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl p-5 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-xl font-bold\",\n                                                                children: \"College Identity Card\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-blue-100 text-sm\",\n                                                                children: \"Official Identification Document\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-blue-200\",\n                                                                children: \"Valid Until\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold\",\n                                                                children: \"31/12/2025\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col md:flex-row gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white border-2 border-white rounded-lg overflow-hidden\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: (currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.image_url) || \"/placeholder.svg?height=100&width=80\",\n                                                                            alt: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name,\n                                                                            className: \"w-20 h-24 object-cover\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                            lineNumber: 310,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 309,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                                className: \"text-lg font-bold\",\n                                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 318,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2 space-y-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-xs text-blue-200\",\n                                                                                                children: \"Application Number\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                                lineNumber: 321,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"font-mono font-bold\",\n                                                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.application_number\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                                lineNumber: 322,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 320,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-xs text-blue-200\",\n                                                                                                children: \"Department\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                                lineNumber: 325,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"font-bold\",\n                                                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.department\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                                lineNumber: 326,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 324,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 319,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 317,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 grid grid-cols-2 gap-3 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-blue-200\",\n                                                                                children: \"Class\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 334,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-bold\",\n                                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.class\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 335,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 333,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-blue-200\",\n                                                                                children: \"Phone\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 338,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-bold\",\n                                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.phone\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 339,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 337,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-white p-2 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: \"https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=\".concat(encodeURIComponent(generateSimpleQRCode())),\n                                                                    alt: \"Student QR Code\",\n                                                                    className: \"w-32 h-32\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 346,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2 text-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-mono text-xs bg-blue-400/20 px-2 py-1 rounded\",\n                                                                    children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.application_number\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                value: generateSimpleQRCode(),\n                                                                readOnly: true,\n                                                                className: \"bg-white/10 border-white/20 text-white placeholder-white/50 text-center font-mono text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                onClick: copyQRData,\n                                                                size: \"sm\",\n                                                                className: \"absolute top-1 right-1 h-6 px-2 bg-white/20 hover:bg-white/30\",\n                                                                children: copiedQR ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 37\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 69\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-blue-200 mt-1 text-center\",\n                                                        children: \"Copy application number for manual entry at station\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"border border-green-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>toggleSection(\"details\"),\n                                    className: \"w-full p-4 flex items-center justify-between text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-100 p-2 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-5 w-5 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Personal Details\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"View your registration information\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 15\n                                        }, this),\n                                        activeSection === \"details\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 13\n                                }, this),\n                                activeSection === \"details\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"pt-0 px-4 pb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col md:flex-row gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: (currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.image_url) || \"/placeholder.svg?height=120&width=120\",\n                                                        alt: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name,\n                                                        className: \"w-24 h-24 rounded-full border-4 border-green-200 object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"mt-2 text-lg font-semibold\",\n                                                        children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"mt-1\",\n                                                        children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.class\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-gray-500 text-sm\",\n                                                                        children: \"Phone\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 422,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.phone\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 423,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-gray-500 text-sm\",\n                                                                        children: \"Email\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 426,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: (currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.email) || \"Not provided\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 427,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-gray-500 text-sm\",\n                                                                        children: \"Department\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 430,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.department\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 431,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 429,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-gray-500 text-sm\",\n                                                                        children: \"Schedule\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 434,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: (currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.schedule) || \"Not assigned\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 435,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 433,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                className: \"text-gray-500 text-sm\",\n                                                                children: \"Application Number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                className: \"font-mono mt-1\",\n                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.application_number\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"border border-amber-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>toggleSection(\"history\"),\n                                    className: \"w-full p-4 flex items-center justify-between text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-amber-100 p-2 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5 text-amber-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Entry/Exit History\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"View your campus access records\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 15\n                                        }, this),\n                                        activeSection === \"history\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 13\n                                }, this),\n                                activeSection === \"history\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"pt-0 px-4 pb-4\",\n                                    children: studentEntries.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-8 w-8 mx-auto text-gray-300 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 text-sm\",\n                                                children: \"No entries recorded yet\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: studentEntries.slice((currentPage - 1) * entriesPerPage, currentPage * entriesPerPage).map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-2 bg-gray-50 rounded-lg border-l-4 border-l-blue-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 rounded-full \".concat(entry.status === \"entry\" ? \"bg-green-500\" : \"bg-red-500\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 492,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm font-medium\",\n                                                                                        children: entry.status === \"entry\" ? \"Entry\" : \"Exit\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 495,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                        variant: \"outline\",\n                                                                                        className: \"text-xs px-1 py-0\",\n                                                                                        children: entry.verified ? \"✓\" : \"⚠\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 498,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 494,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: [\n                                                                                    formatDate(entry.entry_time || entry.entryTime || entry.timestamp),\n                                                                                    \" • \",\n                                                                                    formatTime(entry.entry_time || entry.entryTime || entry.timestamp)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 502,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 493,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 491,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-400\",\n                                                                    children: entry.status === \"entry\" ? \"In\" : \"Out\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 508,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 507,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, entry.id, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 27\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 21\n                                            }, this),\n                                            studentEntries.length > entriesPerPage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mt-4 pt-3 border-t\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            \"Showing \",\n                                                            (currentPage - 1) * entriesPerPage + 1,\n                                                            \"-\",\n                                                            Math.min(currentPage * entriesPerPage, studentEntries.length),\n                                                            \" of \",\n                                                            studentEntries.length\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>setCurrentPage((prev)=>Math.max(1, prev - 1)),\n                                                                disabled: currentPage === 1,\n                                                                className: \"h-7 w-7 p-0\",\n                                                                children: \"←\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 523,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>setCurrentPage((prev)=>Math.min(Math.ceil(studentEntries.length / entriesPerPage), prev + 1)),\n                                                                disabled: currentPage >= Math.ceil(studentEntries.length / entriesPerPage),\n                                                                className: \"h-7 w-7 p-0\",\n                                                                children: \"→\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-3 pt-3 border-t bg-blue-50 rounded-lg p-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: [\n                                                                \"Total Entries: \",\n                                                                studentEntries.length\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: [\n                                                                \"Last Updated: \",\n                                                                new Date().toLocaleTimeString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 549,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"border border-blue-100\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            className: \"pb-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-5 w-5 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 565,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"How to Use Your Digital ID Card\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                lineNumber: 564,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 563,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-50 p-4 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-blue-700 mb-2 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 573,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"QR Code Scanning\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                    className: \"list-decimal list-inside space-y-1 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Show your QR code to station operator\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 577,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Operator will scan with the camera\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Hold QR code steady in front of camera\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 579,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"System retrieves your details automatically\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Proceed to face verification\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 581,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 576,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-50 p-4 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-green-700 mb-2 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 586,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Manual Input Option\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 585,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"list-disc list-inside space-y-1 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Copy your Application Number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: 'Go to station\\'s \"Manual Entry\" section'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 591,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Paste your Application Number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 592,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: 'Click \"Validate\" to retrieve details'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 593,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Continue with face verification\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 594,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 584,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 570,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.Alert, {\n                                    className: \"mt-4 bg-yellow-50 border-yellow-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.AlertDescription, {\n                                        className: \"text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold\",\n                                                children: \"Important:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" Your digital ID card is for official use only. Do not share it with unauthorized persons. Report lost cards immediately.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 599,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 569,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                    lineNumber: 562,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n            lineNumber: 215,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n        lineNumber: 214,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentApp, \"oWzI3S3hIeSS/z40AxYZOhRxnqU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = StudentApp;\nvar _c;\n$RefreshReg$(_c, \"StudentApp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/student/page.tsx\n"));

/***/ })

});