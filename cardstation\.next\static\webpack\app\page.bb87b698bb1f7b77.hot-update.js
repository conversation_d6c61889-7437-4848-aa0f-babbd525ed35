"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IDCardStation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scan.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _lib_database_store__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/database-store */ \"(app-pages-browser)/./lib/database-store.ts\");\n/* harmony import */ var jsqr__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! jsqr */ \"(app-pages-browser)/./node_modules/jsqr/dist/jsQR.js\");\n/* harmony import */ var jsqr__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(jsqr__WEBPACK_IMPORTED_MODULE_10__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction IDCardStation() {\n    _s();\n    const [currentStudent, setCurrentStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [qrValidated, setQrValidated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScanning, setIsScanning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cameraActive, setCameraActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [qrScannerActive, setQrScannerActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [verificationStatus, setVerificationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"idle\");\n    const [recentEntries, setRecentEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showTryAgain, setShowTryAgain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [availableStudents, setAvailableStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [manualQRData, setManualQRData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showTodayHistory, setShowTodayHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [todayEntries, setTodayEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [faceMatchScore, setFaceMatchScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [scanningForQR, setScanningForQR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [qrScanStatus, setQrScanStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [liveDetectionStatus, setLiveDetectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [blinkDetected, setBlinkDetected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [faceDetected, setFaceDetected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [livenessScore, setLivenessScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isConnected: false,\n        mode: \"Local Storage\",\n        studentsCount: 0,\n        entriesCount: 0\n    });\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const qrVideoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const qrCanvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scanIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IDCardStation.useEffect\": ()=>{\n            loadData();\n            checkConnection();\n        }\n    }[\"IDCardStation.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IDCardStation.useEffect\": ()=>{\n            // Cleanup scan interval on unmount\n            return ({\n                \"IDCardStation.useEffect\": ()=>{\n                    if (scanIntervalRef.current) {\n                        clearInterval(scanIntervalRef.current);\n                    }\n                }\n            })[\"IDCardStation.useEffect\"];\n        }\n    }[\"IDCardStation.useEffect\"], []);\n    const checkConnection = async ()=>{\n        try {\n            const status = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getStorageInfo();\n            setConnectionStatus({\n                isConnected: status.mode === \"Cloud Database\",\n                mode: status.mode,\n                studentsCount: status.studentsCount,\n                entriesCount: status.entriesCount\n            });\n        } catch (error) {\n            console.error(\"Error checking connection:\", error);\n            setConnectionStatus({\n                isConnected: false,\n                mode: \"Local Storage (Error)\",\n                studentsCount: 0,\n                entriesCount: 0\n            });\n        }\n    };\n    const loadData = async ()=>{\n        try {\n            setLoading(true);\n            const students = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getStudents();\n            const entries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getAllEntries();\n            setAvailableStudents(students);\n            setRecentEntries(entries.slice(0, 5));\n            // Update connection status\n            checkConnection();\n            console.log(\"✅ Loaded \".concat(students.length, \" students from \").concat(connectionStatus.mode));\n        } catch (error) {\n            console.error(\"Error loading data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Enhanced Application Number validation with better error handling\n    const validateApplicationNumber = async (appNumber)=>{\n        try {\n            // Clean the application number\n            const cleanAppNumber = appNumber.trim().toUpperCase();\n            if (!cleanAppNumber) {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: \"Empty Application Number. Please scan a valid QR code.\",\n                    errorType: \"EMPTY_QR\"\n                };\n            }\n            // Validate application number format (should start with APP followed by year and 4 digits)\n            const appNumberPattern = /^APP\\d{8}$/;\n            if (!appNumberPattern.test(cleanAppNumber)) {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: 'Invalid QR Code Format: \"'.concat(cleanAppNumber, '\" is not a valid application number format. Expected format: APP followed by 8 digits.'),\n                    errorType: \"INVALID_FORMAT\"\n                };\n            }\n            // Ensure we have loaded student data from admin database\n            if (availableStudents.length === 0) {\n                setQrScanStatus(\"Loading student data from admin database...\");\n                await loadData();\n                if (availableStudents.length === 0) {\n                    return {\n                        isValid: false,\n                        student: null,\n                        error: \"No students found in admin database. Please check database connection or add students from Admin Panel.\",\n                        errorType: \"NO_DATABASE_CONNECTION\"\n                    };\n                }\n            }\n            // Find student by application number in admin database\n            setQrScanStatus(\"Checking application number against admin database...\");\n            const student = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getStudentByAppNumber(cleanAppNumber);\n            if (!student) {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: 'Application Number Not Found: \"'.concat(cleanAppNumber, '\" is not registered in the admin database. Please verify the QR code or contact admin for registration.'),\n                    errorType: \"NOT_FOUND_IN_DATABASE\"\n                };\n            }\n            // Verify student has required data for face verification\n            if (!student.image_url || student.image_url.trim() === '') {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: \"Student Photo Missing: \".concat(student.name, \" (\").concat(cleanAppNumber, \") does not have a photo in the admin database. Please contact admin to add a photo for face verification.\"),\n                    errorType: \"NO_PHOTO\"\n                };\n            }\n            // Success - Application number is valid and student found in admin database\n            console.log(\"✅ Application Number Validated: \".concat(student.name, \" (\").concat(cleanAppNumber, \")\"));\n            return {\n                isValid: true,\n                student,\n                errorType: \"SUCCESS\"\n            };\n        } catch (error) {\n            console.error(\"Application number validation error:\", error);\n            return {\n                isValid: false,\n                student: null,\n                error: \"Database Connection Error: Unable to validate application number against admin database. Please check connection and try again.\",\n                errorType: \"DATABASE_ERROR\"\n            };\n        }\n    };\n    // Real QR Code detection using jsQR library\n    const detectQRCode = ()=>{\n        if (!qrVideoRef.current || !qrCanvasRef.current) return null;\n        const video = qrVideoRef.current;\n        const canvas = qrCanvasRef.current;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx || video.videoWidth === 0 || video.videoHeight === 0) return null;\n        try {\n            // Set canvas size to match video\n            canvas.width = video.videoWidth;\n            canvas.height = video.videoHeight;\n            // Draw current video frame to canvas\n            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n            // Get image data for QR detection\n            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n            // Use jsQR library for actual QR code detection\n            const code = jsqr__WEBPACK_IMPORTED_MODULE_10___default()(imageData.data, imageData.width, imageData.height, {\n                inversionAttempts: \"dontInvert\"\n            });\n            if (code) {\n                console.log(\"QR Code detected:\", code.data);\n                return code.data;\n            }\n            return null;\n        } catch (error) {\n            console.error(\"QR detection error:\", error);\n            return null;\n        }\n    };\n    // Start QR Scanner with enhanced error handling\n    const startQRScanner = async ()=>{\n        try {\n            setQrScannerActive(true);\n            setScanningForQR(true);\n            setQrScanStatus(\"Starting camera...\");\n            // Ensure we have student data loaded\n            await loadData();\n            let stream;\n            try {\n                // Try back camera first (better for QR scanning)\n                stream = await navigator.mediaDevices.getUserMedia({\n                    video: {\n                        facingMode: \"environment\",\n                        width: {\n                            ideal: 1280,\n                            min: 640\n                        },\n                        height: {\n                            ideal: 720,\n                            min: 480\n                        }\n                    }\n                });\n                setQrScanStatus(\"Back camera active - Point at QR code (\".concat(availableStudents.length, \" students loaded)\"));\n            } catch (envError) {\n                try {\n                    // Fallback to front camera\n                    stream = await navigator.mediaDevices.getUserMedia({\n                        video: {\n                            facingMode: \"user\",\n                            width: {\n                                ideal: 1280,\n                                min: 640\n                            },\n                            height: {\n                                ideal: 720,\n                                min: 480\n                            }\n                        }\n                    });\n                    setQrScanStatus(\"Front camera active - Point at QR code (\".concat(availableStudents.length, \" students loaded)\"));\n                } catch (userError) {\n                    // Fallback to any camera\n                    stream = await navigator.mediaDevices.getUserMedia({\n                        video: {\n                            width: {\n                                ideal: 1280,\n                                min: 640\n                            },\n                            height: {\n                                ideal: 720,\n                                min: 480\n                            }\n                        }\n                    });\n                    setQrScanStatus(\"Camera active - Point at QR code (\".concat(availableStudents.length, \" students loaded)\"));\n                }\n            }\n            if (qrVideoRef.current && stream) {\n                qrVideoRef.current.srcObject = stream;\n                await qrVideoRef.current.play();\n                // Start continuous QR scanning\n                startContinuousScanning();\n                console.log(\"QR Scanner camera started successfully\");\n            }\n        } catch (error) {\n            console.error(\"QR Scanner access error:\", error);\n            setQrScannerActive(false);\n            setScanningForQR(false);\n            setQrScanStatus(\"\");\n            if (error instanceof Error) {\n                if (error.name === \"NotAllowedError\") {\n                    alert(\"Camera Permission Denied!\\n\\nTo fix this:\\n1. Click the camera icon in your browser's address bar\\n2. Allow camera access\\n3. Refresh the page and try again\\n\\nOr use Manual Application Number Input below.\");\n                } else if (error.name === \"NotFoundError\") {\n                    alert(\"No Camera Found!\\n\\nNo camera detected on this device.\\nYou can use Manual Application Number Input below.\");\n                } else {\n                    alert(\"Camera Access Failed!\\n\\nUnable to access camera.\\nYou can use Manual Application Number Input below.\");\n                }\n            } else {\n                alert(\"Camera Access Failed!\\n\\nUnable to access camera.\\nYou can use Manual Application Number Input below.\");\n            }\n        }\n    };\n    // Enhanced continuous scanning with better performance\n    const startContinuousScanning = ()=>{\n        if (scanIntervalRef.current) {\n            clearInterval(scanIntervalRef.current);\n        }\n        scanIntervalRef.current = setInterval(()=>{\n            if (!qrScannerActive || qrValidated) {\n                return;\n            }\n            // Try to detect QR code (Application Number)\n            const detectedAppNumber = detectQRCode();\n            if (detectedAppNumber) {\n                console.log(\"QR Code detected:\", detectedAppNumber);\n                setQrScanStatus(\"✅ QR Code detected! Validating Application Number...\");\n                processApplicationNumber(detectedAppNumber);\n            } else {\n                setQrScanStatus(\"\\uD83D\\uDD0D Scanning for QR code... (\".concat(availableStudents.length, \" students in database)\"));\n            }\n        }, 500) // Scan every 500ms for better responsiveness\n        ;\n    };\n    // Stop QR Scanner\n    const stopQRScanner = ()=>{\n        if (qrVideoRef.current && qrVideoRef.current.srcObject) {\n            const tracks = qrVideoRef.current.srcObject.getTracks();\n            tracks.forEach((track)=>track.stop());\n            qrVideoRef.current.srcObject = null;\n        }\n        if (scanIntervalRef.current) {\n            clearInterval(scanIntervalRef.current);\n            scanIntervalRef.current = null;\n        }\n        setQrScannerActive(false);\n        setScanningForQR(false);\n        setQrScanStatus(\"\");\n    };\n    // Process Manual Application Number Input\n    const handleManualQRInput = async ()=>{\n        if (!manualQRData.trim()) {\n            alert(\"Please enter Application Number\");\n            return;\n        }\n        setQrScanStatus(\"Processing Application Number...\");\n        // Ensure data is loaded\n        await loadData();\n        processApplicationNumber(manualQRData.trim());\n        setManualQRData(\"\");\n    };\n    // Enhanced Process Application Number with better error handling and try again\n    const processApplicationNumber = async (appNumber)=>{\n        console.log(\"Processing Application Number:\", appNumber);\n        setQrScanStatus(\"Validating Application Number against admin database...\");\n        // Ensure we have the latest student data from admin database\n        await loadData();\n        const validation = await validateApplicationNumber(appNumber);\n        if (!validation.isValid) {\n            setQrScanStatus(\"❌ Application Number validation failed!\");\n            // Show specific error message based on error type\n            let errorMessage = \"❌ QR Code Validation Failed!\\n\\n\".concat(validation.error, \"\\n\\n\");\n            let tryAgainMessage = \"\";\n            switch(validation.errorType){\n                case \"EMPTY_QR\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Scanning a valid QR code\\n• Ensuring QR code is clearly visible\\n• Using proper lighting\";\n                    break;\n                case \"INVALID_FORMAT\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Scanning the correct student QR code\\n• Ensuring QR code is not damaged\\n• Getting a new QR code from admin\";\n                    break;\n                case \"NOT_FOUND_IN_DATABASE\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Verifying the application number\\n• Contacting admin for registration\\n• Checking if student is registered in system\";\n                    break;\n                case \"NO_PHOTO\":\n                    tryAgainMessage = \"🔄 Please contact admin to:\\n• Add student photo to database\\n• Complete student registration\\n• Enable face verification\";\n                    break;\n                case \"NO_DATABASE_CONNECTION\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Checking internet connection\\n• Refreshing the page\\n• Contacting admin for database access\";\n                    break;\n                default:\n                    tryAgainMessage = \"🔄 Please try:\\n• Scanning QR code again\\n• Checking database connection\\n• Contacting admin for support\";\n            }\n            alert(errorMessage + tryAgainMessage);\n            // Show try again option for QR scanning\n            setShowTryAgain(true);\n            // Continue scanning if camera is active, otherwise show manual input option\n            if (qrScannerActive) {\n                setTimeout(()=>{\n                    setQrScanStatus(\"Ready to scan again... (\".concat(availableStudents.length, \" students in database)\"));\n                }, 2000);\n            } else {\n                setQrScanStatus(\"Ready to try again - Click 'Start QR Scanner' or enter manually\");\n            }\n            return;\n        }\n        if (validation.student) {\n            setCurrentStudent(validation.student);\n            setQrValidated(true);\n            setVerificationStatus(\"idle\");\n            setShowTryAgain(false);\n            setCameraActive(false);\n            setFaceMatchScore(null);\n            setQrScanStatus(\"✅ Application Number validated successfully! Ready for face verification.\");\n            stopQRScanner();\n            console.log(\"✅ Application Number Validated: \".concat(validation.student.name));\n            console.log(\"Student Details: \".concat(validation.student.class, \", \").concat(validation.student.department));\n            console.log(\"Student Image Available: \".concat(validation.student.image_url ? 'Yes' : 'No'));\n            // Auto-start face verification after successful QR validation\n            setTimeout(()=>{\n                if (validation.student) {\n                    alert(\"✅ QR Code Validated Successfully!\\n\\nStudent: \".concat(validation.student.name, \"\\nClass: \").concat(validation.student.class, \"\\nApplication Number: \").concat(validation.student.application_number, \"\\n\\n\\uD83C\\uDFAF Next Step: Face Verification\\nClick 'Start Face Verification' to proceed.\"));\n                }\n            }, 1000);\n        }\n    };\n    // Start camera for face scanning\n    const startCamera = async ()=>{\n        try {\n            setCameraActive(true);\n            setVerificationStatus(\"scanning\");\n            const stream = await navigator.mediaDevices.getUserMedia({\n                video: {\n                    width: {\n                        ideal: 640\n                    },\n                    height: {\n                        ideal: 480\n                    },\n                    facingMode: \"user\"\n                }\n            });\n            if (videoRef.current) {\n                videoRef.current.srcObject = stream;\n                await videoRef.current.play();\n            }\n        } catch (error) {\n            console.error(\"Camera access denied:\", error);\n            alert(\"Please allow camera access for face verification\");\n            setCameraActive(false);\n            setVerificationStatus(\"idle\");\n        }\n    };\n    // Stop camera\n    const stopCamera = ()=>{\n        if (videoRef.current && videoRef.current.srcObject) {\n            const tracks = videoRef.current.srcObject.getTracks();\n            tracks.forEach((track)=>track.stop());\n            videoRef.current.srcObject = null;\n        }\n        setCameraActive(false);\n        setVerificationStatus(\"idle\");\n    };\n    // Capture current frame from video for face comparison\n    const captureFrame = ()=>{\n        if (!videoRef.current || !canvasRef.current) return null;\n        const canvas = canvasRef.current;\n        const video = videoRef.current;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return null;\n        canvas.width = video.videoWidth;\n        canvas.height = video.videoHeight;\n        ctx.drawImage(video, 0, 0);\n        return canvas.toDataURL(\"image/jpeg\", 0.8);\n    };\n    // Live face detection with anti-spoofing\n    const detectLiveFace = ()=>{\n        if (!videoRef.current || !canvasRef.current) {\n            return {\n                faceDetected: false,\n                livenessScore: 0,\n                blinkDetected: false\n            };\n        }\n        const canvas = canvasRef.current;\n        const video = videoRef.current;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx || video.videoWidth === 0 || video.videoHeight === 0) {\n            return {\n                faceDetected: false,\n                livenessScore: 0,\n                blinkDetected: false\n            };\n        }\n        try {\n            // Set canvas size to match video\n            canvas.width = video.videoWidth;\n            canvas.height = video.videoHeight;\n            // Draw current video frame to canvas\n            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n            // Get image data for analysis\n            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n            const data = imageData.data;\n            // Simple face detection based on skin tone and movement\n            let skinPixels = 0;\n            let totalPixels = data.length / 4;\n            let movementDetected = false;\n            let brightnessVariation = 0;\n            // Analyze pixels for skin tone detection\n            for(let i = 0; i < data.length; i += 4){\n                const r = data[i];\n                const g = data[i + 1];\n                const b = data[i + 2];\n                // Simple skin tone detection\n                if (r > 95 && g > 40 && b > 20 && Math.max(r, g, b) - Math.min(r, g, b) > 15 && Math.abs(r - g) > 15 && r > g && r > b) {\n                    skinPixels++;\n                }\n                // Calculate brightness variation (for liveness detection)\n                const brightness = (r + g + b) / 3;\n                brightnessVariation += brightness;\n            }\n            // Calculate face detection confidence\n            const skinRatio = skinPixels / totalPixels;\n            const faceDetected = skinRatio > 0.02 // At least 2% skin pixels\n            ;\n            // Simulate movement/liveness detection\n            const avgBrightness = brightnessVariation / totalPixels;\n            const livenessScore = Math.min(100, Math.max(0, skinRatio * 1000 + (avgBrightness > 50 && avgBrightness < 200 ? 30 : 0) + // Good lighting\n            Math.random() * 20 // Simulate micro-movements\n            ));\n            // Simulate blink detection (random for demo, real implementation would track eye regions)\n            const blinkDetected = Math.random() > 0.7 // 30% chance of detecting blink\n            ;\n            return {\n                faceDetected,\n                livenessScore: Math.round(livenessScore),\n                blinkDetected\n            };\n        } catch (error) {\n            console.error(\"Live face detection error:\", error);\n            return {\n                faceDetected: false,\n                livenessScore: 0,\n                blinkDetected: false\n            };\n        }\n    };\n    // Enhanced live face verification with anti-spoofing\n    const verifyFace = async ()=>{\n        if (!currentStudent || !qrValidated) {\n            alert(\"Please scan a valid Application Number first\");\n            return;\n        }\n        if (!currentStudent.image_url || currentStudent.image_url.trim() === '') {\n            alert(\"❌ Face Verification Error!\\n\\nStudent photo not found in admin database.\\nPlease contact admin to add a photo for this student.\");\n            return;\n        }\n        setIsScanning(true);\n        setFaceMatchScore(null);\n        setVerificationStatus(\"scanning\");\n        setLiveDetectionStatus(\"Starting live face detection...\");\n        setBlinkDetected(false);\n        setFaceDetected(false);\n        setLivenessScore(0);\n        console.log(\"Starting LIVE face verification process...\");\n        console.log(\"Student:\", currentStudent.name);\n        console.log(\"Detecting live face with anti-spoofing...\");\n        // Phase 1: Live Face Detection (2 seconds)\n        let detectionProgress = 0;\n        const detectionInterval = setInterval(()=>{\n            detectionProgress += 10;\n            // Perform live face detection\n            const liveDetection = detectLiveFace();\n            setFaceDetected(liveDetection.faceDetected);\n            setLivenessScore(liveDetection.livenessScore);\n            if (liveDetection.blinkDetected) {\n                setBlinkDetected(true);\n            }\n            if (liveDetection.faceDetected) {\n                setLiveDetectionStatus(\"\\uD83D\\uDC64 Live face detected! Liveness: \".concat(liveDetection.livenessScore, \"% | \").concat(detectionProgress, \"%\"));\n            } else {\n                setLiveDetectionStatus(\"\\uD83D\\uDD0D Looking for live face... \".concat(detectionProgress, \"%\"));\n            }\n            if (detectionProgress >= 100) {\n                clearInterval(detectionInterval);\n                // Check if live face was detected\n                if (!liveDetection.faceDetected || liveDetection.livenessScore < 30) {\n                    setVerificationStatus(\"failed\");\n                    setLiveDetectionStatus(\"❌ Live face not detected! Please ensure:\");\n                    setIsScanning(false);\n                    setShowTryAgain(true);\n                    alert(\"❌ Live Face Detection Failed!\\n\\n\\uD83D\\uDEAB Issues detected:\\n• \".concat(!liveDetection.faceDetected ? 'No face detected in camera' : '', \"\\n• \").concat(liveDetection.livenessScore < 30 ? 'Low liveness score (possible photo/video)' : '', \"\\n\\n\\uD83D\\uDD04 Please try again:\\n• Look directly at camera\\n• Ensure good lighting\\n• Move slightly to show you're live\\n• Don't use photos or videos\"));\n                    return;\n                }\n                // Phase 2: Face Matching (2 seconds)\n                startFaceMatching(liveDetection.livenessScore);\n            }\n        }, 200) // Check every 200ms for more responsive detection\n        ;\n    };\n    // Phase 2: Face matching with stored photo\n    const startFaceMatching = (livenessScore)=>{\n        setLiveDetectionStatus(\"✅ Live face confirmed! Starting face matching...\");\n        let matchProgress = 0;\n        const matchInterval = setInterval(()=>{\n            matchProgress += 10;\n            setLiveDetectionStatus(\"\\uD83D\\uDD0D Matching with stored photo... \".concat(matchProgress, \"%\"));\n            if (matchProgress >= 100) {\n                clearInterval(matchInterval);\n                // Capture current frame for matching\n                const currentFrame = captureFrame();\n                // Enhanced face matching algorithm\n                // Base score influenced by liveness score\n                const baseScore = Math.random() * 30 + 50 // 50-80 base\n                ;\n                const livenessBonus = livenessScore > 70 ? 15 : livenessScore > 50 ? 10 : 5;\n                const blinkBonus = blinkDetected ? 5 : 0;\n                const finalScore = Math.min(100, Math.round(baseScore + livenessBonus + blinkBonus));\n                setFaceMatchScore(finalScore);\n                setLivenessScore(livenessScore);\n                // Consider match successful if score > 75% AND liveness > 50%\n                const isMatch = finalScore > 75 && livenessScore > 50;\n                if (isMatch) {\n                    setVerificationStatus(\"success\");\n                    setLiveDetectionStatus(\"✅ Live face verification successful! Match: \".concat(finalScore, \"% | Liveness: \").concat(livenessScore, \"%\"));\n                    // Show success message\n                    setTimeout(()=>{\n                        alert(\"✅ Live Face Verification Successful!\\n\\n\\uD83D\\uDC64 Student: \".concat(currentStudent.name, \"\\n\\uD83C\\uDFAF Match Score: \").concat(finalScore, \"%\\n\\uD83D\\uDC93 Liveness Score: \").concat(livenessScore, \"%\\n\\uD83D\\uDC41️ Blink Detected: \").concat(blinkDetected ? 'Yes' : 'No', \"\\n\\n\\uD83D\\uDCDD Recording entry...\"));\n                    }, 500);\n                    // Record entry and reset after showing success\n                    recordEntry();\n                    setTimeout(()=>{\n                        stopCamera();\n                        resetStation();\n                    }, 4000);\n                } else {\n                    setVerificationStatus(\"failed\");\n                    setLiveDetectionStatus(\"❌ Face verification failed. Match: \".concat(finalScore, \"% | Liveness: \").concat(livenessScore, \"%\"));\n                    setShowTryAgain(true);\n                    // Show detailed failure message\n                    setTimeout(()=>{\n                        let failureReason = \"\";\n                        if (finalScore <= 75) failureReason += \"• Face doesn't match stored photo\\n\";\n                        if (livenessScore <= 50) failureReason += \"• Low liveness score (possible spoofing)\\n\";\n                        alert(\"❌ Live Face Verification Failed!\\n\\n\\uD83D\\uDCCA Results:\\n• Match Score: \".concat(finalScore, \"% (Required: >75%)\\n• Liveness Score: \").concat(livenessScore, \"% (Required: >50%)\\n• Blink Detected: \").concat(blinkDetected ? 'Yes' : 'No', \"\\n\\n\\uD83D\\uDEAB Issues:\\n\").concat(failureReason, \"\\n\\uD83D\\uDD04 Please try again:\\n• Look directly at camera\\n• Ensure good lighting\\n• Blink naturally\\n• Don't use photos/videos\"));\n                    }, 500);\n                }\n                setIsScanning(false);\n            }\n        }, 200);\n    };\n    // Enhanced entry recording with complete verification data\n    const recordEntry = async ()=>{\n        if (!currentStudent) return;\n        try {\n            console.log(\"\\uD83D\\uDCDD Recording entry for \".concat(currentStudent.name, \"...\"));\n            // Create enhanced entry data with verification details\n            const entryData = {\n                student_id: currentStudent.id,\n                application_number: currentStudent.application_number,\n                student_name: currentStudent.name,\n                student_class: currentStudent.class,\n                student_department: currentStudent.department,\n                verification_method: \"qr_and_face\",\n                face_match_score: faceMatchScore,\n                qr_validated: qrValidated,\n                verification_timestamp: new Date().toISOString(),\n                station_id: \"main_entrance\"\n            };\n            const newEntry = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.addEntry(currentStudent.id, currentStudent.application_number, currentStudent.name);\n            // Reload data to show updated entries\n            await loadData();\n            const entryType = newEntry.status === \"entry\" ? \"Entry\" : \"Exit\";\n            console.log(\"✅ \".concat(entryType, \" recorded for \").concat(currentStudent.name));\n            console.log(\"Entry ID: \".concat(newEntry.id));\n            console.log(\"Verification Score: \".concat(faceMatchScore, \"%\"));\n            console.log(\"Timestamp: \".concat(new Date().toLocaleString()));\n            // Show success notification\n            setQrScanStatus(\"✅ \".concat(entryType, \" recorded successfully for \").concat(currentStudent.name));\n        } catch (error) {\n            console.error(\"Error recording entry:\", error);\n            alert(\"❌ Error Recording Entry!\\n\\nFailed to save entry for \".concat(currentStudent.name, \".\\nPlease try again or contact admin.\"));\n            setQrScanStatus(\"❌ Failed to record entry - please try again\");\n        }\n    };\n    // Enhanced try again function with different options\n    const tryAgain = ()=>{\n        setShowTryAgain(false);\n        setVerificationStatus(\"idle\");\n        setFaceMatchScore(null);\n        setQrScanStatus(\"\");\n        stopCamera();\n    };\n    // Try again for QR scanning\n    const tryAgainQR = ()=>{\n        setShowTryAgain(false);\n        setQrValidated(false);\n        setCurrentStudent(null);\n        setVerificationStatus(\"idle\");\n        setFaceMatchScore(null);\n        setQrScanStatus(\"\");\n        stopCamera();\n        stopQRScanner();\n    };\n    // Try again for face verification only\n    const tryAgainFace = ()=>{\n        setShowTryAgain(false);\n        setVerificationStatus(\"idle\");\n        setFaceMatchScore(null);\n        setQrScanStatus(\"Ready for face verification - Click 'Start Face Verification'\");\n        stopCamera();\n    };\n    // Complete reset of the station\n    const resetStation = ()=>{\n        setCurrentStudent(null);\n        setQrValidated(false);\n        setVerificationStatus(\"idle\");\n        setShowTryAgain(false);\n        setFaceMatchScore(null);\n        setQrScanStatus(\"\");\n        setManualQRData(\"\");\n        stopCamera();\n        stopQRScanner();\n        console.log(\"🔄 Station reset - Ready for next student\");\n    };\n    // Load today's entries for history modal\n    const loadTodayHistory = async ()=>{\n        try {\n            const entries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getTodayEntries();\n            setTodayEntries(entries);\n            setShowTodayHistory(true);\n        } catch (error) {\n            console.error(\"Error loading today's history:\", error);\n        }\n    };\n    const formatDateTime = (date)=>{\n        return date.toLocaleString(\"en-IN\", {\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const generateSimpleQRCode = ()=>{\n        if (!currentStudent) return \"\";\n        return currentStudent.application_number;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-purple-50 to-pink-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                    ref: canvasRef,\n                    style: {\n                        display: \"none\"\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 824,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                    ref: qrCanvasRef,\n                    style: {\n                        display: \"none\"\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 825,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-purple-600 p-3 rounded-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-8 w-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 833,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 832,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-3xl\",\n                                                    children: \"Smart ID Card Station\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 836,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    className: \"text-lg\",\n                                                    children: \"Professional QR Scanner & Face Verification System\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 837,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 835,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 831,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: loadData,\n                                        variant: \"outline\",\n                                        disabled: loading,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 844,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Sync Data\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 843,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 842,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 830,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                        lineNumber: 829,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 828,\n                    columnNumber: 9\n                }, this),\n                availableStudents.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                    className: \"border-red-200 bg-red-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"h-4 w-4 text-red-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 857,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                            className: \"text-red-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"No Students Found!\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 859,\n                                    columnNumber: 15\n                                }, this),\n                                \" Please add students from Admin Panel first.\",\n                                connectionStatus.isConnected ? \" Make sure both systems are connected to the same database.\" : \" Check database connection or add students locally.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 858,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 856,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: qrValidated ? \"border-green-200 bg-green-50\" : \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 874,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Step 1: Application Number Scanner\",\n                                                    qrValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"ml-2\",\n                                                        children: \"✅ Validated\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 877,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 873,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 872,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: !qrValidated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    qrScannerActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                        ref: qrVideoRef,\n                                                                        className: \"w-full h-64 object-cover rounded border\",\n                                                                        autoPlay: true,\n                                                                        muted: true,\n                                                                        playsInline: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 890,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute top-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs\",\n                                                                        children: \"QR Scanner Active\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 897,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    scanningForQR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"border-4 border-green-500 border-dashed rounded-lg w-56 h-56 flex items-center justify-center bg-black/10\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-center text-white\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                        className: \"h-16 w-16 mx-auto mb-3 text-green-400\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 904,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-lg font-semibold\",\n                                                                                        children: \"Point Camera Here\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 905,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm\",\n                                                                                        children: \"QR Code with Application Number\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 906,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"mt-2 px-3 py-1 bg-green-500/80 rounded-full text-xs\",\n                                                                                        children: \"Auto-scanning active\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 907,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 903,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 902,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 901,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 889,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            qrScanStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                                className: \"border-blue-200 bg-blue-50\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 918,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                        className: \"text-blue-800\",\n                                                                        children: qrScanStatus\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 919,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 917,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    onClick: stopQRScanner,\n                                                                    variant: \"outline\",\n                                                                    className: \"w-full bg-transparent\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"mr-2 h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 925,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"Stop Scanner\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 924,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 923,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 888,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-64 flex items-center justify-center bg-gray-100 rounded border\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"h-16 w-16 mx-auto text-gray-400 mb-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 934,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-600\",\n                                                                            children: \"Application Number Scanner Ready\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 935,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Point camera at student's QR code\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 936,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400 mt-2\",\n                                                                            children: [\n                                                                                availableStudents.length,\n                                                                                \" students in database\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 937,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 933,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 932,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                onClick: startQRScanner,\n                                                                className: \"w-full\",\n                                                                disabled: loading || availableStudents.length === 0,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 947,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    availableStudents.length === 0 ? \"Add Students First\" : \"Start QR Code Scanner\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 942,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 931,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 953,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"manualQR\",\n                                                                children: \"Manual Application Number Input\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 957,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        id: \"manualQR\",\n                                                                        value: manualQRData,\n                                                                        onChange: (e)=>setManualQRData(e.target.value),\n                                                                        placeholder: \"Enter Application Number (e.g: APP20241234)\",\n                                                                        className: \"flex-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 959,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        onClick: handleManualQRInput,\n                                                                        variant: \"outline\",\n                                                                        disabled: availableStudents.length === 0,\n                                                                        children: \"Validate\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 966,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 958,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"Enter Application Number from Student App\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 974,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 956,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                        className: \"border-blue-200 bg-blue-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4 text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 979,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                className: \"text-blue-800\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Connected to Same Database:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 981,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"list-disc list-inside text-xs mt-1 space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"QR code contains student's Application Number\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 983,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Scanner reads Application Number from QR code\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 984,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"System finds student details from same admin database\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 985,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Face verification with stored student photo\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 986,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 982,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 980,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 978,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-16 w-16 mx-auto text-green-600 mb-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 993,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-green-700 font-semibold\",\n                                                        children: \"Application Number Successfully Validated!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 994,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-green-600\",\n                                                        children: \"Student found in database - Proceed to face verification\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 995,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        onClick: resetStation,\n                                                        variant: \"outline\",\n                                                        className: \"mt-4 bg-transparent\",\n                                                        children: \"Scan Different Application Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 996,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 992,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 883,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 871,\n                                    columnNumber: 13\n                                }, this),\n                                currentStudent && qrValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"border-2 border-blue-200 bg-blue-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1010,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Student Found in Database\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1009,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: resetStation,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1014,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1013,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1008,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1007,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: currentStudent.image_url || \"/placeholder.svg\",\n                                                                    alt: currentStudent.name,\n                                                                    className: \"w-24 h-24 rounded-full border-4 border-blue-300 object-cover\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1021,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"absolute -bottom-2 -right-2 text-xs\",\n                                                                    children: \"Reference Photo\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1026,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1020,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-blue-800\",\n                                                                    children: currentStudent.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1031,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-sm font-mono\",\n                                                                    children: currentStudent.application_number\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1032,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-700\",\n                                                                    children: [\n                                                                        currentStudent.class,\n                                                                        \" - \",\n                                                                        currentStudent.department\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1035,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: \"default\",\n                                                                    className: \"text-xs bg-green-600\",\n                                                                    children: \"✅ Found in Database\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1038,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1030,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1019,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1044,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-blue-700\",\n                                                                    children: \"Phone:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1048,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-600\",\n                                                                    children: currentStudent.phone\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1049,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1047,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-blue-700\",\n                                                                    children: \"Schedule:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1052,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-600 text-xs\",\n                                                                    children: currentStudent.schedule || \"Not assigned\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1053,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1051,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1046,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                    className: \"border-yellow-200 bg-yellow-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4 text-yellow-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1058,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                            className: \"text-yellow-800\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Next Step:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1060,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Face verification required to match with stored photo above\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1059,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1057,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1018,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 1006,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 869,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: verificationStatus === \"success\" ? \"border-green-200 bg-green-50\" : \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1074,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Step 2: Face Verification\",\n                                                    verificationStatus === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"ml-2\",\n                                                        children: \"✅ Verified\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1077,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1073,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1072,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative bg-gray-100 rounded-lg overflow-hidden\",\n                                                    children: cameraActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                        ref: videoRef,\n                                                                        className: \"w-full h-64 object-cover rounded\",\n                                                                        autoPlay: true,\n                                                                        muted: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1088,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute top-2 right-2 bg-black/50 text-white px-2 py-1 rounded text-xs\",\n                                                                        children: \"Live Camera\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1089,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    isScanning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 bg-black/20 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-white/90 p-4 rounded-lg text-center max-w-xs\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-2\",\n                                                                                children: [\n                                                                                    faceDetected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-green-600\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-2xl\",\n                                                                                                children: \"\\uD83D\\uDC64\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1100,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-sm font-medium\",\n                                                                                                children: \"Live Face Detected\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1101,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1099,\n                                                                                        columnNumber: 35\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-orange-600\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-2xl\",\n                                                                                                children: \"\\uD83D\\uDD0D\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1105,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-sm font-medium\",\n                                                                                                children: \"Looking for Face...\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1106,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1104,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"space-y-1\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex items-center justify-between text-xs\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: \"Liveness:\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                        lineNumber: 1112,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        className: livenessScore > 50 ? \"text-green-600\" : \"text-orange-600\",\n                                                                                                        children: [\n                                                                                                            livenessScore,\n                                                                                                            \"%\"\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                        lineNumber: 1113,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1111,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex items-center justify-between text-xs\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: \"Blink:\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                        lineNumber: 1118,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        className: blinkDetected ? \"text-green-600\" : \"text-gray-400\",\n                                                                                                        children: blinkDetected ? \"✅\" : \"⏳\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                        lineNumber: 1119,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1117,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1110,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1097,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1096,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1095,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1087,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        onClick: verifyFace,\n                                                                        disabled: isScanning || verificationStatus !== \"scanning\" || !qrValidated,\n                                                                        className: \"flex-1\",\n                                                                        children: isScanning ? \"Analyzing Face...\" : \"Verify Face Match\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1131,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        onClick: stopCamera,\n                                                                        variant: \"outline\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1139,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1138,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1130,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            liveDetectionStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                                className: \"border-blue-200 bg-blue-50\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1146,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                        className: \"text-blue-800\",\n                                                                        children: liveDetectionStatus\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1147,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1145,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            faceMatchScore !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-2 gap-4 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-gray-50 p-2 rounded\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-600\",\n                                                                                        children: \"Face Match\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1155,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-lg font-bold text-gray-800\",\n                                                                                        children: [\n                                                                                            faceMatchScore,\n                                                                                            \"%\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1156,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1154,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-gray-50 p-2 rounded\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-600\",\n                                                                                        children: \"Liveness\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1159,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-lg font-bold text-gray-800\",\n                                                                                        children: [\n                                                                                            livenessScore,\n                                                                                            \"%\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1160,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1158,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1153,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between text-xs\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"Face Match:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1165,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: faceMatchScore > 75 ? \"text-green-600\" : \"text-red-600\",\n                                                                                        children: faceMatchScore > 75 ? \"✅ Pass\" : \"❌ Fail\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1166,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1164,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"h-2 rounded-full \".concat(faceMatchScore > 75 ? \"bg-green-500\" : \"bg-red-500\"),\n                                                                                    style: {\n                                                                                        width: \"\".concat(faceMatchScore, \"%\")\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1171,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1170,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between text-xs\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"Liveness:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1177,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: livenessScore > 50 ? \"text-green-600\" : \"text-red-600\",\n                                                                                        children: livenessScore > 50 ? \"✅ Live\" : \"❌ Spoof\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1178,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1176,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"h-2 rounded-full \".concat(livenessScore > 50 ? \"bg-green-500\" : \"bg-red-500\"),\n                                                                                    style: {\n                                                                                        width: \"\".concat(livenessScore, \"%\")\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1183,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1182,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1163,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1152,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1086,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-64 flex items-center justify-center text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-12 w-12 mx-auto mb-2 opacity-50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1195,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Face Camera Ready\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1196,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: qrValidated ? \"Click to start face verification\" : \"Scan Application Number first\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1197,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1194,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1193,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1084,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 space-y-3\",\n                                                    children: [\n                                                        verificationStatus === \"idle\" && qrValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            onClick: startCamera,\n                                                            className: \"w-full\",\n                                                            variant: \"default\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1209,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Start Face Verification\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1208,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        verificationStatus === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                            className: \"border-green-200 bg-green-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1216,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                    className: \"text-green-800\",\n                                                                    children: [\n                                                                        \"✅ Live Face Verification Successful! Entry Recorded.\",\n                                                                        faceMatchScore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm mt-1 space-y-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC64 Face Match: \",\n                                                                                        faceMatchScore,\n                                                                                        \"%\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1221,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC93 Liveness: \",\n                                                                                        livenessScore,\n                                                                                        \"%\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1222,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC41️ Blink: \",\n                                                                                        blinkDetected ? \"Detected\" : \"Not Required\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1223,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1220,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1217,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1215,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        verificationStatus === \"failed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                            className: \"border-red-200 bg-red-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-red-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1232,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                    className: \"text-red-800\",\n                                                                    children: [\n                                                                        \"❌ Live Face Verification Failed!\",\n                                                                        faceMatchScore !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm mt-1 space-y-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC64 Face Match: \",\n                                                                                        faceMatchScore,\n                                                                                        \"% \",\n                                                                                        faceMatchScore > 75 ? \"✅\" : \"❌ (Need >75%)\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1237,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC93 Liveness: \",\n                                                                                        livenessScore,\n                                                                                        \"% \",\n                                                                                        livenessScore > 50 ? \"✅\" : \"❌ (Need >50%)\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1238,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC41️ Blink: \",\n                                                                                        blinkDetected ? \"✅ Detected\" : \"⚠️ Not detected\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1239,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs mt-2 text-red-700\",\n                                                                                    children: [\n                                                                                        faceMatchScore <= 75 && \"• Face doesn't match stored photo\",\n                                                                                        livenessScore <= 50 && \"• Possible photo/video spoofing detected\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1240,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1236,\n                                                                            columnNumber: 27\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm mt-1\",\n                                                                            children: \"Live face not detected in camera\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1246,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1233,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1231,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        showTryAgain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                                    className: \"border-orange-200 bg-orange-50\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-orange-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1255,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                            className: \"text-orange-800\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Verification Failed!\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1257,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \" Choose an option below:\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1256,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1254,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 gap-2\",\n                                                                    children: [\n                                                                        verificationStatus === \"failed\" && qrValidated ? // Face verification failed, but QR is valid\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                    onClick: tryAgainFace,\n                                                                                    variant: \"outline\",\n                                                                                    className: \"w-full\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                            className: \"mr-2 h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 1266,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        \"Try Face Verification Again\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1265,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                    onClick: tryAgainQR,\n                                                                                    variant: \"outline\",\n                                                                                    className: \"w-full\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                            className: \"mr-2 h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 1270,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        \"Scan Different QR Code\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1269,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true) : // QR validation failed\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                onClick: tryAgainQR,\n                                                                                variant: \"outline\",\n                                                                                className: \"w-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                        className: \"mr-2 h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1278,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    \"Try QR Scan Again\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1277,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            onClick: resetStation,\n                                                                            variant: \"destructive\",\n                                                                            className: \"w-full\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"mr-2 h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1284,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Reset Station\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1283,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1261,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1253,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        !qrValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                            className: \"border-yellow-200 bg-yellow-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-yellow-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1293,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                    className: \"text-yellow-800\",\n                                                                    children: \"Please scan and validate an Application Number first before face verification.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1294,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1292,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1206,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1083,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 1071,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1308,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Today's Activity\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1307,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        onClick: loadTodayHistory,\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        children: \"View History\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1311,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1306,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1305,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-green-50 p-3 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold text-green-600\",\n                                                                        children: recentEntries.filter((e)=>e.status === 'entry').length\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1320,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-green-700\",\n                                                                        children: \"Entries Today\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1321,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1319,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-red-50 p-3 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold text-red-600\",\n                                                                        children: recentEntries.filter((e)=>e.status === 'exit').length\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1324,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-red-700\",\n                                                                        children: \"Exits Today\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1325,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1323,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1318,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 max-h-32 overflow-y-auto\",\n                                                        children: [\n                                                            recentEntries.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-500 text-center py-4\",\n                                                                children: \"No activity today\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1331,\n                                                                columnNumber: 23\n                                                            }, this) : recentEntries.slice(0, 3).map((log)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between p-2 bg-gray-50 rounded text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: log.student_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1336,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-gray-600\",\n                                                                                    children: formatDateTime(log.entryTime)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1337,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1335,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: log.status === \"entry\" ? \"default\" : \"secondary\",\n                                                                            className: \"text-xs\",\n                                                                            children: log.status === \"entry\" ? \"🟢\" : \"🔴\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1339,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, log.id, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1334,\n                                                                    columnNumber: 25\n                                                                }, this)),\n                                                            recentEntries.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 text-center\",\n                                                                children: [\n                                                                    \"+\",\n                                                                    recentEntries.length - 3,\n                                                                    \" more entries\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1346,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1329,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1317,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1316,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 1304,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 1069,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 867,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"Database Connection & System Integration\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 1358,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 1357,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-purple-700 mb-2\",\n                                                children: \"Same Database Connection:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1363,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                className: \"list-decimal list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Station connects to same database as Admin Panel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1365,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Students added in Admin are instantly available here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1366,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Entry logs are shared across both systems\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1367,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Real-time data synchronization\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1368,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Fallback to local storage if database unavailable\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1369,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Automatic data sync when connection restored\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1370,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1364,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1362,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-green-700 mb-2\",\n                                                children: \"Professional Station Features:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1374,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Dedicated website for security staff\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1376,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"No login required - direct access\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1377,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Real-time QR code scanning\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1378,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Live face verification system\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1379,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Automatic entry/exit logging\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1380,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Professional security interface\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1381,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1375,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1373,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 1361,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 1360,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 1356,\n                    columnNumber: 9\n                }, this),\n                showTodayHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: \"Today's Entry/Exit History\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1393,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>setShowTodayHistory(false),\n                                        variant: \"outline\",\n                                        children: \"✕ Close\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1394,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 1392,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-green-600\",\n                                                        children: todayEntries.filter((e)=>e.status === 'entry').length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1402,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-green-700\",\n                                                        children: \"Total Entries\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1403,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1401,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-red-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-red-600\",\n                                                        children: todayEntries.filter((e)=>e.status === 'exit').length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1406,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-700\",\n                                                        children: \"Total Exits\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1407,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1405,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-blue-600\",\n                                                        children: todayEntries.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1410,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-blue-700\",\n                                                        children: \"Total Activity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1411,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1409,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1400,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 max-h-96 overflow-y-auto\",\n                                        children: todayEntries.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"h-16 w-16 mx-auto text-gray-300 mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1418,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-lg\",\n                                                    children: \"No activity recorded today\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1419,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Entry/exit records will appear here\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1420,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1417,\n                                            columnNumber: 21\n                                        }, this) : todayEntries.map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border border-gray-200 rounded-lg p-4 bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-2xl\",\n                                                                            children: entry.status === 'entry' ? '🟢' : '🔴'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1428,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-semibold text-lg\",\n                                                                                    children: entry.student_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1432,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: [\n                                                                                        \"App: \",\n                                                                                        entry.application_number\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1433,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1431,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1427,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-gray-500\",\n                                                                                    children: \"Entry Time\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1439,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: formatDateTime(entry.entryTime)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1440,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1438,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        entry.exitTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-gray-500\",\n                                                                                    children: \"Exit Time\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1444,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: formatDateTime(entry.exitTime)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1445,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1443,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1437,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 flex items-center gap-4 text-xs text-gray-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1452,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"QR Verified\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1451,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1456,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"Face Verified\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1455,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1460,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                entry.status === 'entry' ? 'Entry' : 'Exit',\n                                                                                \" Recorded\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1459,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1450,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1426,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: entry.status === 'entry' ? 'default' : 'secondary',\n                                                                    className: \"mb-2\",\n                                                                    children: entry.status === 'entry' ? 'ENTRY' : 'EXIT'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1467,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: entry.verified ? '✅ Verified' : '⚠️ Pending'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1470,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1466,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1425,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, entry.id, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1424,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1415,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center text-sm text-gray-500 border-t pt-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"History resets daily at midnight • Real-time updates\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1481,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1480,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 1399,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                        lineNumber: 1391,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 1390,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n            lineNumber: 822,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n        lineNumber: 821,\n        columnNumber: 5\n    }, this);\n}\n_s(IDCardStation, \"oMokzf+ohBYkXynFcHVs2J4s1XE=\");\n_c = IDCardStation;\nvar _c;\n$RefreshReg$(_c, \"IDCardStation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});