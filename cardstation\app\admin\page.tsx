"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  UserPlus,
  Users,
  Activity,
  Copy,
  Check,
  Edit,
  Trash2,
  Save,
  X,
  LogOut,
  RefreshCw,
  Database,
  Upload,
  ImageIcon,
  Camera,
  Home,
  Calendar,
} from "lucide-react"
import { dbStore, type Student } from "@/lib/database-store"
import { supabase } from "@/lib/supabase"
import Link from "next/link"

export default function AdminPanel() {
  const [students, setStudents] = useState<Student[]>([])
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingStudent, setEditingStudent] = useState<Student | null>(null)
  const [copiedText, setCopiedText] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [databaseConnected, setDatabaseConnected] = useState(false)
  const [storageInfo, setStorageInfo] = useState({ mode: "Local", studentsCount: 0, entriesCount: 0 })
  const [stats, setStats] = useState({
    totalStudents: 0,
    todayEntries: 0,
    todayExits: 0,
    totalEntries: 0,
  })
  const [showEntriesModal, setShowEntriesModal] = useState(false)
  const [showCalendarHistory, setShowCalendarHistory] = useState(false)
  const [selectedDate, setSelectedDate] = useState<string | null>(null)
  const [selectedDateEntries, setSelectedDateEntries] = useState<any[]>([])
  const [currentMonth, setCurrentMonth] = useState(new Date())
  const [allEntries, setAllEntries] = useState<any[]>([])
  const [todayEntries, setTodayEntries] = useState<any[]>([])
  const [newStudent, setNewStudent] = useState({
    name: "",
    phone: "",
    email: "",
    class: "",
    department: "",
    schedule: "",
    image: "", // Will store base64 image
  })
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [imageFile, setImageFile] = useState<File | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const router = useRouter()

  useEffect(() => {
    // Check if admin is logged in
    if (typeof window !== "undefined") {
      const adminLoggedIn = localStorage.getItem("adminLoggedIn")
      if (!adminLoggedIn) {
        router.push("/")
        return
      }
    }

    setIsAuthenticated(true)
    checkDatabaseConnection()
    loadData()

    // Auto-refresh enabled for admin panel to show real-time entry data
    const interval = setInterval(() => {
      loadData()
      console.log("🔄 Admin Panel: Auto-refreshing entry data...")
    }, 3000) // Refresh every 3 seconds for faster updates

    return () => clearInterval(interval)
  }, [router])

  const checkDatabaseConnection = () => {
    const connected = supabase !== null
    setDatabaseConnected(connected)
    setStorageInfo(dbStore.getStorageInfo())
  }

  const loadData = async () => {
    try {
      setLoading(true)
      console.log("🔄 Admin Panel: Loading data...")

      // Load students with fallback
      let studentsData = []
      try {
        studentsData = await dbStore.getStudents()
        console.log("📊 Students loaded:", studentsData.length)
      } catch (error) {
        console.warn("⚠️ Failed to load students:", error)
        studentsData = []
      }

      // Load today's entries with fallback
      let todayEntriesData = []
      try {
        todayEntriesData = await dbStore.getTodayEntries()
        console.log("📊 Today's entries loaded:", todayEntriesData.length)
      } catch (error) {
        console.warn("⚠️ Failed to load today's entries:", error)
        todayEntriesData = []
      }

      // Load all entries with fallback
      let allEntriesData = []
      try {
        allEntriesData = await dbStore.getAllEntries()
        console.log("📊 All entries loaded:", allEntriesData.length)
      } catch (error) {
        console.warn("⚠️ Failed to load all entries:", error)
        allEntriesData = []
      }

      setStudents(studentsData)
      setTodayEntries(todayEntriesData)
      setAllEntries(allEntriesData)
      setStats({
        totalStudents: studentsData.length,
        todayEntries: todayEntriesData.filter((entry: any) => entry.status === 'entry').length,
        todayExits: todayEntriesData.filter((entry: any) => entry.status === 'exit').length,
        totalEntries: allEntriesData.length,
      })
      setStorageInfo(dbStore.getStorageInfo())

      console.log("✅ Admin Panel: Data loaded successfully")
      console.log("📊 Final stats:", {
        students: studentsData.length,
        todayEntries: todayEntriesData.length,
        totalEntries: allEntriesData.length
      })
    } catch (error) {
      console.error("❌ Error loading data:", error)
      // Set default values on error
      setStudents([])
      setTodayEntries([])
      setAllEntries([])
      setStats({
        totalStudents: 0,
        todayEntries: 0,
        totalEntries: 0,
      })
    } finally {
      setLoading(false)
    }
  }

  const handleRefresh = () => {
    loadData()
  }

  const handleLogout = () => {
    if (typeof window !== "undefined") {
      localStorage.removeItem("adminLoggedIn")
      localStorage.removeItem("adminUsername")
    }
    router.push("/")
  }

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const handleViewEntries = () => {
    setShowEntriesModal(true)
  }

  const handleViewCalendar = () => {
    setShowCalendarHistory(true)
  }

  const handleDateClick = async (date: string) => {
    try {
      setSelectedDate(date)
      // Get entries for selected date
      const dateEntries = allEntries.filter((entry: any) => {
        const entryDate = new Date(entry.entryTime).toDateString()
        const selectedDateObj = new Date(date).toDateString()
        return entryDate === selectedDateObj
      })
      setSelectedDateEntries(dateEntries)
    } catch (error) {
      console.error("Error loading date entries:", error)
      setSelectedDateEntries([])
    }
  }

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear()
    const month = date.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const daysInMonth = lastDay.getDate()
    const startingDayOfWeek = firstDay.getDay()

    const days = []

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null)
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(day)
    }

    return days
  }

  const getEntriesForDate = (day: number) => {
    if (!day) return 0
    const date = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day)
    return allEntries.filter((entry: any) => {
      const entryDate = new Date(entry.entryTime)
      return entryDate.toDateString() === date.toDateString()
    }).length
  }

  // Handle image file selection
  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith("image/")) {
      alert("Please select a valid image file (JPG, PNG, GIF, etc.)")
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert("Image size should be less than 5MB")
      return
    }

    setImageFile(file)

    // Create preview
    const reader = new FileReader()
    reader.onload = (e) => {
      const result = e.target?.result as string
      setImagePreview(result)
      setNewStudent({ ...newStudent, image: result })
    }
    reader.readAsDataURL(file)
  }

  // Remove selected image
  const removeImage = () => {
    setImageFile(null)
    setImagePreview(null)
    setNewStudent({ ...newStudent, image: "" })
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  // Take photo using camera
  const takePhoto = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true })

      // Create a video element to capture the stream
      const video = document.createElement("video")
      video.srcObject = stream
      video.autoplay = true

      // Create a modal or popup to show camera feed
      const modal = document.createElement("div")
      modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
      `

      const container = document.createElement("div")
      container.style.cssText = `
        background: white;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
      `

      const canvas = document.createElement("canvas")
      const captureBtn = document.createElement("button")
      captureBtn.textContent = "Capture Photo"
      captureBtn.style.cssText = `
        background: #3b82f6;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        margin: 10px;
        cursor: pointer;
      `

      const cancelBtn = document.createElement("button")
      cancelBtn.textContent = "Cancel"
      cancelBtn.style.cssText = `
        background: #6b7280;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        margin: 10px;
        cursor: pointer;
      `

      container.appendChild(video)
      container.appendChild(document.createElement("br"))
      container.appendChild(captureBtn)
      container.appendChild(cancelBtn)
      modal.appendChild(container)
      document.body.appendChild(modal)

      // Capture photo
      captureBtn.onclick = () => {
        canvas.width = video.videoWidth
        canvas.height = video.videoHeight
        const ctx = canvas.getContext("2d")
        ctx?.drawImage(video, 0, 0)

        const imageData = canvas.toDataURL("image/jpeg", 0.8)
        setImagePreview(imageData)
        setNewStudent({ ...newStudent, image: imageData })

        // Stop camera and close modal
        stream.getTracks().forEach((track) => track.stop())
        document.body.removeChild(modal)
      }

      // Cancel
      cancelBtn.onclick = () => {
        stream.getTracks().forEach((track) => track.stop())
        document.body.removeChild(modal)
      }
    } catch (error) {
      alert("Camera access denied or not available")
    }
  }

  const validateForm = () => {
    if (!newStudent.name.trim()) {
      alert("Student name is required")
      return false
    }
    if (!newStudent.phone.trim()) {
      alert("Phone number is required")
      return false
    }
    if (newStudent.phone.length !== 10 || !/^\d+$/.test(newStudent.phone)) {
      alert("Phone number must be exactly 10 digits")
      return false
    }
    if (!newStudent.class) {
      alert("Class selection is required")
      return false
    }
    if (!newStudent.image) {
      alert("Student photo is required. Please upload an image or take a photo.")
      return false
    }
    return true
  }

  const handleAddStudent = async () => {
    if (!validateForm()) return

    // Check if phone number already exists
    const existingStudent = students.find((s) => s.phone === newStudent.phone)
    if (existingStudent) {
      alert("Phone number already exists!")
      return
    }

    setLoading(true)
    try {
      const applicationNumber = dbStore.generateApplicationNumber()
      const student = await dbStore.addStudent({
        ...newStudent,
        application_number: applicationNumber,
        image_url: newStudent.image, // Store base64 image
      })

      await loadData()
      resetForm()

      alert(
        `Student Added Successfully!\n\nName: ${student.name}\nApplication Number: ${applicationNumber}\nPhone: ${student.phone}\n\nPlease provide Application Number and Phone Number to the student for login.\n\nData saved in ${storageInfo.mode} storage.`,
      )
    } catch (error) {
      alert("Error adding student. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  const handleEditStudent = (student: Student) => {
    setEditingStudent(student)
    setNewStudent({
      name: student.name,
      phone: student.phone,
      email: student.email || "",
      class: student.class,
      department: student.department || "",
      schedule: student.schedule || "",
      image: student.image_url || "",
    })
    setImagePreview(student.image_url || null)
    setShowAddForm(false)
  }

  const handleUpdateStudent = async () => {
    if (!validateForm() || !editingStudent) return

    // Check if phone number already exists (excluding current student)
    const existingStudent = students.find((s) => s.phone === newStudent.phone && s.id !== editingStudent.id)
    if (existingStudent) {
      alert("Phone number already exists!")
      return
    }

    setLoading(true)
    try {
      await dbStore.updateStudent(editingStudent.id, {
        name: newStudent.name,
        phone: newStudent.phone,
        email: newStudent.email || null,
        class: newStudent.class,
        department: newStudent.department || null,
        schedule: newStudent.schedule || null,
        image_url: newStudent.image,
      })

      await loadData()
      resetForm()
      alert(`Student updated successfully!\n\nData saved in ${storageInfo.mode} storage.`)
    } catch (error) {
      alert("Error updating student. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteStudent = async (student: Student) => {
    if (confirm(`Are you sure you want to delete ${student.name}?\n\nThis action cannot be undone.`)) {
      try {
        setLoading(true)
        await dbStore.deleteStudent(student.id)
        await loadData()
        alert(`Student deleted successfully!\n\nData updated in ${storageInfo.mode} storage.`)
      } catch (error) {
        alert("Error deleting student. Please try again.")
      } finally {
        setLoading(false)
      }
    }
  }

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedText(`${type}-${text}`)
      setTimeout(() => setCopiedText(null), 2000)
    } catch (error) {
      alert("Failed to copy to clipboard")
    }
  }

  const resetForm = () => {
    setNewStudent({
      name: "",
      phone: "",
      email: "",
      class: "",
      department: "",
      schedule: "",
      image: "",
    })
    setImagePreview(null)
    setImageFile(null)
    setShowAddForm(false)
    setEditingStudent(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  if (!isAuthenticated) {
    return <div>Loading...</div>
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-3xl">Admin Panel</CardTitle>
                <CardDescription className="text-lg">
                  Student Management System - {storageInfo.mode} Storage
                </CardDescription>
              </div>
              <div className="flex gap-2">
                <div className="flex items-center gap-2 px-3 py-2 bg-green-50 rounded-lg border border-green-200">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-xs text-green-700 font-medium">Live Updates (3s)</span>
                </div>
                <Button onClick={() => setShowCalendarHistory(true)} variant="outline">
                  <Calendar className="mr-2 h-4 w-4" />
                  History
                </Button>
                <Button onClick={handleRefresh} variant="outline" disabled={loading}>
                  <RefreshCw className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`} />
                  Refresh
                </Button>
                <Button onClick={handleLogout} variant="outline">
                  <LogOut className="mr-2 h-4 w-4" />
                  Logout
                </Button>
                <Link href="/">
                  <Button variant="outline" size="lg">
                    <Home className="mr-2 h-4 w-4" />
                    Home
                  </Button>
                </Link>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Storage Status Alert */}
        <Alert className={databaseConnected ? "border-green-200 bg-green-50" : "border-yellow-200 bg-yellow-50"}>
          <Database className={`h-4 w-4 ${databaseConnected ? "text-green-600" : "text-yellow-600"}`} />
          <AlertDescription className={databaseConnected ? "text-green-800" : "text-yellow-800"}>
            <strong>{storageInfo.mode} Storage Active:</strong>{" "}
            {databaseConnected
              ? "Data syncs across all devices automatically"
              : `Data saved locally on this device (${storageInfo.studentsCount} students, ${storageInfo.entriesCount} entries)`}
          </AlertDescription>
        </Alert>

        {/* Stats - 4 Cards Layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Total Students */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Users className="h-8 w-8 text-blue-600" />
                <div>
                  <p className="text-2xl font-bold">{stats.totalStudents}</p>
                  <p className="text-sm text-gray-600">Total Students</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Today's Entries */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Activity className="h-8 w-8 text-green-600" />
                <div>
                  <p className="text-2xl font-bold">{stats.todayEntries}</p>
                  <p className="text-sm text-gray-600">Today's Entries</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Today's Exits */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <X className="h-8 w-8 text-red-600" />
                <div>
                  <p className="text-2xl font-bold">{stats.todayExits}</p>
                  <p className="text-sm text-gray-600">Today's Exits</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* History Calendar */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-8 w-8 text-purple-600" />
                  <div>
                    <p className="text-2xl font-bold">{new Date().getDate()}</p>
                    <p className="text-sm text-gray-600">History Calendar</p>
                  </div>
                </div>
                <Button onClick={handleViewCalendar} variant="outline" size="sm">
                  View Calendar
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Add Student Button */}
        {!showAddForm && !editingStudent && (
          <Card>
            <CardContent className="p-6">
              <Button onClick={() => setShowAddForm(true)} className="w-full h-16 text-lg" disabled={loading}>
                <UserPlus className="mr-2 h-6 w-6" />
                Add New Student
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Add/Edit Student Form */}
        {(showAddForm || editingStudent) && (
          <Card>
            <CardHeader>
              <CardTitle>{editingStudent ? "Edit Student" : "Add New Student"}</CardTitle>
              <CardDescription>
                {editingStudent ? "Update student information" : "Fill required fields to register a new student"} -
                Data will be saved in {storageInfo.mode} storage
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Student Photo Upload Section */}
              <div className="space-y-4">
                <Label className="text-base font-semibold">Student Photo *</Label>

                {/* Image Preview */}
                {imagePreview ? (
                  <div className="flex items-center space-x-4">
                    <div className="relative">
                      <img
                        src={imagePreview || "/placeholder.svg"}
                        alt="Student preview"
                        className="w-32 h-32 rounded-full border-4 border-blue-200 object-cover"
                      />
                      <Button
                        onClick={removeImage}
                        size="sm"
                        variant="destructive"
                        className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                    <div className="space-y-2">
                      <p className="text-sm text-green-600">✅ Photo uploaded successfully</p>
                      <Button onClick={() => fileInputRef.current?.click()} variant="outline" size="sm">
                        <Upload className="mr-2 h-4 w-4" />
                        Change Photo
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                    <ImageIcon className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                    <p className="text-gray-600 mb-4">Upload student photo (Required)</p>
                    <div className="flex justify-center space-x-4">
                      <Button onClick={() => fileInputRef.current?.click()} variant="outline">
                        <Upload className="mr-2 h-4 w-4" />
                        Upload Photo
                      </Button>
                      <Button onClick={takePhoto} variant="outline">
                        <Camera className="mr-2 h-4 w-4" />
                        Take Photo
                      </Button>
                    </div>
                    <p className="text-xs text-gray-500 mt-2">Supported formats: JPG, PNG, GIF (Max 5MB)</p>
                  </div>
                )}

                {/* Hidden file input */}
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleImageSelect}
                  className="hidden"
                />
              </div>

              <Separator />

              {/* Student Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Student Name *</Label>
                  <Input
                    id="name"
                    value={newStudent.name}
                    onChange={(e) => setNewStudent({ ...newStudent, name: e.target.value })}
                    placeholder="Enter full name"
                    disabled={loading}
                  />
                </div>
                <div>
                  <Label htmlFor="phone">Phone Number *</Label>
                  <Input
                    id="phone"
                    value={newStudent.phone}
                    onChange={(e) => setNewStudent({ ...newStudent, phone: e.target.value })}
                    placeholder="10-digit phone number"
                    maxLength={10}
                    disabled={loading}
                  />
                </div>
                <div>
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    value={newStudent.email}
                    onChange={(e) => setNewStudent({ ...newStudent, email: e.target.value })}
                    placeholder="<EMAIL>"
                    disabled={loading}
                  />
                </div>
                <div>
                  <Label htmlFor="class">Class *</Label>
                  <Select
                    value={newStudent.class}
                    onValueChange={(value) => setNewStudent({ ...newStudent, class: value })}
                    disabled={loading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select class" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10th-A">10th A</SelectItem>
                      <SelectItem value="10th-B">10th B</SelectItem>
                      <SelectItem value="10th-C">10th C</SelectItem>
                      <SelectItem value="11th-A">11th A</SelectItem>
                      <SelectItem value="11th-B">11th B</SelectItem>
                      <SelectItem value="11th-C">11th C</SelectItem>
                      <SelectItem value="12th-A">12th A</SelectItem>
                      <SelectItem value="12th-B">12th B</SelectItem>
                      <SelectItem value="12th-C">12th C</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="department">Department</Label>
                  <Select
                    value={newStudent.department}
                    onValueChange={(value) => setNewStudent({ ...newStudent, department: value })}
                    disabled={loading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select department" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Science">Science</SelectItem>
                      <SelectItem value="Commerce">Commerce</SelectItem>
                      <SelectItem value="Arts">Arts</SelectItem>
                      <SelectItem value="Computer Science">Computer Science</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="schedule">Time Schedule</Label>
                  <Select
                    value={newStudent.schedule}
                    onValueChange={(value) => setNewStudent({ ...newStudent, schedule: value })}
                    disabled={loading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select schedule" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Morning Shift (8:00 AM - 2:00 PM)">
                        Morning Shift (8:00 AM - 2:00 PM)
                      </SelectItem>
                      <SelectItem value="Afternoon Shift (2:00 PM - 8:00 PM)">
                        Afternoon Shift (2:00 PM - 8:00 PM)
                      </SelectItem>
                      <SelectItem value="Full Day (8:00 AM - 4:00 PM)">Full Day (8:00 AM - 4:00 PM)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Separator />

              <div className="flex gap-2">
                {editingStudent ? (
                  <Button onClick={handleUpdateStudent} className="flex-1" disabled={loading}>
                    <Save className="mr-2 h-4 w-4" />
                    {loading ? "Updating..." : "Update Student"}
                  </Button>
                ) : (
                  <Button onClick={handleAddStudent} className="flex-1" disabled={loading}>
                    <UserPlus className="mr-2 h-4 w-4" />
                    {loading ? "Adding..." : "Add Student"}
                  </Button>
                )}
                <Button onClick={resetForm} variant="outline" className="flex-1 bg-transparent" disabled={loading}>
                  <X className="mr-2 h-4 w-4" />
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Students List */}
        <Card>
          <CardHeader>
            <CardTitle>Registered Students ({students.length})</CardTitle>
            <CardDescription>
              All registered students with their login credentials - Stored in {storageInfo.mode} storage
            </CardDescription>
          </CardHeader>
          <CardContent>
            {students.length === 0 ? (
              <div className="text-center py-12">
                <Users className="h-16 w-16 mx-auto text-gray-300 mb-4" />
                <p className="text-xl text-gray-500 mb-2">No students registered yet</p>
                <p className="text-gray-400">Click "Add New Student" to get started</p>
              </div>
            ) : (
              <div className="space-y-4">
                {students.map((student) => (
                  <div key={student.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border">
                    <div className="flex items-center space-x-4">
                      <img
                        src={student.image_url || "/placeholder.svg?height=60&width=60"}
                        alt={student.name}
                        className="w-12 h-12 rounded-full border-2 border-gray-200 object-cover"
                      />
                      <div>
                        <h3 className="font-semibold text-lg">{student.name}</h3>
                        <p className="text-sm text-gray-600">
                          {student.class} {student.department && `- ${student.department}`}
                        </p>
                        <p className="text-sm text-gray-500">{student.phone}</p>
                        {student.email && <p className="text-xs text-gray-400">{student.email}</p>}
                      </div>
                    </div>

                    <div className="text-right space-y-2">
                      {/* Login Credentials */}
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="font-mono text-xs">
                            App: {student.application_number}
                          </Badge>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => copyToClipboard(student.application_number, "app")}
                            className="h-6 w-6 p-0"
                          >
                            {copiedText === `app-${student.application_number}` ? (
                              <Check className="h-3 w-3 text-green-600" />
                            ) : (
                              <Copy className="h-3 w-3" />
                            )}
                          </Button>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary" className="text-xs">
                            Phone: {student.phone}
                          </Badge>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => copyToClipboard(student.phone, "phone")}
                            className="h-6 w-6 p-0"
                          >
                            {copiedText === `phone-${student.phone}` ? (
                              <Check className="h-3 w-3 text-green-600" />
                            ) : (
                              <Copy className="h-3 w-3" />
                            )}
                          </Button>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex gap-1">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEditStudent(student)}
                          disabled={loading}
                          className="h-8 w-8 p-0"
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleDeleteStudent(student)}
                          disabled={loading}
                          className="h-8 w-8 p-0"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Admin Instructions - {storageInfo.mode} Storage</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-blue-700 mb-2">Required Fields:</h3>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>✅ Student Name (Full name required)</li>
                  <li>✅ Phone Number (10 digits, unique)</li>
                  <li>✅ Class Selection (from dropdown)</li>
                  <li>✅ Student Photo (Upload or camera)</li>
                  <li>📝 Email (Optional)</li>
                  <li>📝 Department (Optional)</li>
                  <li>📝 Schedule (Optional)</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold text-green-700 mb-2">Photo Requirements:</h3>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>📸 Clear face photo required</li>
                  <li>📸 JPG, PNG, GIF formats supported</li>
                  <li>📸 Maximum file size: 5MB</li>
                  <li>📸 Upload from device or take with camera</li>
                  <li>📸 Used for face verification at station</li>
                  <li>📸 Can be changed during editing</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Entries Modal */}
        {showEntriesModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-2xl font-bold">Entry/Exit History</h2>
                <Button onClick={() => setShowEntriesModal(false)} variant="outline">
                  ✕ Close
                </Button>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-lg font-semibold text-green-600 mb-2">Today's Entries ({todayEntries.length})</h3>
                    <div className="space-y-2 max-h-60 overflow-y-auto">
                      {todayEntries.length === 0 ? (
                        <p className="text-gray-500 text-sm">No entries today</p>
                      ) : (
                        todayEntries.map((entry: any) => (
                          <div key={entry.id} className="border border-green-200 rounded p-3 bg-green-50">
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="font-medium">{entry.student_name}</p>
                                <p className="text-sm text-gray-600">App: {entry.application_number}</p>
                                <p className="text-sm text-gray-600">
                                  {entry.status === 'entry' ? '🟢 Entry' : '🔴 Exit'} - {formatDateTime(entry.entry_time)}
                                </p>
                                {entry.exit_time && (
                                  <p className="text-sm text-gray-600">Exit: {formatDateTime(entry.exit_time)}</p>
                                )}
                              </div>
                              <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                                {entry.verified ? '✅ Verified' : '⚠️ Pending'}
                              </span>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold text-blue-600 mb-2">All Entries ({allEntries.length})</h3>
                    <div className="space-y-2 max-h-60 overflow-y-auto">
                      {allEntries.length === 0 ? (
                        <p className="text-gray-500 text-sm">No entries found</p>
                      ) : (
                        allEntries.slice(0, 10).map((entry: any) => (
                          <div key={entry.id} className="border border-blue-200 rounded p-3 bg-blue-50">
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="font-medium">{entry.student_name}</p>
                                <p className="text-sm text-gray-600">App: {entry.application_number}</p>
                                <p className="text-sm text-gray-600">
                                  {entry.status === 'entry' ? '🟢 Entry' : '🔴 Exit'} - {formatDateTime(entry.entry_time)}
                                </p>
                                {entry.exit_time && (
                                  <p className="text-sm text-gray-600">Exit: {formatDateTime(entry.exit_time)}</p>
                                )}
                              </div>
                              <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                {entry.verified ? '✅ Verified' : '⚠️ Pending'}
                              </span>
                            </div>
                          </div>
                        ))
                      )}
                      {allEntries.length > 10 && (
                        <p className="text-sm text-gray-500 text-center">... and {allEntries.length - 10} more entries</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Calendar History Modal */}
        {showCalendarHistory && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[80vh] overflow-y-auto">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  📅 Entry/Exit History Calendar
                </h2>
                <Button onClick={() => setShowCalendarHistory(false)} variant="outline" size="sm">
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-4">
                {/* Today's Entries Section */}
                <div className="border rounded-lg p-4">
                  <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
                    🗓️ Today's Activity ({new Date().toLocaleDateString()})
                    <Badge variant="secondary">{todayEntries.length} entries</Badge>
                  </h3>
                  {todayEntries.length > 0 ? (
                    <div className="space-y-2">
                      {todayEntries.map((entry, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center gap-3">
                            <div className={`w-3 h-3 rounded-full ${entry.status === 'entry' ? 'bg-green-500' : 'bg-red-500'}`}></div>
                            <div>
                              <div className="font-medium">{entry.student_name}</div>
                              <div className="text-sm text-gray-600">App: {entry.application_number}</div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-medium">
                              {entry.status === 'entry' ? '🟢 ENTRY' : '🔴 EXIT'}
                            </div>
                            <div className="text-sm text-gray-600">
                              {new Date(entry.entry_time).toLocaleTimeString()}
                              {entry.exit_time && ` - ${new Date(entry.exit_time).toLocaleTimeString()}`}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      📭 No entries recorded today
                    </div>
                  )}
                </div>

                {/* All Entries Section */}
                <div className="border rounded-lg p-4">
                  <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
                    📊 Complete History
                    <Badge variant="secondary">{allEntries.length} total entries</Badge>
                  </h3>
                  {allEntries.length > 0 ? (
                    <div className="space-y-2 max-h-60 overflow-y-auto">
                      {allEntries.slice(0, 20).map((entry, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center gap-3">
                            <div className={`w-3 h-3 rounded-full ${entry.status === 'entry' ? 'bg-green-500' : 'bg-red-500'}`}></div>
                            <div>
                              <div className="font-medium">{entry.student_name}</div>
                              <div className="text-sm text-gray-600">App: {entry.application_number}</div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-medium">
                              {entry.status === 'entry' ? '🟢 ENTRY' : '🔴 EXIT'}
                            </div>
                            <div className="text-sm text-gray-600">
                              {new Date(entry.entry_time).toLocaleDateString()} {new Date(entry.entry_time).toLocaleTimeString()}
                              {entry.exit_time && (
                                <div>Exit: {new Date(entry.exit_time).toLocaleTimeString()}</div>
                              )}
                            </div>
                            {entry.face_match_score && (
                              <div className="text-xs text-blue-600">
                                Face: {entry.face_match_score}%
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                      {allEntries.length > 20 && (
                        <div className="text-center py-2 text-gray-500">
                          ...and {allEntries.length - 20} more entries
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      📭 No entries recorded yet
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Calendar History Modal */}
        {showCalendarHistory && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold flex items-center gap-2">
                    <Calendar className="h-6 w-6" />
                    Entry/Exit History Calendar
                  </h2>
                  <Button onClick={() => setShowCalendarHistory(false)} variant="outline">
                    ✕ Close
                  </Button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Calendar */}
                  <div>
                    <div className="flex items-center justify-between mb-4">
                      <Button
                        onClick={() => setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1))}
                        variant="outline"
                        size="sm"
                      >
                        ←
                      </Button>
                      <h3 className="text-lg font-semibold">
                        {currentMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                      </h3>
                      <Button
                        onClick={() => setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1))}
                        variant="outline"
                        size="sm"
                      >
                        →
                      </Button>
                    </div>

                    {/* Calendar Grid */}
                    <div className="grid grid-cols-7 gap-1 mb-2">
                      {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                        <div key={day} className="p-2 text-center text-sm font-medium text-gray-600">
                          {day}
                        </div>
                      ))}
                    </div>

                    <div className="grid grid-cols-7 gap-1">
                      {getDaysInMonth(currentMonth).map((day, index) => {
                        if (!day) {
                          return <div key={index} className="p-2"></div>
                        }

                        const entriesCount = getEntriesForDate(day)
                        const dateString = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day).toISOString().split('T')[0]
                        const isToday = new Date().toDateString() === new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day).toDateString()

                        return (
                          <button
                            key={day}
                            onClick={() => handleDateClick(dateString)}
                            className={`p-2 text-sm rounded hover:bg-blue-100 transition-colors ${
                              isToday ? 'bg-blue-500 text-white' :
                              entriesCount > 0 ? 'bg-green-100 text-green-800' : 'hover:bg-gray-100'
                            }`}
                          >
                            <div>{day}</div>
                            {entriesCount > 0 && (
                              <div className="text-xs mt-1">
                                {entriesCount} entries
                              </div>
                            )}
                          </button>
                        )
                      })}
                    </div>
                  </div>

                  {/* Selected Date Details */}
                  <div>
                    {selectedDate ? (
                      <div>
                        <h3 className="text-lg font-semibold mb-4">
                          📅 {new Date(selectedDate).toLocaleDateString('en-US', {
                            weekday: 'long',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          })}
                        </h3>

                        {selectedDateEntries.length > 0 ? (
                          <div className="space-y-3">
                            <div className="grid grid-cols-2 gap-4 mb-4">
                              <div className="bg-green-50 p-3 rounded-lg text-center">
                                <div className="text-2xl font-bold text-green-600">
                                  {selectedDateEntries.filter(e => e.status === 'entry').length}
                                </div>
                                <div className="text-sm text-green-700">Entries</div>
                              </div>
                              <div className="bg-red-50 p-3 rounded-lg text-center">
                                <div className="text-2xl font-bold text-red-600">
                                  {selectedDateEntries.filter(e => e.status === 'exit').length}
                                </div>
                                <div className="text-sm text-red-700">Exits</div>
                              </div>
                            </div>

                            <div className="max-h-60 overflow-y-auto space-y-2">
                              {selectedDateEntries.map((entry: any, index: number) => (
                                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                                  <div>
                                    <p className="font-medium">{entry.student_name}</p>
                                    <p className="text-sm text-gray-600">App: {entry.application_number}</p>
                                  </div>
                                  <div className="text-right">
                                    <Badge variant={entry.status === 'entry' ? 'default' : 'secondary'}>
                                      {entry.status === 'entry' ? '🟢 Entry' : '🔴 Exit'}
                                    </Badge>
                                    <p className="text-xs text-gray-500 mt-1">
                                      {new Date(entry.entryTime).toLocaleTimeString()}
                                    </p>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        ) : (
                          <div className="text-center py-8 text-gray-500">
                            📭 No entries recorded on this date
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        📅 Click on a date to view entry/exit history
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
