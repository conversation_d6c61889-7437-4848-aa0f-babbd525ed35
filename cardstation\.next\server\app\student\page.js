/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/student/page";
exports.ids = ["app/student/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4c5ceb8252e9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkQ6XFxpZGNhcmRcXGNhcmRzdGF0aW9uXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNGM1Y2ViODI1MmU5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: 'v0 App',\n    description: 'Created with v0',\n    generator: 'v0.dev'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDc0I7QUFFZixNQUFNQSxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFdBQVc7QUFDYixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdSO0lBQ0EscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO3NCQUFNSDs7Ozs7Ozs7Ozs7QUFHYiIsInNvdXJjZXMiOlsiRDpcXGlkY2FyZFxcY2FyZHN0YXRpb25cXGFwcFxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICd2MCBBcHAnLFxuICBkZXNjcmlwdGlvbjogJ0NyZWF0ZWQgd2l0aCB2MCcsXG4gIGdlbmVyYXRvcjogJ3YwLmRldicsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5PntjaGlsZHJlbn08L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiZ2VuZXJhdG9yIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/student/page.tsx":
/*!******************************!*\
  !*** ./app/student/page.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\idcard\\cardstation\\app\\student\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fstudent%2Fpage&page=%2Fstudent%2Fpage&appPaths=%2Fstudent%2Fpage&pagePath=private-next-app-dir%2Fstudent%2Fpage.tsx&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fstudent%2Fpage&page=%2Fstudent%2Fpage&appPaths=%2Fstudent%2Fpage&pagePath=private-next-app-dir%2Fstudent%2Fpage.tsx&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/student/page.tsx */ \"(rsc)/./app/student/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'student',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\idcard\\\\cardstation\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/student/page\",\n        pathname: \"/student\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fstudent%2Fpage&page=%2Fstudent%2Fpage&appPaths=%2Fstudent%2Fpage&pagePath=private-next-app-dir%2Fstudent%2Fpage.tsx&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Capp%5C%5Cstudent%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Capp%5C%5Cstudent%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/student/page.tsx */ \"(rsc)/./app/student/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNpZGNhcmQlNUMlNUNjYXJkc3RhdGlvbiU1QyU1Q2FwcCU1QyU1Q3N0dWRlbnQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0pBQW9GIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxpZGNhcmRcXFxcY2FyZHN0YXRpb25cXFxcYXBwXFxcXHN0dWRlbnRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Capp%5C%5Cstudent%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/student/page.tsx":
/*!******************************!*\
  !*** ./app/student/page.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentApp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(ssr)/./components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/alert */ \"(ssr)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCircle_Clock_Copy_Home_LogOut_QrCode_RefreshCw_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCircle,Clock,Copy,Home,LogOut,QrCode,RefreshCw,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCircle_Clock_Copy_Home_LogOut_QrCode_RefreshCw_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCircle,Clock,Copy,Home,LogOut,QrCode,RefreshCw,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCircle_Clock_Copy_Home_LogOut_QrCode_RefreshCw_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCircle,Clock,Copy,Home,LogOut,QrCode,RefreshCw,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCircle_Clock_Copy_Home_LogOut_QrCode_RefreshCw_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCircle,Clock,Copy,Home,LogOut,QrCode,RefreshCw,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCircle_Clock_Copy_Home_LogOut_QrCode_RefreshCw_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCircle,Clock,Copy,Home,LogOut,QrCode,RefreshCw,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCircle_Clock_Copy_Home_LogOut_QrCode_RefreshCw_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCircle,Clock,Copy,Home,LogOut,QrCode,RefreshCw,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCircle_Clock_Copy_Home_LogOut_QrCode_RefreshCw_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCircle,Clock,Copy,Home,LogOut,QrCode,RefreshCw,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCircle_Clock_Copy_Home_LogOut_QrCode_RefreshCw_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCircle,Clock,Copy,Home,LogOut,QrCode,RefreshCw,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCircle_Clock_Copy_Home_LogOut_QrCode_RefreshCw_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCircle,Clock,Copy,Home,LogOut,QrCode,RefreshCw,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CheckCircle_Clock_Copy_Home_LogOut_QrCode_RefreshCw_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CheckCircle,Clock,Copy,Home,LogOut,QrCode,RefreshCw,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _lib_database_store__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/database-store */ \"(ssr)/./lib/database-store.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./components/ui/label.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\nfunction StudentApp() {\n    const [currentStudent, setCurrentStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [studentEntries, setStudentEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showQRCard, setShowQRCard] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [copiedQR, setCopiedQR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentApp.useEffect\": ()=>{\n            // Check if student is logged in\n            if (false) {}\n        }\n    }[\"StudentApp.useEffect\"], [\n        router\n    ]);\n    const loadStudentData = async (studentId)=>{\n        try {\n            setLoading(true);\n            setIsAuthenticated(true);\n            // Get all students and find the current one\n            const students = await _lib_database_store__WEBPACK_IMPORTED_MODULE_8__.dbStore.getStudents();\n            const student = students.find((s)=>s.id === studentId);\n            if (student) {\n                setCurrentStudent(student);\n                const entries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_8__.dbStore.getStudentEntries(student.id);\n                setStudentEntries(entries);\n            } else {\n                // Student not found, logout\n                handleLogout();\n            }\n        } catch (error) {\n            console.error(\"Error loading student data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleLogout = ()=>{\n        if (false) {}\n        router.push(\"/\");\n    };\n    const handleRefresh = ()=>{\n        if (currentStudent) {\n            loadStudentData(currentStudent.id);\n        }\n    };\n    // Simple QR Code - Only Application Number\n    const generateSimpleQRCode = ()=>{\n        if (!currentStudent) return \"\";\n        return currentStudent.application_number;\n    };\n    const copyQRData = async ()=>{\n        try {\n            const qrData = generateSimpleQRCode();\n            await navigator.clipboard.writeText(qrData);\n            setCopiedQR(true);\n            setTimeout(()=>setCopiedQR(false), 2000);\n        } catch (error) {\n            alert(\"Failed to copy QR data\");\n        }\n    };\n    const formatDateTime = (date)=>{\n        return date.toLocaleString(\"en-IN\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const formatTime = (date)=>{\n        return date.toLocaleString(\"en-IN\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const formatDate = (date)=>{\n        return date.toLocaleString(\"en-IN\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\"\n        });\n    };\n    if (!isAuthenticated || !currentStudent) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Loading student data...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                lineNumber: 134,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n            lineNumber: 133,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-green-50 to-blue-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: currentStudent?.image_url || \"/placeholder.svg?height=50&width=50\",\n                                            alt: currentStudent?.name,\n                                            className: \"w-12 h-12 rounded-full border-2 border-green-200 object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    children: [\n                                                        \"Welcome, \",\n                                                        currentStudent?.name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    children: [\n                                                        \"App No: \",\n                                                        currentStudent?.application_number\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleRefresh,\n                                            variant: \"outline\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCircle_Clock_Copy_Home_LogOut_QrCode_RefreshCw_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: `mr-2 h-4 w-4 ${loading ? \"animate-spin\" : \"\"}`\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Refresh\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleLogout,\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCircle_Clock_Copy_Home_LogOut_QrCode_RefreshCw_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Logout\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: ()=>router.push(\"/\"),\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCircle_Clock_Copy_Home_LogOut_QrCode_RefreshCw_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Home\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCircle_Clock_Copy_Home_LogOut_QrCode_RefreshCw_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Personal Details\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: currentStudent?.image_url || \"/placeholder.svg?height=80&width=80\",\n                                                    alt: currentStudent?.name,\n                                                    className: \"w-20 h-20 rounded-full border-4 border-green-200 object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-semibold\",\n                                                            children: currentStudent?.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"secondary\",\n                                                            children: currentStudent?.class\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: currentStudent?.department\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-gray-700\",\n                                                                    children: \"Phone:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 206,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: currentStudent?.phone\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 207,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-gray-700\",\n                                                                    children: \"Email:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: currentStudent?.email || \"Not provided\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 211,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Time Schedule:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: currentStudent?.schedule || \"Not assigned\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-gray-700\",\n                                                            children: \"Application Number:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: \"font-mono\",\n                                                            children: currentStudent?.application_number\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: ()=>setShowQRCard(!showQRCard),\n                                            className: \"w-full\",\n                                            variant: showQRCard ? \"secondary\" : \"default\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCircle_Clock_Copy_Home_LogOut_QrCode_RefreshCw_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 17\n                                                }, this),\n                                                showQRCard ? \"Hide QR ID Card\" : \"Show QR ID Card\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCircle_Clock_Copy_Home_LogOut_QrCode_RefreshCw_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Entry/Exit History\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            children: \"Complete verification history with QR scan and face verification details\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3 max-h-96 overflow-y-auto\",\n                                            children: studentEntries.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCircle_Clock_Copy_Home_LogOut_QrCode_RefreshCw_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-12 w-12 mx-auto text-gray-300 mb-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"No entries recorded yet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-400\",\n                                                        children: \"Go to ID Card Station to scan your QR code\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-4 p-3 bg-blue-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-blue-700\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"How it works:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 261,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-xs text-blue-600 mt-1 space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Scan your QR code at the station\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 264,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Complete face verification\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 265,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Entry/exit will be recorded automatically\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 266,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• View your complete history here\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 267,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 263,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 19\n                                            }, this) : studentEntries.map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border border-gray-200 rounded-lg p-4 bg-white shadow-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start justify-between\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg\",\n                                                                            children: entry.status === \"entry\" ? \"🟢\" : \"🔴\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                            lineNumber: 277,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-semibold capitalize text-lg\",\n                                                                            children: entry.status === \"entry\" ? \"Entry\" : \"Exit\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                            lineNumber: 280,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                            variant: entry.status === \"entry\" ? \"default\" : \"secondary\",\n                                                                            children: entry.verified ? \"✅ Verified\" : \"⚠️ Pending\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                            lineNumber: 283,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 276,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-2 gap-3 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-gray-500\",\n                                                                                    children: \"Date\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                                    lineNumber: 290,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: formatDate(entry.entryTime)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                                    lineNumber: 291,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                            lineNumber: 289,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-gray-500\",\n                                                                                    children: \"Time\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                                    lineNumber: 294,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: formatTime(entry.entryTime)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                                    lineNumber: 295,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                            lineNumber: 293,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 288,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                entry.exitTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2 pt-2 border-t border-gray-100\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-2 gap-3 text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-gray-500\",\n                                                                                        children: \"Exit Date\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 303,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: formatDate(entry.exitTime)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 304,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 302,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-gray-500\",\n                                                                                        children: \"Exit Time\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 307,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: formatTime(entry.exitTime)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 308,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 306,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 301,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 300,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 pt-2 border-t border-gray-100\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-4 text-xs text-gray-500\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"flex items-center gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCircle_Clock_Copy_Home_LogOut_QrCode_RefreshCw_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                        className: \"h-3 w-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 317,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    \"QR Verified\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 316,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"flex items-center gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCircle_Clock_Copy_Home_LogOut_QrCode_RefreshCw_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                        className: \"h-3 w-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 321,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    \"Face Verified\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 320,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"flex items-center gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCircle_Clock_Copy_Home_LogOut_QrCode_RefreshCw_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                        className: \"h-3 w-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 325,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    \"Auto Recorded\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 324,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 315,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 314,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, entry.id, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this),\n                                        studentEntries.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 pt-3 border-t border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between text-sm text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Total Entries: \",\n                                                            studentEntries.length\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Last Updated: \",\n                                                            new Date().toLocaleTimeString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this),\n                showQRCard && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"border-2 border-blue-300 bg-gradient-to-r from-blue-500 to-purple-600 text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"flex items-center gap-2 text-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCircle_Clock_Copy_Home_LogOut_QrCode_RefreshCw_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Digital QR ID Card\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    className: \"text-blue-100\",\n                                    children: \"Simple QR code with Application Number - Scan at ID Card Station\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: currentStudent?.image_url || \"/placeholder.svg?height=64&width=64\",\n                                            alt: currentStudent?.name,\n                                            className: \"w-16 h-16 rounded-full border-4 border-white object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold text-white\",\n                                                    children: currentStudent?.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-100\",\n                                                    children: [\n                                                        currentStudent?.class,\n                                                        \" - \",\n                                                        currentStudent?.department\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"text-xs\",\n                                                    children: \"Application Number Based\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-blue-100\",\n                                                    children: \"Application No:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white font-mono text-lg\",\n                                                    children: currentStudent?.application_number\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-blue-100\",\n                                                    children: \"Phone:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white\",\n                                                    children: currentStudent?.phone\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-block bg-white p-4 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(generateSimpleQRCode())}`,\n                                                alt: \"Student Application Number QR Code\",\n                                                className: \"w-48 h-48 mx-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"text-xs\",\n                                                children: [\n                                                    \"Application Number: \",\n                                                    currentStudent?.application_number\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                            className: \"text-blue-100\",\n                                            children: \"QR Code Data (Application Number):\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                    value: generateSimpleQRCode(),\n                                                    readOnly: true,\n                                                    className: \"bg-white/10 border-white/20 text-white placeholder-white/50 text-center font-mono text-lg\",\n                                                    placeholder: \"Application number will appear here...\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    onClick: copyQRData,\n                                                    size: \"sm\",\n                                                    variant: \"outline\",\n                                                    className: \"absolute top-2 right-2 h-6 w-6 p-0 bg-white/20 border-white/30 text-white hover:bg-white/30\",\n                                                    children: copiedQR ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCircle_Clock_Copy_Home_LogOut_QrCode_RefreshCw_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 33\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCircle_Clock_Copy_Home_LogOut_QrCode_RefreshCw_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-blue-200\",\n                                            children: 'Copy this Application Number and paste in \"Manual QR Input\" at the station if camera scanning doesn\\'t work'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.Alert, {\n                                    className: \"border-yellow-200 bg-yellow-50/10 border-opacity-30\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CheckCircle_Clock_Copy_Home_LogOut_QrCode_RefreshCw_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-4 w-4 text-yellow-200\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.AlertDescription, {\n                                            className: \"text-yellow-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"How it works:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"list-disc list-inside text-xs mt-1 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"QR code contains your Application Number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Station scans and finds your details from admin database\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Face verification with your stored photo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Entry/exit automatically recorded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-blue-100\",\n                                            children: \"\\uD83D\\uDCF1 Show this QR code at ID Card Station\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-blue-100 mt-1\",\n                                            children: \"Station will find your details using Application Number\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 444,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: \"How to Use QR Code at Station\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-green-700 mb-2\",\n                                                children: \"QR Code Scanning:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                className: \"list-decimal list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Show your QR code to station operator\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Operator will start QR scanner camera\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Hold QR code steady in front of camera\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Scanner reads your Application Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Station finds your details from admin database\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Proceed to face verification\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-blue-700 mb-2\",\n                                                children: \"Manual Input Option:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Copy your Application Number from above\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: 'Go to station\\'s \"Manual QR Input\"'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Paste your Application Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: 'Click \"Validate\" button'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Station will find your details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Continue with face verification\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n                    lineNumber: 453,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n            lineNumber: 144,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\student\\\\page.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/student/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/alert.tsx":
/*!*********************************!*\
  !*** ./components/ui/alert.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* binding */ Alert),\n/* harmony export */   AlertDescription: () => (/* binding */ AlertDescription),\n/* harmony export */   AlertTitle: () => (/* binding */ AlertTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst alertVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\", {\n    variants: {\n        variant: {\n            default: \"bg-background text-foreground\",\n            destructive: \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Alert = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        role: \"alert\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(alertVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\cardstation\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 25,\n        columnNumber: 3\n    }, undefined));\nAlert.displayName = \"Alert\";\nconst AlertTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"mb-1 font-medium leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\cardstation\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, undefined));\nAlertTitle.displayName = \"AlertTitle\";\nconst AlertDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm [&_p]:leading-relaxed\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\cardstation\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined));\nAlertDescription.displayName = \"AlertDescription\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/alert.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_1__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus-ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\cardstation\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 26,\n        columnNumber: 10\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\cardstation\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 42,\n        columnNumber: 12\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\cardstation\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 6,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\cardstation\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\cardstation\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\cardstation\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\cardstation\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 32,\n        columnNumber: 37\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\cardstation\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\cardstation\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFJaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUErQixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDM0YscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGdXQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBQ0FKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJEOlxcaWRjYXJkXFxjYXJkc3RhdGlvblxcY29tcG9uZW50c1xcdWlcXGlucHV0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmV4cG9ydCBpbnRlcmZhY2UgSW5wdXRQcm9wcyBleHRlbmRzIFJlYWN0LklucHV0SFRNTEF0dHJpYnV0ZXM8SFRNTElucHV0RWxlbWVudD4ge31cblxuY29uc3QgSW5wdXQgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxJbnB1dEVsZW1lbnQsIElucHV0UHJvcHM+KCh7IGNsYXNzTmFtZSwgdHlwZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gIHJldHVybiAoXG4gICAgPGlucHV0XG4gICAgICB0eXBlPXt0eXBlfVxuICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgXCJmbGV4IGgtMTAgdy1mdWxsIHJvdW5kZWQtbWQgYm9yZGVyIGJvcmRlci1pbnB1dCBiZy1iYWNrZ3JvdW5kIHB4LTMgcHktMiB0ZXh0LXNtIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAgICAgICAgY2xhc3NOYW1lLFxuICAgICAgKX1cbiAgICAgIHJlZj17cmVmfVxuICAgICAgey4uLnByb3BzfVxuICAgIC8+XG4gIClcbn0pXG5JbnB1dC5kaXNwbGF5TmFtZSA9IFwiSW5wdXRcIlxuXG5leHBvcnQgeyBJbnB1dCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIklucHV0IiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInR5cGUiLCJwcm9wcyIsInJlZiIsImlucHV0IiwiZGlzcGxheU5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/label.tsx":
/*!*********************************!*\
  !*** ./components/ui/label.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\cardstation\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 15,\n        columnNumber: 3\n    }, undefined));\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2xhYmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFOEI7QUFDeUI7QUFDVTtBQUVqQztBQUVoQyxNQUFNSSxnQkFBZ0JGLDZEQUFHQSxDQUFDO0FBRTFCLE1BQU1HLHNCQUFRTCw2Q0FBZ0IsQ0FHNUIsQ0FBQyxFQUFFTyxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNSLHVEQUFtQjtRQUFDUSxLQUFLQTtRQUFLRixXQUFXSiw4Q0FBRUEsQ0FBQ0MsaUJBQWlCRztRQUFhLEdBQUdDLEtBQUs7Ozs7OztBQUVyRkgsTUFBTU0sV0FBVyxHQUFHVix1REFBbUIsQ0FBQ1UsV0FBVztBQUVuQyIsInNvdXJjZXMiOlsiRDpcXGlkY2FyZFxcY2FyZHN0YXRpb25cXGNvbXBvbmVudHNcXHVpXFxsYWJlbC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCAqIGFzIExhYmVsUHJpbWl0aXZlIGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtbGFiZWxcIlxuaW1wb3J0IHsgY3ZhLCB0eXBlIFZhcmlhbnRQcm9wcyB9IGZyb20gXCJjbGFzcy12YXJpYW5jZS1hdXRob3JpdHlcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmNvbnN0IGxhYmVsVmFyaWFudHMgPSBjdmEoXCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIGxlYWRpbmctbm9uZSBwZWVyLWRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBwZWVyLWRpc2FibGVkOm9wYWNpdHktNzBcIilcblxuY29uc3QgTGFiZWwgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBMYWJlbFByaW1pdGl2ZS5Sb290PixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBMYWJlbFByaW1pdGl2ZS5Sb290PiAmIFZhcmlhbnRQcm9wczx0eXBlb2YgbGFiZWxWYXJpYW50cz5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPExhYmVsUHJpbWl0aXZlLlJvb3QgcmVmPXtyZWZ9IGNsYXNzTmFtZT17Y24obGFiZWxWYXJpYW50cygpLCBjbGFzc05hbWUpfSB7Li4ucHJvcHN9IC8+XG4pKVxuTGFiZWwuZGlzcGxheU5hbWUgPSBMYWJlbFByaW1pdGl2ZS5Sb290LmRpc3BsYXlOYW1lXG5cbmV4cG9ydCB7IExhYmVsIH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkxhYmVsUHJpbWl0aXZlIiwiY3ZhIiwiY24iLCJsYWJlbFZhcmlhbnRzIiwiTGFiZWwiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJyZWYiLCJSb290IiwiZGlzcGxheU5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/separator.tsx":
/*!*************************************!*\
  !*** ./components/ui/separator.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Separator: () => (/* binding */ Separator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-separator */ \"(ssr)/./node_modules/@radix-ui/react-separator/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Separator auto */ \n\n\n\nconst Separator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, orientation = \"horizontal\", decorative = true, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        decorative: decorative,\n        orientation: orientation,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"shrink-0 bg-border\", orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\cardstation\\\\components\\\\ui\\\\separator.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined));\nSeparator.displayName = _radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3NlcGFyYXRvci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFOEI7QUFDaUM7QUFFL0I7QUFFaEMsTUFBTUcsMEJBQVlILDZDQUFnQixDQUdoQyxDQUFDLEVBQUVLLFNBQVMsRUFBRUMsY0FBYyxZQUFZLEVBQUVDLGFBQWEsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQ3pFLDhEQUFDUiwyREFBdUI7UUFDdEJRLEtBQUtBO1FBQ0xGLFlBQVlBO1FBQ1pELGFBQWFBO1FBQ2JELFdBQVdILDhDQUFFQSxDQUFDLHNCQUFzQkksZ0JBQWdCLGVBQWUsbUJBQW1CLGtCQUFrQkQ7UUFDdkcsR0FBR0csS0FBSzs7Ozs7O0FBR2JMLFVBQVVRLFdBQVcsR0FBR1YsMkRBQXVCLENBQUNVLFdBQVc7QUFFdkMiLCJzb3VyY2VzIjpbIkQ6XFxpZGNhcmRcXGNhcmRzdGF0aW9uXFxjb21wb25lbnRzXFx1aVxcc2VwYXJhdG9yLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0ICogYXMgU2VwYXJhdG9yUHJpbWl0aXZlIGZyb20gXCJAcmFkaXgtdWkvcmVhY3Qtc2VwYXJhdG9yXCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBTZXBhcmF0b3IgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBTZXBhcmF0b3JQcmltaXRpdmUuUm9vdD4sXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgU2VwYXJhdG9yUHJpbWl0aXZlLlJvb3Q+XG4+KCh7IGNsYXNzTmFtZSwgb3JpZW50YXRpb24gPSBcImhvcml6b250YWxcIiwgZGVjb3JhdGl2ZSA9IHRydWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8U2VwYXJhdG9yUHJpbWl0aXZlLlJvb3RcbiAgICByZWY9e3JlZn1cbiAgICBkZWNvcmF0aXZlPXtkZWNvcmF0aXZlfVxuICAgIG9yaWVudGF0aW9uPXtvcmllbnRhdGlvbn1cbiAgICBjbGFzc05hbWU9e2NuKFwic2hyaW5rLTAgYmctYm9yZGVyXCIsIG9yaWVudGF0aW9uID09PSBcImhvcml6b250YWxcIiA/IFwiaC1bMXB4XSB3LWZ1bGxcIiA6IFwiaC1mdWxsIHctWzFweF1cIiwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuU2VwYXJhdG9yLmRpc3BsYXlOYW1lID0gU2VwYXJhdG9yUHJpbWl0aXZlLlJvb3QuZGlzcGxheU5hbWVcblxuZXhwb3J0IHsgU2VwYXJhdG9yIH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlNlcGFyYXRvclByaW1pdGl2ZSIsImNuIiwiU2VwYXJhdG9yIiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsIm9yaWVudGF0aW9uIiwiZGVjb3JhdGl2ZSIsInByb3BzIiwicmVmIiwiUm9vdCIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/separator.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/database-store.ts":
/*!*******************************!*\
  !*** ./lib/database-store.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dbStore: () => (/* binding */ dbStore)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/./lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ dbStore auto */ \n// Local storage keys\nconst STUDENTS_KEY = \"smart_id_students\";\nconst ENTRIES_KEY = \"smart_id_entries\";\nclass DatabaseStore {\n    isSupabaseAvailable() {\n        return _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase !== null && \"undefined\" !== \"undefined\";\n    }\n    isLocalStorageAvailable() {\n        return  false && 0;\n    }\n    // Local Storage Methods\n    saveStudentsToLocal(students) {\n        if (this.isLocalStorageAvailable()) {\n            localStorage.setItem(STUDENTS_KEY, JSON.stringify(students));\n        }\n    }\n    loadStudentsFromLocal() {\n        if (!this.isLocalStorageAvailable()) return [];\n        try {\n            const data = localStorage.getItem(STUDENTS_KEY);\n            if (!data) {\n                // Return sample student for testing\n                const sampleStudent = {\n                    id: \"STU_001\",\n                    application_number: \"APP20254105\",\n                    name: \"Test Student\",\n                    phone: \"9772348371\",\n                    email: \"<EMAIL>\",\n                    class: \"12th\",\n                    department: \"Science\",\n                    schedule: \"Morning\",\n                    image_url: \"/placeholder-user.jpg\",\n                    createdAt: new Date(),\n                    updatedAt: new Date()\n                };\n                console.log(\"📝 Using sample student for testing:\", sampleStudent.name);\n                return [\n                    sampleStudent\n                ];\n            }\n            const students = JSON.parse(data);\n            return students.map((s)=>({\n                    ...s,\n                    createdAt: new Date(s.createdAt),\n                    updatedAt: new Date(s.updatedAt)\n                }));\n        } catch (error) {\n            console.error(\"Error loading students from localStorage:\", error);\n            return [];\n        }\n    }\n    saveEntriesToLocal(entries) {\n        if (this.isLocalStorageAvailable()) {\n            localStorage.setItem(ENTRIES_KEY, JSON.stringify(entries));\n        }\n    }\n    loadEntriesFromLocal() {\n        if (!this.isLocalStorageAvailable()) return [];\n        try {\n            const data = localStorage.getItem(ENTRIES_KEY);\n            if (!data) return [];\n            const entries = JSON.parse(data);\n            return entries.map((e)=>({\n                    ...e,\n                    entryTime: new Date(e.entryTime),\n                    exitTime: e.exitTime ? new Date(e.exitTime) : undefined,\n                    createdAt: new Date(e.createdAt)\n                }));\n        } catch (error) {\n            console.error(\"Error loading entries from localStorage:\", error);\n            return [];\n        }\n    }\n    // Student Management\n    async addStudent(student) {\n        const res = await fetch(\"/api/students\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(student)\n        });\n        if (!res.ok) throw new Error(\"Failed to add student\");\n        const data = await res.json();\n        return {\n            ...data,\n            createdAt: new Date(data.createdAt),\n            updatedAt: new Date(data.updatedAt)\n        };\n    }\n    async getStudents() {\n        try {\n            // Try API first\n            const res = await fetch(\"/api/students\");\n            if (res.ok) {\n                const data = await res.json();\n                console.log(\"✅ Students loaded from API:\", data.length);\n                return data.map((s)=>({\n                        ...s,\n                        createdAt: new Date(s.createdAt || s.created_at || new Date()),\n                        updatedAt: new Date(s.updatedAt || s.updated_at || new Date())\n                    }));\n            } else {\n                throw new Error(\"API failed\");\n            }\n        } catch (error) {\n            console.warn(\"⚠️ Students API not available, using localStorage fallback\");\n            // Fallback to localStorage\n            const localStudents = this.loadStudentsFromLocal();\n            console.log(\"✅ Students loaded from localStorage:\", localStudents.length);\n            return localStudents;\n        }\n    }\n    async getStudentByAppNumber(appNumber) {\n        try {\n            // Try API first\n            const res = await fetch(`/api/students?application_number=${encodeURIComponent(appNumber)}`);\n            if (res.ok) {\n                const data = await res.json();\n                if (!data || data.length === 0) return null;\n                const s = data[0];\n                console.log(\"✅ Student found via API:\", s.name);\n                return {\n                    ...s,\n                    createdAt: new Date(s.createdAt || s.created_at || new Date()),\n                    updatedAt: new Date(s.updatedAt || s.updated_at || new Date())\n                };\n            } else {\n                throw new Error(\"API failed\");\n            }\n        } catch (error) {\n            console.warn(\"⚠️ Student API not available, using localStorage fallback\");\n            // Fallback to localStorage\n            const localStudents = this.loadStudentsFromLocal();\n            const student = localStudents.find((s)=>s.application_number === appNumber);\n            if (student) {\n                console.log(\"✅ Student found via localStorage:\", student.name);\n            } else {\n                console.log(\"❌ Student not found in localStorage\");\n            }\n            return student || null;\n        }\n    }\n    async getStudentByAppAndPhone(appNumber, phone) {\n        const url = `/api/students?application_number=${encodeURIComponent(appNumber)}&phone=${encodeURIComponent(phone)}`;\n        const res = await fetch(url);\n        if (!res.ok) return null;\n        const data = await res.json();\n        if (!data || data.length === 0) return null;\n        const s = data[0];\n        return {\n            ...s,\n            createdAt: new Date(s.createdAt),\n            updatedAt: new Date(s.updatedAt)\n        };\n    }\n    async updateStudent(id, updates) {\n        const res = await fetch(\"/api/students\", {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                id,\n                ...updates\n            })\n        });\n        if (!res.ok) throw new Error(\"Failed to update student\");\n        const data = await res.json();\n        return {\n            ...data,\n            createdAt: new Date(data.createdAt),\n            updatedAt: new Date(data.updatedAt)\n        };\n    }\n    async deleteStudent(id) {\n        const res = await fetch(\"/api/students\", {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                id\n            })\n        });\n        if (!res.ok) throw new Error(\"Failed to delete student\");\n        return true;\n    }\n    // Entry Log Management - Using API route for better reliability\n    async addEntry(studentId, applicationNumber, studentName) {\n        try {\n            const entryData = {\n                student_id: studentId,\n                application_number: applicationNumber,\n                student_name: studentName,\n                verification_method: \"qr_and_face\",\n                qr_validated: true,\n                verification_timestamp: new Date().toISOString(),\n                station_id: \"main_entrance\"\n            };\n            console.log(\"Sending entry data to API:\", entryData);\n            try {\n                // Try API first\n                const res = await fetch('/api/entries', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(entryData)\n                });\n                if (res.ok) {\n                    const data = await res.json();\n                    console.log(\"✅ Entry recorded via API:\", data);\n                    return {\n                        ...data,\n                        entryTime: new Date(data.entry_time),\n                        exitTime: data.exit_time ? new Date(data.exit_time) : undefined,\n                        createdAt: new Date(data.created_at || data.entry_time),\n                        updatedAt: new Date(data.updated_at || data.entry_time)\n                    };\n                } else {\n                    throw new Error(\"API failed\");\n                }\n            } catch (apiError) {\n                console.warn(\"⚠️ API not available, using localStorage fallback\");\n                // Fallback to localStorage\n                const existingEntries = this.loadEntriesFromLocal();\n                // Check if student already has entry today without exit\n                const today = new Date();\n                today.setHours(0, 0, 0, 0);\n                const tomorrow = new Date(today);\n                tomorrow.setDate(tomorrow.getDate() + 1);\n                const todayEntry = existingEntries.find((entry)=>entry.student_id === studentId && entry.entryTime >= today && entry.entryTime < tomorrow && !entry.exitTime);\n                const now = new Date();\n                const entryId = `entry_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;\n                let newEntry;\n                if (todayEntry) {\n                    // This is an exit\n                    todayEntry.exitTime = now;\n                    todayEntry.status = \"exit\";\n                    // Update existing entry\n                    const updatedEntries = existingEntries.map((entry)=>entry.id === todayEntry.id ? todayEntry : entry);\n                    this.saveEntriesToLocal(updatedEntries);\n                    newEntry = todayEntry;\n                    console.log(\"✅ EXIT recorded via localStorage:\", newEntry);\n                } else {\n                    // This is an entry\n                    newEntry = {\n                        id: entryId,\n                        student_id: studentId,\n                        application_number: applicationNumber,\n                        student_name: studentName,\n                        entryTime: now,\n                        exitTime: undefined,\n                        status: \"entry\",\n                        verified: true,\n                        createdAt: now\n                    };\n                    existingEntries.push(newEntry);\n                    this.saveEntriesToLocal(existingEntries);\n                    console.log(\"✅ ENTRY recorded via localStorage:\", newEntry);\n                }\n                return newEntry;\n            }\n        } catch (error) {\n            console.error(\"❌ Error adding entry:\", error);\n            throw error;\n        }\n    }\n    async getStudentEntries(studentId) {\n        try {\n            // Use API route which handles both MongoDB and fallback\n            const res = await fetch(`/api/entries?student_id=${encodeURIComponent(studentId)}`);\n            if (!res.ok) {\n                console.error(\"Failed to fetch entries from API\");\n                return [];\n            }\n            const data = await res.json();\n            return data.map((e)=>({\n                    ...e,\n                    entryTime: new Date(e.entry_time),\n                    exitTime: e.exit_time ? new Date(e.exit_time) : undefined,\n                    createdAt: new Date(e.created_at || e.entry_time),\n                    updatedAt: new Date(e.updated_at || e.entry_time)\n                }));\n        } catch (error) {\n            console.error(\"Error fetching student entries:\", error);\n            return [];\n        }\n    }\n    async getAllEntries() {\n        try {\n            // Try API first\n            const res = await fetch('/api/entries');\n            if (res.ok) {\n                const data = await res.json();\n                console.log(\"✅ Entries loaded from API:\", data.length);\n                return data.map((e)=>({\n                        ...e,\n                        entryTime: new Date(e.entry_time),\n                        exitTime: e.exit_time ? new Date(e.exit_time) : undefined,\n                        createdAt: new Date(e.created_at || e.entry_time),\n                        updatedAt: new Date(e.updated_at || e.entry_time)\n                    }));\n            } else {\n                throw new Error(\"API failed\");\n            }\n        } catch (error) {\n            console.warn(\"⚠️ API not available, using localStorage fallback\");\n            // Fallback to localStorage\n            const localEntries = this.loadEntriesFromLocal();\n            console.log(\"✅ Entries loaded from localStorage:\", localEntries.length);\n            return localEntries;\n        }\n    }\n    async getTodayEntries() {\n        try {\n            // Get all entries and filter for today\n            const allEntries = await this.getAllEntries();\n            const today = new Date().toDateString();\n            return allEntries.filter((e)=>e.entryTime.toDateString() === today);\n        } catch (error) {\n            console.error(\"Error fetching today entries:\", error);\n            return [];\n        }\n    }\n    // Admin Authentication\n    async authenticateAdmin(username, password) {\n        if (this.isSupabaseAvailable() && _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase) {\n            // Use Supabase\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"admin_users\").select(\"*\").eq(\"username\", username).single();\n            if (error || !data) {\n                return false;\n            }\n            // Simple password check (in production, use proper hashing)\n            return password === \"admin123\";\n        } else {\n            // Fallback authentication for demo\n            return username === \"admin\" && password === \"admin123\";\n        }\n    }\n    // Utility functions\n    generateApplicationNumber() {\n        const year = new Date().getFullYear();\n        const random = Math.floor(Math.random() * 10000).toString().padStart(4, \"0\");\n        return `APP${year}${random}`;\n    }\n    convertStudentDates(student) {\n        return {\n            ...student,\n            createdAt: new Date(student.created_at),\n            updatedAt: new Date(student.updated_at)\n        };\n    }\n    convertEntryLogDates(entry) {\n        return {\n            ...entry,\n            entryTime: new Date(entry.entry_time),\n            exitTime: entry.exit_time ? new Date(entry.exit_time) : undefined,\n            createdAt: new Date(entry.created_at)\n        };\n    }\n    // Clear all local data (for testing)\n    clearLocalData() {\n        if (this.isLocalStorageAvailable()) {\n            localStorage.removeItem(STUDENTS_KEY);\n            localStorage.removeItem(ENTRIES_KEY);\n        }\n    }\n    // Get storage info\n    getStorageInfo() {\n        return {\n            mode: \"Cloud\",\n            studentsCount: 0,\n            entriesCount: 0\n        };\n    }\n}\nconst dbStore = new DatabaseStore();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/database-store.ts\n");

/***/ }),

/***/ "(ssr)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Same database configuration as main system\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || \"\";\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || \"\";\n// Create a fallback client that won't break during build\nconst supabase = supabaseUrl && supabaseAnonKey ? (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey) : null;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJEOlxcaWRjYXJkXFxjYXJkc3RhdGlvblxcbGliXFx1dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlIENsYXNzVmFsdWUsIGNsc3ggfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Capp%5C%5Cstudent%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Capp%5C%5Cstudent%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/student/page.tsx */ \"(ssr)/./app/student/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNpZGNhcmQlNUMlNUNjYXJkc3RhdGlvbiU1QyU1Q2FwcCU1QyU1Q3N0dWRlbnQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0pBQW9GIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxpZGNhcmRcXFxcY2FyZHN0YXRpb25cXFxcYXBwXFxcXHN0dWRlbnRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Capp%5C%5Cstudent%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Ccardstation%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/tailwind-merge","vendor-chunks/whatwg-url","vendor-chunks/lucide-react","vendor-chunks/@radix-ui","vendor-chunks/webidl-conversions","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/isows","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fstudent%2Fpage&page=%2Fstudent%2Fpage&appPaths=%2Fstudent%2Fpage&pagePath=private-next-app-dir%2Fstudent%2Fpage.tsx&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();