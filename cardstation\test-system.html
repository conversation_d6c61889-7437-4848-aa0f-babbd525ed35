<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ID Card Station Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .success {
            background: #28a745;
        }
        .error {
            background: #dc3545;
        }
        .qr-code {
            text-align: center;
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>🆔 ID Card Station System Test</h1>
    
    <div class="card">
        <h2>📱 Student QR Code</h2>
        <p>Test Application Number: <strong>APP20254105</strong></p>
        <div class="qr-code">
            <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=APP20254105" 
                 alt="Test QR Code" style="border: 2px solid #ddd; padding: 10px;">
        </div>
        <p><em>This QR code contains the application number that the scanner will read.</em></p>
    </div>

    <div class="card">
        <h2>🔍 QR Scanner Simulation</h2>
        <div id="scanner-status" class="status">Ready to scan QR code</div>
        <button class="button" onclick="simulateQRScan()">Simulate QR Code Scan</button>
        <button class="button" onclick="simulateInvalidQR()">Test Invalid QR Code</button>
    </div>

    <div class="card">
        <h2>👤 Face Verification Simulation</h2>
        <div id="face-status" class="status">Waiting for QR validation</div>
        <button class="button" id="face-btn" onclick="simulateFaceVerification()" disabled>
            Start Face Verification
        </button>
    </div>

    <div class="card">
        <h2>📝 Entry Recording</h2>
        <div id="entry-status" class="status">Waiting for verification</div>
        <div id="entry-history"></div>
    </div>

    <div class="card">
        <h2>🔄 System Status</h2>
        <p><strong>Database:</strong> <span id="db-status">Testing mode (sample data)</span></p>
        <p><strong>QR Scanner:</strong> <span id="qr-scanner-status">Ready</span></p>
        <p><strong>Face Camera:</strong> <span id="camera-status">Ready</span></p>
        <button class="button" onclick="resetSystem()">Reset System</button>
    </div>

    <script>
        let currentStudent = null;
        let qrValidated = false;
        let entries = [];

        // Sample student data (same as in API)
        const sampleStudent = {
            id: "STU_001",
            application_number: "APP20254105",
            name: "Test Student",
            phone: "9772348371",
            email: "<EMAIL>",
            class: "12th",
            department: "Science",
            schedule: "Morning",
            image_url: "/placeholder-user.jpg"
        };

        function updateStatus(elementId, message, type = '') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = 'status ' + type;
        }

        function simulateQRScan() {
            updateStatus('scanner-status', '🔍 Scanning QR code...', '');
            
            setTimeout(() => {
                updateStatus('scanner-status', '✅ QR Code detected! Validating...', '');
                
                setTimeout(() => {
                    // Simulate validation against database
                    currentStudent = sampleStudent;
                    qrValidated = true;
                    
                    updateStatus('scanner-status', 
                        `✅ Application Number validated successfully!\nStudent: ${currentStudent.name}\nClass: ${currentStudent.class}`, 
                        'success');
                    
                    updateStatus('face-status', 'Ready for face verification', '');
                    document.getElementById('face-btn').disabled = false;
                }, 1500);
            }, 1000);
        }

        function simulateInvalidQR() {
            updateStatus('scanner-status', '🔍 Scanning QR code...', '');
            
            setTimeout(() => {
                updateStatus('scanner-status', 
                    '❌ Application Number validation failed!\nApplication Number "INVALID123" not found in database.\n\n🔄 Please try:\n• Verifying the application number\n• Contacting admin for registration', 
                    'error');
            }, 1000);
        }

        function simulateFaceVerification() {
            if (!qrValidated) {
                alert('Please scan a valid QR code first!');
                return;
            }

            updateStatus('face-status', '📸 Starting face verification...', '');
            document.getElementById('face-btn').disabled = true;

            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += 10;
                updateStatus('face-status', `🔍 Analyzing face... ${progress}%`, '');
                
                if (progress >= 100) {
                    clearInterval(progressInterval);
                    
                    // Simulate face match result
                    const matchScore = Math.floor(Math.random() * 40) + 60; // 60-100%
                    const isMatch = matchScore > 75;
                    
                    if (isMatch) {
                        updateStatus('face-status', 
                            `✅ Face verification successful!\nMatch Score: ${matchScore}%\n\n📝 Recording entry...`, 
                            'success');
                        
                        setTimeout(() => {
                            recordEntry();
                        }, 1000);
                    } else {
                        updateStatus('face-status', 
                            `❌ Face verification failed!\nMatch Score: ${matchScore}% (Required: >75%)\n\n🔄 Please try again with better lighting`, 
                            'error');
                        document.getElementById('face-btn').disabled = false;
                    }
                }
            }, 300);
        }

        function recordEntry() {
            const now = new Date();
            const entry = {
                id: 'entry_' + Date.now(),
                student_name: currentStudent.name,
                application_number: currentStudent.application_number,
                entry_time: now.toLocaleString(),
                status: 'entry',
                verified: true
            };
            
            entries.unshift(entry);
            
            updateStatus('entry-status', 
                `✅ Entry recorded successfully!\nStudent: ${entry.student_name}\nTime: ${entry.entry_time}`, 
                'success');
            
            updateEntryHistory();
            
            setTimeout(() => {
                resetSystem();
            }, 3000);
        }

        function updateEntryHistory() {
            const historyDiv = document.getElementById('entry-history');
            if (entries.length === 0) {
                historyDiv.innerHTML = '<p><em>No entries recorded yet</em></p>';
                return;
            }
            
            let html = '<h3>Recent Entries:</h3>';
            entries.slice(0, 5).forEach(entry => {
                html += `
                    <div style="border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 4px;">
                        <strong>${entry.student_name}</strong> - ${entry.status}<br>
                        <small>App No: ${entry.application_number} | Time: ${entry.entry_time}</small>
                    </div>
                `;
            });
            historyDiv.innerHTML = html;
        }

        function resetSystem() {
            currentStudent = null;
            qrValidated = false;
            
            updateStatus('scanner-status', 'Ready to scan QR code', '');
            updateStatus('face-status', 'Waiting for QR validation', '');
            updateStatus('entry-status', 'Waiting for verification', '');
            
            document.getElementById('face-btn').disabled = true;
        }

        // Initialize
        updateEntryHistory();
    </script>
</body>
</html>
