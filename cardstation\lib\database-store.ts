"use client"

import { supabase } from "./supabase"
import type { Database } from "./supabase"
import clientPromise from "./mongodb"

type Student = Database["public"]["Tables"]["students"]["Row"]
type StudentInsert = Database["public"]["Tables"]["students"]["Insert"]
type StudentUpdate = Database["public"]["Tables"]["students"]["Update"]
type EntryLog = Database["public"]["Tables"]["entry_logs"]["Row"]
type EntryLogInsert = Database["public"]["Tables"]["entry_logs"]["Insert"]

export interface StudentWithDates extends Omit<Student, "created_at" | "updated_at"> {
  createdAt: Date
  updatedAt: Date
}

export interface EntryLogWithDates extends Omit<EntryLog, "entry_time" | "exit_time" | "created_at"> {
  entryTime: Date
  exitTime?: Date
  createdAt: Date
}

// Local storage keys
const STUDENTS_KEY = "smart_id_students"
const ENTRIES_KEY = "smart_id_entries"

class DatabaseStore {
  private isSupabaseAvailable(): boolean {
    return supabase !== null && typeof window !== "undefined"
  }

  private isLocalStorageAvailable(): boolean {
    return typeof window !== "undefined" && typeof window.localStorage !== "undefined"
  }

  // Local Storage Methods
  private saveStudentsToLocal(students: StudentWithDates[]): void {
    if (this.isLocalStorageAvailable()) {
      localStorage.setItem(STUDENTS_KEY, JSON.stringify(students))
    }
  }

  private loadStudentsFromLocal(): StudentWithDates[] {
    if (!this.isLocalStorageAvailable()) return []

    try {
      const data = localStorage.getItem(STUDENTS_KEY)
      if (!data) return []

      const students = JSON.parse(data)
      return students.map((s: any) => ({
        ...s,
        createdAt: new Date(s.createdAt),
        updatedAt: new Date(s.updatedAt),
      }))
    } catch (error) {
      console.error("Error loading students from localStorage:", error)
      return []
    }
  }

  private saveEntriesToLocal(entries: EntryLogWithDates[]): void {
    if (this.isLocalStorageAvailable()) {
      localStorage.setItem(ENTRIES_KEY, JSON.stringify(entries))
    }
  }

  private loadEntriesFromLocal(): EntryLogWithDates[] {
    if (!this.isLocalStorageAvailable()) return []

    try {
      const data = localStorage.getItem(ENTRIES_KEY)
      if (!data) return []

      const entries = JSON.parse(data)
      return entries.map((e: any) => ({
        ...e,
        entryTime: new Date(e.entryTime),
        exitTime: e.exitTime ? new Date(e.exitTime) : undefined,
        createdAt: new Date(e.createdAt),
      }))
    } catch (error) {
      console.error("Error loading entries from localStorage:", error)
      return []
    }
  }

  // Student Management
  async addStudent(student: Omit<StudentInsert, "id" | "created_at" | "updated_at">): Promise<StudentWithDates> {
    const res = await fetch("/api/students", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(student),
    });
    if (!res.ok) throw new Error("Failed to add student");
    const data = await res.json();
    return {
      ...data,
      createdAt: new Date(data.createdAt),
      updatedAt: new Date(data.updatedAt),
    };
  }

  async getStudents(): Promise<StudentWithDates[]> {
    const res = await fetch("/api/students");
    if (!res.ok) throw new Error("Failed to fetch students");
    const data = await res.json();
    return data.map((s: any) => ({
      ...s,
      createdAt: new Date(s.createdAt),
      updatedAt: new Date(s.updatedAt),
    }));
  }

  async getStudentByAppNumber(appNumber: string): Promise<StudentWithDates | null> {
    const res = await fetch(`/api/students?application_number=${encodeURIComponent(appNumber)}`);
    if (!res.ok) return null;
    const data = await res.json();
    if (!data || data.length === 0) return null;
    const s = data[0];
    return {
      ...s,
      createdAt: new Date(s.createdAt),
      updatedAt: new Date(s.updatedAt),
    };
  }

  async getStudentByAppAndPhone(appNumber: string, phone: string): Promise<StudentWithDates | null> {
    const url = `/api/students?application_number=${encodeURIComponent(appNumber)}&phone=${encodeURIComponent(phone)}`;
    const res = await fetch(url);
    if (!res.ok) return null;
    const data = await res.json();
    if (!data || data.length === 0) return null;
    const s = data[0];
    return {
      ...s,
      createdAt: new Date(s.createdAt),
      updatedAt: new Date(s.updatedAt),
    };
  }

  async updateStudent(id: string, updates: StudentUpdate): Promise<StudentWithDates | null> {
    const res = await fetch("/api/students", {
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ id, ...updates }),
    });
    if (!res.ok) throw new Error("Failed to update student");
    const data = await res.json();
    return {
      ...data,
      createdAt: new Date(data.createdAt),
      updatedAt: new Date(data.updatedAt),
    };
  }

  async deleteStudent(id: string): Promise<boolean> {
    const res = await fetch("/api/students", {
      method: "DELETE",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ id }),
    });
    if (!res.ok) throw new Error("Failed to delete student");
    return true;
  }

  // Entry Log Management
  async addEntry(studentId: string, applicationNumber: string, studentName: string): Promise<EntryLogWithDates> {
    if (this.isSupabaseAvailable() && supabase) {
      // Use Supabase
      const { data: existingEntry } = await supabase
        .from("entry_logs")
        .select("*")
        .eq("student_id", studentId)
        .is("exit_time", null)
        .order("entry_time", { ascending: false })
        .limit(1)
        .single()

      if (existingEntry) {
        // Student is inside, mark exit
        const { data, error } = await supabase
          .from("entry_logs")
          .update({
            exit_time: new Date().toISOString(),
            status: "exit",
          })
          .eq("id", existingEntry.id)
          .select()
          .single()

        if (error) {
          console.error("Error updating entry:", error)
          throw new Error("Failed to record exit")
        }

        return this.convertEntryLogDates(data)
      } else {
        // New entry
        const { data, error } = await supabase
          .from("entry_logs")
          .insert({
            student_id: studentId,
            application_number: applicationNumber,
            student_name: studentName,
            status: "entry",
            verified: true,
          })
          .select()
          .single()

        if (error) {
          console.error("Error adding entry:", error)
          throw new Error("Failed to record entry")
        }

        return this.convertEntryLogDates(data)
      }
    } else {
      // Use localStorage
      const entries = this.loadEntriesFromLocal()

      // Check if student is already inside
      const existingEntry = entries.find((e) => e.student_id === studentId && !e.exitTime)

      if (existingEntry) {
        // Student is inside, mark exit
        existingEntry.exitTime = new Date()
        existingEntry.status = "exit"
        this.saveEntriesToLocal(entries)
        return existingEntry
      } else {
        // New entry
        const newEntry: EntryLogWithDates = {
          id: `entry_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          student_id: studentId,
          application_number: applicationNumber,
          student_name: studentName,
          entryTime: new Date(),
          status: "entry",
          verified: true,
          createdAt: new Date(),
        }

        entries.unshift(newEntry)
        this.saveEntriesToLocal(entries)
        return newEntry
      }
    }
  }

  async getStudentEntries(studentId: string): Promise<EntryLogWithDates[]> {
    try {
      // Use API route which handles both MongoDB and fallback
      const res = await fetch(`/api/entries?student_id=${encodeURIComponent(studentId)}`);
      if (!res.ok) {
        console.error("Failed to fetch entries from API");
        return [];
      }

      const data = await res.json();
      return data.map((e: any) => ({
        ...e,
        entryTime: new Date(e.entry_time),
        exitTime: e.exit_time ? new Date(e.exit_time) : undefined,
        createdAt: new Date(e.created_at || e.entry_time),
        updatedAt: new Date(e.updated_at || e.entry_time)
      }));
    } catch (error) {
      console.error("Error fetching student entries:", error);
      return [];
    }
  }

  async getAllEntries(): Promise<EntryLogWithDates[]> {
    try {
      // Use API route which handles both MongoDB and fallback
      const res = await fetch('/api/entries');
      if (!res.ok) {
        console.error("Failed to fetch all entries from API");
        return [];
      }

      const data = await res.json();
      return data.map((e: any) => ({
        ...e,
        entryTime: new Date(e.entry_time),
        exitTime: e.exit_time ? new Date(e.exit_time) : undefined,
        createdAt: new Date(e.created_at || e.entry_time),
        updatedAt: new Date(e.updated_at || e.entry_time)
      }));
    } catch (error) {
      console.error("Error fetching all entries:", error);
      return [];
    }
  }

  async getTodayEntries(): Promise<EntryLogWithDates[]> {
    try {
      // Get all entries and filter for today
      const allEntries = await this.getAllEntries();
      const today = new Date().toDateString();
      return allEntries.filter((e) => e.entryTime.toDateString() === today);
    } catch (error) {
      console.error("Error fetching today entries:", error);
      return [];
    }
  }

  // Admin Authentication
  async authenticateAdmin(username: string, password: string): Promise<boolean> {
    if (this.isSupabaseAvailable() && supabase) {
      // Use Supabase
      const { data, error } = await supabase.from("admin_users").select("*").eq("username", username).single()

      if (error || !data) {
        return false
      }

      // Simple password check (in production, use proper hashing)
      return password === "admin123"
    } else {
      // Fallback authentication for demo
      return username === "admin" && password === "admin123"
    }
  }

  // Utility functions
  generateApplicationNumber(): string {
    const year = new Date().getFullYear()
    const random = Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, "0")
    return `APP${year}${random}`
  }

  private convertStudentDates(student: Student): StudentWithDates {
    return {
      ...student,
      createdAt: new Date(student.created_at),
      updatedAt: new Date(student.updated_at),
    }
  }

  private convertEntryLogDates(entry: EntryLog): EntryLogWithDates {
    return {
      ...entry,
      entryTime: new Date(entry.entry_time),
      exitTime: entry.exit_time ? new Date(entry.exit_time) : undefined,
      createdAt: new Date(entry.created_at),
    }
  }

  // Clear all local data (for testing)
  clearLocalData(): void {
    if (this.isLocalStorageAvailable()) {
      localStorage.removeItem(STUDENTS_KEY)
      localStorage.removeItem(ENTRIES_KEY)
    }
  }

  // Get storage info
  getStorageInfo(): { mode: string; studentsCount: number; entriesCount: number } {
  return {
    mode: "Cloud",
    studentsCount: 0,
    entriesCount: 0,
  }
}
}

export const dbStore = new DatabaseStore()
export type { StudentWithDates as Student, EntryLogWithDates as EntryLog }
 