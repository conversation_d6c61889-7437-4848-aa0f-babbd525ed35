"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./app/admin/page.tsx":
/*!****************************!*\
  !*** ./app/admin/page.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _lib_database_store__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/database-store */ \"(app-pages-browser)/./lib/database-store.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_13__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AdminPanel() {\n    _s();\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingStudent, setEditingStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [copiedText, setCopiedText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [databaseConnected, setDatabaseConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [storageInfo, setStorageInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        mode: \"Local\",\n        studentsCount: 0,\n        entriesCount: 0\n    });\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalStudents: 0,\n        todayEntries: 0,\n        todayExits: 0,\n        totalEntries: 0\n    });\n    const [newStudent, setNewStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        phone: \"\",\n        email: \"\",\n        class: \"\",\n        department: \"\",\n        schedule: \"\",\n        image: \"\"\n    });\n    const [imagePreview, setImagePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageFile, setImageFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPanel.useEffect\": ()=>{\n            // Check if admin is logged in\n            if (true) {\n                const adminLoggedIn = localStorage.getItem(\"adminLoggedIn\");\n                if (!adminLoggedIn) {\n                    router.push(\"/\");\n                    return;\n                }\n            }\n            setIsAuthenticated(true);\n            checkDatabaseConnection();\n            loadData();\n        }\n    }[\"AdminPanel.useEffect\"], [\n        router\n    ]);\n    // Auto-reload data every 5 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPanel.useEffect\": ()=>{\n            if (!isAuthenticated) return;\n            const interval = setInterval({\n                \"AdminPanel.useEffect.interval\": ()=>{\n                    console.log(\"🔄 Auto-refreshing admin data...\");\n                    loadData();\n                }\n            }[\"AdminPanel.useEffect.interval\"], 5000) // 5 seconds\n            ;\n            return ({\n                \"AdminPanel.useEffect\": ()=>clearInterval(interval)\n            })[\"AdminPanel.useEffect\"];\n        }\n    }[\"AdminPanel.useEffect\"], [\n        isAuthenticated\n    ]);\n    const checkDatabaseConnection = async ()=>{\n        const connected = _lib_supabase__WEBPACK_IMPORTED_MODULE_12__.supabase !== null;\n        setDatabaseConnected(connected);\n        const storageInfo = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getStorageInfo();\n        setStorageInfo(storageInfo);\n    };\n    const loadData = async ()=>{\n        try {\n            setLoading(true);\n            const studentsData = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getStudents();\n            const allEntries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getAllEntries();\n            const todayEntries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getTodayEntries();\n            setStudents(studentsData);\n            setStats({\n                totalStudents: studentsData.length,\n                todayEntries: todayEntries.filter((e)=>e.status === 'entry').length,\n                todayExits: todayEntries.filter((e)=>e.status === 'exit').length,\n                totalEntries: allEntries.length\n            });\n            const storageInfo = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getStorageInfo();\n            setStorageInfo(storageInfo);\n        } catch (error) {\n            console.error(\"Error loading data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleRefresh = ()=>{\n        loadData();\n    };\n    const handleLogout = ()=>{\n        if (true) {\n            localStorage.removeItem(\"adminLoggedIn\");\n            localStorage.removeItem(\"adminUsername\");\n        }\n        router.push(\"/\");\n    };\n    // Handle image file selection\n    const handleImageSelect = (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (!file) return;\n        // Validate file type\n        if (!file.type.startsWith(\"image/\")) {\n            alert(\"Please select a valid image file (JPG, PNG, GIF, etc.)\");\n            return;\n        }\n        // Validate file size (max 5MB)\n        if (file.size > 5 * 1024 * 1024) {\n            alert(\"Image size should be less than 5MB\");\n            return;\n        }\n        setImageFile(file);\n        // Create preview\n        const reader = new FileReader();\n        reader.onload = (e)=>{\n            var _e_target;\n            const result = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n            setImagePreview(result);\n            setNewStudent({\n                ...newStudent,\n                image: result\n            });\n        };\n        reader.readAsDataURL(file);\n    };\n    // Remove selected image\n    const removeImage = ()=>{\n        setImageFile(null);\n        setImagePreview(null);\n        setNewStudent({\n            ...newStudent,\n            image: \"\"\n        });\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    // Take photo using camera\n    const takePhoto = async ()=>{\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({\n                video: true\n            });\n            // Create a video element to capture the stream\n            const video = document.createElement(\"video\");\n            video.srcObject = stream;\n            video.autoplay = true;\n            // Create a modal or popup to show camera feed\n            const modal = document.createElement(\"div\");\n            modal.style.cssText = \"\\n        position: fixed;\\n        top: 0;\\n        left: 0;\\n        width: 100%;\\n        height: 100%;\\n        background: rgba(0,0,0,0.8);\\n        display: flex;\\n        justify-content: center;\\n        align-items: center;\\n        z-index: 1000;\\n      \";\n            const container = document.createElement(\"div\");\n            container.style.cssText = \"\\n        background: white;\\n        padding: 20px;\\n        border-radius: 10px;\\n        text-align: center;\\n      \";\n            const canvas = document.createElement(\"canvas\");\n            const captureBtn = document.createElement(\"button\");\n            captureBtn.textContent = \"Capture Photo\";\n            captureBtn.style.cssText = \"\\n        background: #3b82f6;\\n        color: white;\\n        padding: 10px 20px;\\n        border: none;\\n        border-radius: 5px;\\n        margin: 10px;\\n        cursor: pointer;\\n      \";\n            const cancelBtn = document.createElement(\"button\");\n            cancelBtn.textContent = \"Cancel\";\n            cancelBtn.style.cssText = \"\\n        background: #6b7280;\\n        color: white;\\n        padding: 10px 20px;\\n        border: none;\\n        border-radius: 5px;\\n        margin: 10px;\\n        cursor: pointer;\\n      \";\n            container.appendChild(video);\n            container.appendChild(document.createElement(\"br\"));\n            container.appendChild(captureBtn);\n            container.appendChild(cancelBtn);\n            modal.appendChild(container);\n            document.body.appendChild(modal);\n            // Capture photo\n            captureBtn.onclick = ()=>{\n                canvas.width = video.videoWidth;\n                canvas.height = video.videoHeight;\n                const ctx = canvas.getContext(\"2d\");\n                ctx === null || ctx === void 0 ? void 0 : ctx.drawImage(video, 0, 0);\n                const imageData = canvas.toDataURL(\"image/jpeg\", 0.8);\n                setImagePreview(imageData);\n                setNewStudent({\n                    ...newStudent,\n                    image: imageData\n                });\n                // Stop camera and close modal\n                stream.getTracks().forEach((track)=>track.stop());\n                document.body.removeChild(modal);\n            };\n            // Cancel\n            cancelBtn.onclick = ()=>{\n                stream.getTracks().forEach((track)=>track.stop());\n                document.body.removeChild(modal);\n            };\n        } catch (error) {\n            alert(\"Camera access denied or not available\");\n        }\n    };\n    const validateForm = ()=>{\n        if (!newStudent.name.trim()) {\n            alert(\"Student name is required\");\n            return false;\n        }\n        if (!newStudent.phone.trim()) {\n            alert(\"Phone number is required\");\n            return false;\n        }\n        if (newStudent.phone.length !== 10 || !/^\\d+$/.test(newStudent.phone)) {\n            alert(\"Phone number must be exactly 10 digits\");\n            return false;\n        }\n        if (!newStudent.class) {\n            alert(\"Class selection is required\");\n            return false;\n        }\n        if (!newStudent.image) {\n            alert(\"Student photo is required. Please upload an image or take a photo.\");\n            return false;\n        }\n        return true;\n    };\n    const handleAddStudent = async ()=>{\n        if (!validateForm()) return;\n        // Check if phone number already exists\n        const existingStudent = students.find((s)=>s.phone === newStudent.phone);\n        if (existingStudent) {\n            alert(\"Phone number already exists!\");\n            return;\n        }\n        setLoading(true);\n        try {\n            const applicationNumber = _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.generateApplicationNumber();\n            const student = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.addStudent({\n                ...newStudent,\n                application_number: applicationNumber,\n                image_url: newStudent.image\n            });\n            await loadData();\n            resetForm();\n            alert(\"Student Added Successfully!\\n\\nName: \".concat(student.name, \"\\nApplication Number: \").concat(applicationNumber, \"\\nPhone: \").concat(student.phone, \"\\n\\nPlease provide Application Number and Phone Number to the student for login.\\n\\nData saved in \").concat(storageInfo.mode, \" storage.\"));\n        } catch (error) {\n            alert(\"Error adding student. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleEditStudent = (student)=>{\n        setEditingStudent(student);\n        setNewStudent({\n            name: student.name,\n            phone: student.phone,\n            email: student.email || \"\",\n            class: student.class,\n            department: student.department || \"\",\n            schedule: student.schedule || \"\",\n            image: student.image_url || \"\"\n        });\n        setImagePreview(student.image_url || null);\n        setShowAddForm(false);\n    };\n    const handleUpdateStudent = async ()=>{\n        if (!validateForm() || !editingStudent) return;\n        // Check if phone number already exists (excluding current student)\n        const existingStudent = students.find((s)=>s.phone === newStudent.phone && s.id !== editingStudent.id);\n        if (existingStudent) {\n            alert(\"Phone number already exists!\");\n            return;\n        }\n        setLoading(true);\n        try {\n            await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.updateStudent(editingStudent.id, {\n                name: newStudent.name,\n                phone: newStudent.phone,\n                email: newStudent.email || null,\n                class: newStudent.class,\n                department: newStudent.department || null,\n                schedule: newStudent.schedule || null,\n                image_url: newStudent.image\n            });\n            await loadData();\n            resetForm();\n            alert(\"Student updated successfully!\\n\\nData saved in \".concat(storageInfo.mode, \" storage.\"));\n        } catch (error) {\n            alert(\"Error updating student. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDeleteStudent = async (student)=>{\n        if (confirm(\"Are you sure you want to delete \".concat(student.name, \"?\\n\\nThis action cannot be undone.\"))) {\n            try {\n                setLoading(true);\n                await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.deleteStudent(student.id);\n                await loadData();\n                alert(\"Student deleted successfully!\\n\\nData updated in \".concat(storageInfo.mode, \" storage.\"));\n            } catch (error) {\n                alert(\"Error deleting student. Please try again.\");\n            } finally{\n                setLoading(false);\n            }\n        }\n    };\n    const copyToClipboard = async (text, type)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n            setCopiedText(\"\".concat(type, \"-\").concat(text));\n            setTimeout(()=>setCopiedText(null), 2000);\n        } catch (error) {\n            alert(\"Failed to copy to clipboard\");\n        }\n    };\n    const resetForm = ()=>{\n        setNewStudent({\n            name: \"\",\n            phone: \"\",\n            email: \"\",\n            class: \"\",\n            department: \"\",\n            schedule: \"\",\n            image: \"\"\n        });\n        setImagePreview(null);\n        setImageFile(null);\n        setShowAddForm(false);\n        setEditingStudent(null);\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    const today = new Date().toISOString().slice(0, 10);\n    const totalStudents = students.length;\n    // Replace the following with your actual attendance/logs array if available\n    // For demonstration, using an empty array as placeholder\n    const logs = [] // Replace with actual logs source\n    ;\n    const todaysEntries = logs.filter((e)=>e.type === \"entry\" && e.timestamp.slice(0, 10) === today).length;\n    const todaysExits = logs.filter((e)=>e.type === \"exit\" && e.timestamp.slice(0, 10) === today).length;\n    const totalEntries = logs.filter((e)=>e.type === \"entry\").length;\n    const remainingStudents = totalStudents - todaysExits;\n    if (!isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 431,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-3xl\",\n                                            children: \"Admin Panel\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            className: \"text-lg\",\n                                            children: [\n                                                \"Student Management System - \",\n                                                storageInfo.mode,\n                                                \" Storage\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleRefresh,\n                                            variant: \"outline\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Refresh\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleLogout,\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Logout\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_13___default()), {\n                                            href: \"/\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                size: \"lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Home\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 439,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 438,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.Alert, {\n                    className: databaseConnected ? \"border-green-200 bg-green-50\" : \"border-yellow-200 bg-yellow-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"h-4 w-4 \".concat(databaseConnected ? \"text-green-600\" : \"text-yellow-600\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 469,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.AlertDescription, {\n                            className: databaseConnected ? \"text-green-800\" : \"text-yellow-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: [\n                                        storageInfo.mode,\n                                        \" Storage Active:\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 13\n                                }, this),\n                                \" \",\n                                databaseConnected ? \"Data syncs across all devices automatically\" : \"Data saved locally on this device (\".concat(storageInfo.studentsCount, \" students, \").concat(storageInfo.entriesCount, \" entries)\")\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 468,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"bg-blue-50 border-blue-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 sm:p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl sm:text-4xl font-bold text-blue-600 mb-2\",\n                                        children: stats.totalStudents\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm sm:text-base font-medium text-blue-700\",\n                                        children: \"Total Students\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-500 mt-1\",\n                                        children: \"Registered\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"bg-green-50 border-green-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 sm:p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl sm:text-4xl font-bold text-green-600 mb-2\",\n                                        children: stats.todayEntries\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm sm:text-base font-medium text-green-700\",\n                                        children: \"Total Entries\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-green-500 mt-1\",\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"bg-red-50 border-red-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 sm:p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl sm:text-4xl font-bold text-red-600 mb-2\",\n                                        children: stats.todayExits\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm sm:text-base font-medium text-red-700\",\n                                        children: \"Total Exits\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-red-500 mt-1\",\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 511,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"bg-purple-50 border-purple-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 sm:p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl sm:text-4xl font-bold text-purple-600 mb-2\",\n                                        children: stats.todayEntries + stats.todayExits\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm sm:text-base font-medium text-purple-700\",\n                                        children: \"Total Activity\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-purple-500 mt-1\",\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 526,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 479,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"inline-flex items-center gap-2 text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 544,\n                                columnNumber: 13\n                            }, this),\n                            \"Auto-refreshing every 5 seconds\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 543,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 542,\n                    columnNumber: 9\n                }, this),\n                !showAddForm && !editingStudent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>setShowAddForm(true),\n                            className: \"w-full h-16 text-lg\",\n                            disabled: loading,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"mr-2 h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 554,\n                                    columnNumber: 17\n                                }, this),\n                                \"Add New Student\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 553,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 552,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 551,\n                    columnNumber: 11\n                }, this),\n                (showAddForm || editingStudent) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: editingStudent ? \"Edit Student\" : \"Add New Student\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: [\n                                        editingStudent ? \"Update student information\" : \"Fill required fields to register a new student\",\n                                        \" - Data will be saved in \",\n                                        storageInfo.mode,\n                                        \" storage\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 566,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 564,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            className: \"text-base font-semibold\",\n                                            children: \"Student Photo *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 17\n                                        }, this),\n                                        imagePreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: imagePreview || \"/placeholder.svg\",\n                                                            alt: \"Student preview\",\n                                                            className: \"w-32 h-32 rounded-full border-4 border-blue-200 object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: removeImage,\n                                                            size: \"sm\",\n                                                            variant: \"destructive\",\n                                                            className: \"absolute -top-2 -right-2 h-6 w-6 rounded-full p-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 585,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-green-600\",\n                                                            children: \"✅ Photo uploaded successfully\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 595,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: ()=>{\n                                                                var _fileInputRef_current;\n                                                                return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                            },\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 597,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Change Photo\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 596,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-16 w-16 mx-auto text-gray-400 mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4\",\n                                                    children: \"Upload student photo (Required)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: ()=>{\n                                                                var _fileInputRef_current;\n                                                                return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                            },\n                                                            variant: \"outline\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 608,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Upload Photo\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 607,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: takePhoto,\n                                                            variant: \"outline\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 612,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Take Photo\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 606,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-2\",\n                                                    children: \"Supported formats: JPG, PNG, GIF (Max 5MB)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 616,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 603,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            ref: fileInputRef,\n                                            type: \"file\",\n                                            accept: \"image/*\",\n                                            onChange: handleImageSelect,\n                                            className: \"hidden\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_9__.Separator, {}, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 630,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"name\",\n                                                    children: \"Student Name *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 635,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"name\",\n                                                    value: newStudent.name,\n                                                    onChange: (e)=>setNewStudent({\n                                                            ...newStudent,\n                                                            name: e.target.value\n                                                        }),\n                                                    placeholder: \"Enter full name\",\n                                                    disabled: loading\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"phone\",\n                                                    children: \"Phone Number *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 645,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"phone\",\n                                                    value: newStudent.phone,\n                                                    onChange: (e)=>setNewStudent({\n                                                            ...newStudent,\n                                                            phone: e.target.value\n                                                        }),\n                                                    placeholder: \"10-digit phone number\",\n                                                    maxLength: 10,\n                                                    disabled: loading\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 646,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 644,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"email\",\n                                                    children: \"Email Address\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 656,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"email\",\n                                                    type: \"email\",\n                                                    value: newStudent.email,\n                                                    onChange: (e)=>setNewStudent({\n                                                            ...newStudent,\n                                                            email: e.target.value\n                                                        }),\n                                                    placeholder: \"<EMAIL>\",\n                                                    disabled: loading\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 657,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"class\",\n                                                    children: \"Class *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 667,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: newStudent.class,\n                                                    onValueChange: (value)=>setNewStudent({\n                                                            ...newStudent,\n                                                            class: value\n                                                        }),\n                                                    disabled: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select class\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 674,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 673,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"10th-A\",\n                                                                    children: \"10th A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 677,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"10th-B\",\n                                                                    children: \"10th B\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 678,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"10th-C\",\n                                                                    children: \"10th C\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 679,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"11th-A\",\n                                                                    children: \"11th A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 680,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"11th-B\",\n                                                                    children: \"11th B\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 681,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"11th-C\",\n                                                                    children: \"11th C\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 682,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"12th-A\",\n                                                                    children: \"12th A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 683,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"12th-B\",\n                                                                    children: \"12th B\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 684,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"12th-C\",\n                                                                    children: \"12th C\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 685,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 676,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 668,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 666,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"department\",\n                                                    children: \"Department\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: newStudent.department,\n                                                    onValueChange: (value)=>setNewStudent({\n                                                            ...newStudent,\n                                                            department: value\n                                                        }),\n                                                    disabled: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select department\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 697,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 696,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Science\",\n                                                                    children: \"Science\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 700,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Commerce\",\n                                                                    children: \"Commerce\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 701,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Arts\",\n                                                                    children: \"Arts\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 702,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Computer Science\",\n                                                                    children: \"Computer Science\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 703,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 691,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 689,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"schedule\",\n                                                    children: \"Time Schedule\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 708,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: newStudent.schedule,\n                                                    onValueChange: (value)=>setNewStudent({\n                                                            ...newStudent,\n                                                            schedule: value\n                                                        }),\n                                                    disabled: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select schedule\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 715,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 714,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Morning Shift (8:00 AM - 2:00 PM)\",\n                                                                    children: \"Morning Shift (8:00 AM - 2:00 PM)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 718,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Afternoon Shift (2:00 PM - 8:00 PM)\",\n                                                                    children: \"Afternoon Shift (2:00 PM - 8:00 PM)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 721,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Full Day (8:00 AM - 4:00 PM)\",\n                                                                    children: \"Full Day (8:00 AM - 4:00 PM)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 724,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 717,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 709,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 707,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 633,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_9__.Separator, {}, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 730,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        editingStudent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleUpdateStudent,\n                                            className: \"flex-1\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 735,\n                                                    columnNumber: 21\n                                                }, this),\n                                                loading ? \"Updating...\" : \"Update Student\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 734,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleAddStudent,\n                                            className: \"flex-1\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 740,\n                                                    columnNumber: 21\n                                                }, this),\n                                                loading ? \"Adding...\" : \"Add Student\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 739,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: resetForm,\n                                            variant: \"outline\",\n                                            className: \"flex-1 bg-transparent\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 745,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Cancel\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 744,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 732,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 571,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 563,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: [\n                                        \"Registered Students (\",\n                                        students.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 756,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: [\n                                        \"All registered students with their login credentials - Stored in \",\n                                        storageInfo.mode,\n                                        \" storage\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 757,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 755,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: students.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"h-16 w-16 mx-auto text-gray-300 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 764,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-500 mb-2\",\n                                        children: \"No students registered yet\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 765,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: 'Click \"Add New Student\" to get started'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 766,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 763,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: students.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: student.image_url || \"/placeholder.svg?height=60&width=60\",\n                                                        alt: student.name,\n                                                        className: \"w-12 h-12 rounded-full border-2 border-gray-200 object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 773,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-lg\",\n                                                                children: student.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 779,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: [\n                                                                    student.class,\n                                                                    \" \",\n                                                                    student.department && \"- \".concat(student.department)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 780,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: student.phone\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 783,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            student.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: student.email\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 784,\n                                                                columnNumber: 43\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 778,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 772,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"font-mono text-xs\",\n                                                                        children: [\n                                                                            \"App: \",\n                                                                            student.application_number\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 792,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"ghost\",\n                                                                        onClick: ()=>copyToClipboard(student.application_number, \"app\"),\n                                                                        className: \"h-6 w-6 p-0\",\n                                                                        children: copiedText === \"app-\".concat(student.application_number) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 802,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 804,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 795,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 791,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                        variant: \"secondary\",\n                                                                        className: \"text-xs\",\n                                                                        children: [\n                                                                            \"Phone: \",\n                                                                            student.phone\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 809,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"ghost\",\n                                                                        onClick: ()=>copyToClipboard(student.phone, \"phone\"),\n                                                                        className: \"h-6 w-6 p-0\",\n                                                                        children: copiedText === \"phone-\".concat(student.phone) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 819,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 821,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 812,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 808,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 790,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleEditStudent(student),\n                                                                disabled: loading,\n                                                                className: \"h-8 w-8 p-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 836,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 829,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"destructive\",\n                                                                onClick: ()=>handleDeleteStudent(student),\n                                                                disabled: loading,\n                                                                className: \"h-8 w-8 p-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 845,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 838,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 828,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 788,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, student.id, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 771,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 769,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 761,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 754,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: [\n                                    \"Admin Instructions - \",\n                                    storageInfo.mode,\n                                    \" Storage\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 859,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 858,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-blue-700 mb-2\",\n                                                children: \"Required Fields:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 864,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Student Name (Full name required)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 866,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Phone Number (10 digits, unique)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 867,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Class Selection (from dropdown)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 868,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Student Photo (Upload or camera)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 869,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCDD Email (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 870,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCDD Department (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 871,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCDD Schedule (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 872,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 865,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 863,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-green-700 mb-2\",\n                                                children: \"Photo Requirements:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 876,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Clear face photo required\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 878,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 JPG, PNG, GIF formats supported\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 879,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Maximum file size: 5MB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 880,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Upload from device or take with camera\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 881,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Used for face verification at station\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 882,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Can be changed during editing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 883,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 877,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 875,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 862,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 861,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 857,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 436,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 435,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPanel, \"0IW6c6jiidB9toBW0keIqCX24kc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AdminPanel;\nfunction StatCard(param) {\n    let { icon, value, label, color } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow p-6 flex items-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-\".concat(color, \"-500 text-3xl mr-4\"),\n                children: icon\n            }, void 0, false, {\n                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 897,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-2xl font-bold\",\n                        children: value\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 899,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600\",\n                        children: label\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 900,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 898,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 896,\n        columnNumber: 5\n    }, this);\n}\n_c1 = StatCard;\nconst UserIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n        className: \"h-6 w-6 text-blue-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 906,\n        columnNumber: 24\n    }, undefined);\n_c2 = UserIcon;\nconst EntryIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n        className: \"h-6 w-6 text-green-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 907,\n        columnNumber: 25\n    }, undefined);\n_c3 = EntryIcon;\nconst ExitIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n        className: \"h-6 w-6 text-red-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 908,\n        columnNumber: 24\n    }, undefined);\n_c4 = ExitIcon;\nconst TotalIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n        className: \"h-6 w-6 text-purple-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 909,\n        columnNumber: 25\n    }, undefined);\n_c5 = TotalIcon;\nconst RemainIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n        className: \"h-6 w-6 text-orange-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 910,\n        columnNumber: 26\n    }, undefined);\n_c6 = RemainIcon;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"AdminPanel\");\n$RefreshReg$(_c1, \"StatCard\");\n$RefreshReg$(_c2, \"UserIcon\");\n$RefreshReg$(_c3, \"EntryIcon\");\n$RefreshReg$(_c4, \"ExitIcon\");\n$RefreshReg$(_c5, \"TotalIcon\");\n$RefreshReg$(_c6, \"RemainIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/page.tsx\n"));

/***/ })

});