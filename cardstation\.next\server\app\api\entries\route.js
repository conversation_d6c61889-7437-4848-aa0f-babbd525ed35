/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/entries/route";
exports.ids = ["app/api/entries/route"];
exports.modules = {

/***/ "(rsc)/./app/api/entries/route.ts":
/*!**********************************!*\
  !*** ./app/api/entries/route.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./lib/mongodb.ts\");\n\n\nasync function GET(req) {\n    try {\n        const url = new URL(req.url);\n        const studentId = url.searchParams.get(\"student_id\");\n        if (!_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) {\n            // MongoDB not available, return empty array - no test data\n            console.log(\"MongoDB not available, returning empty entries array\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json([]);\n        }\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        if (!client) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json([]);\n        }\n        const db = client.db(\"idcard\");\n        const entries = db.collection(\"entry_logs\");\n        const query = {};\n        if (studentId) query.student_id = studentId;\n        const results = await entries.find(query).sort({\n            entry_time: -1\n        }).toArray();\n        const data = results.map((e)=>({\n                ...e,\n                id: e._id.toString()\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    // MongoDB code commented out for testing - uncomment when MongoDB is available\n    /*\r\n    const client = await clientPromise\r\n    if (!client) {\r\n      return NextResponse.json([])\r\n    }\r\n\r\n    const db = client.db(\"idcard\")\r\n    const entries = db.collection(\"entry_logs\")\r\n\r\n    const query: any = {}\r\n    if (studentId) query.student_id = studentId\r\n\r\n    const results = await entries.find(query).sort({ entry_time: -1 }).toArray()\r\n    const data = results.map((e) => ({\r\n      ...e,\r\n      id: e._id.toString(),\r\n    }))\r\n    return NextResponse.json(data)\r\n    */ } catch (error) {\n        console.error(\"GET /api/entries error:\", error);\n        // Return empty array - no test data\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json([]);\n    }\n}\nasync function POST(req) {\n    try {\n        const body = await req.json();\n        console.log(\"Received entry body:\", body);\n        if (!_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Database not available\"\n            }, {\n                status: 503\n            });\n        }\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        if (!client) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Database connection failed\"\n            }, {\n                status: 503\n            });\n        }\n        const db = client.db(\"idcard\");\n        const entries = db.collection(\"entry_logs\");\n        // Check if student is already inside (has entry today without exit)\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        const tomorrow = new Date(today);\n        tomorrow.setDate(tomorrow.getDate() + 1);\n        const existingEntry = await entries.findOne({\n            student_id: body.student_id,\n            entry_time: {\n                $gte: today,\n                $lt: tomorrow\n            },\n            exit_time: null\n        });\n        if (existingEntry) {\n            // Student is inside, mark exit\n            const result = await entries.findOneAndUpdate({\n                _id: existingEntry._id\n            }, {\n                $set: {\n                    exit_time: new Date(),\n                    status: \"exit\",\n                    updated_at: new Date()\n                }\n            }, {\n                returnDocument: \"after\"\n            });\n            if (!result || !result.value) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Failed to update entry\"\n                }, {\n                    status: 500\n                });\n            }\n            console.log(\"Exit recorded for student:\", body.student_name);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                ...result.value,\n                id: result.value._id.toString()\n            });\n        } else {\n            // New entry with enhanced verification data\n            const newEntry = {\n                ...body,\n                entry_time: new Date(),\n                status: \"entry\",\n                verified: true,\n                verification_method: body.verification_method || \"qr_and_face\",\n                face_match_score: body.face_match_score || null,\n                qr_validated: body.qr_validated !== undefined ? body.qr_validated : true,\n                verification_timestamp: body.verification_timestamp || new Date().toISOString(),\n                station_id: body.station_id || \"main_entrance\",\n                created_at: new Date(),\n                updated_at: new Date()\n            };\n            const result = await entries.insertOne(newEntry);\n            console.log(\"Entry recorded for student:\", body.student_name);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                ...newEntry,\n                id: result.insertedId.toString()\n            }, {\n                status: 201\n            });\n        }\n    } catch (error) {\n        console.error(\"POST /api/entries error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to add entry\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/entries/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/mongodb.ts":
/*!************************!*\
  !*** ./lib/mongodb.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_0__);\n\nconst uri = process.env.MONGODB_URI || \"mongodb://localhost:27017/idcard\";\nconst options = {};\nlet client = null;\nlet clientPromise = null;\ntry {\n    if (process.env.MONGODB_URI) {\n        client = new mongodb__WEBPACK_IMPORTED_MODULE_0__.MongoClient(uri, options);\n        clientPromise = client.connect();\n    }\n} catch (error) {\n    console.warn(\"MongoDB connection failed, will use local storage:\", error);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (clientPromise);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvbW9uZ29kYi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBcUM7QUFFckMsTUFBTUMsTUFBTUMsUUFBUUMsR0FBRyxDQUFDQyxXQUFXLElBQUk7QUFDdkMsTUFBTUMsVUFBVSxDQUFDO0FBRWpCLElBQUlDLFNBQTZCO0FBQ2pDLElBQUlDLGdCQUE2QztBQUVqRCxJQUFJO0lBQ0YsSUFBSUwsUUFBUUMsR0FBRyxDQUFDQyxXQUFXLEVBQUU7UUFDM0JFLFNBQVMsSUFBSU4sZ0RBQVdBLENBQUNDLEtBQUtJO1FBQzlCRSxnQkFBZ0JELE9BQU9FLE9BQU87SUFDaEM7QUFDRixFQUFFLE9BQU9DLE9BQU87SUFDZEMsUUFBUUMsSUFBSSxDQUFDLHNEQUFzREY7QUFDckU7QUFFQSxpRUFBZUYsYUFBYUEsRUFBQSIsInNvdXJjZXMiOlsiRDpcXGlkY2FyZFxcY2FyZHN0YXRpb25cXGxpYlxcbW9uZ29kYi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBNb25nb0NsaWVudCB9IGZyb20gXCJtb25nb2RiXCJcclxuXHJcbmNvbnN0IHVyaSA9IHByb2Nlc3MuZW52Lk1PTkdPREJfVVJJIHx8IFwibW9uZ29kYjovL2xvY2FsaG9zdDoyNzAxNy9pZGNhcmRcIlxyXG5jb25zdCBvcHRpb25zID0ge31cclxuXHJcbmxldCBjbGllbnQ6IE1vbmdvQ2xpZW50IHwgbnVsbCA9IG51bGxcclxubGV0IGNsaWVudFByb21pc2U6IFByb21pc2U8TW9uZ29DbGllbnQ+IHwgbnVsbCA9IG51bGxcclxuXHJcbnRyeSB7XHJcbiAgaWYgKHByb2Nlc3MuZW52Lk1PTkdPREJfVVJJKSB7XHJcbiAgICBjbGllbnQgPSBuZXcgTW9uZ29DbGllbnQodXJpLCBvcHRpb25zKVxyXG4gICAgY2xpZW50UHJvbWlzZSA9IGNsaWVudC5jb25uZWN0KClcclxuICB9XHJcbn0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgY29uc29sZS53YXJuKFwiTW9uZ29EQiBjb25uZWN0aW9uIGZhaWxlZCwgd2lsbCB1c2UgbG9jYWwgc3RvcmFnZTpcIiwgZXJyb3IpXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGNsaWVudFByb21pc2UiXSwibmFtZXMiOlsiTW9uZ29DbGllbnQiLCJ1cmkiLCJwcm9jZXNzIiwiZW52IiwiTU9OR09EQl9VUkkiLCJvcHRpb25zIiwiY2xpZW50IiwiY2xpZW50UHJvbWlzZSIsImNvbm5lY3QiLCJlcnJvciIsImNvbnNvbGUiLCJ3YXJuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fentries%2Froute&page=%2Fapi%2Fentries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fentries%2Froute.ts&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fentries%2Froute&page=%2Fapi%2Fentries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fentries%2Froute.ts&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_idcard_cardstation_app_api_entries_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/entries/route.ts */ \"(rsc)/./app/api/entries/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/entries/route\",\n        pathname: \"/api/entries\",\n        filename: \"route\",\n        bundlePath: \"app/api/entries/route\"\n    },\n    resolvedPagePath: \"D:\\\\idcard\\\\cardstation\\\\app\\\\api\\\\entries\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_idcard_cardstation_app_api_entries_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fentries%2Froute&page=%2Fapi%2Fentries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fentries%2Froute.ts&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "mongodb":
/*!**************************!*\
  !*** external "mongodb" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongodb");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fentries%2Froute&page=%2Fapi%2Fentries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fentries%2Froute.ts&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();