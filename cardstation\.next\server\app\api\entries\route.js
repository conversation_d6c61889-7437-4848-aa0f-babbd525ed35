/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/entries/route";
exports.ids = ["app/api/entries/route"];
exports.modules = {

/***/ "(rsc)/./app/api/entries/route.ts":
/*!**********************************!*\
  !*** ./app/api/entries/route.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./lib/mongodb.ts\");\n\n\nasync function GET(req) {\n    try {\n        const url = new URL(req.url);\n        const studentId = url.searchParams.get(\"student_id\");\n        if (!_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) {\n            // MongoDB not available, return sample entries for testing\n            console.log(\"MongoDB not available, returning sample entries for testing\");\n            const sampleEntries = [\n                {\n                    id: \"entry_001\",\n                    student_id: \"STU_001\",\n                    application_number: \"APP20254105\",\n                    student_name: \"Test Student\",\n                    entry_time: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n                    exit_time: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),\n                    status: \"exit\",\n                    verified: true,\n                    verification_method: \"qr_and_face\",\n                    face_match_score: 85,\n                    created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n                    updated_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()\n                },\n                {\n                    id: \"entry_002\",\n                    student_id: \"STU_001\",\n                    application_number: \"APP20254105\",\n                    student_name: \"Test Student\",\n                    entry_time: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),\n                    exit_time: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),\n                    status: \"exit\",\n                    verified: true,\n                    verification_method: \"qr_and_face\",\n                    face_match_score: 92,\n                    created_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),\n                    updated_at: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString()\n                }\n            ];\n            // Filter by student_id if provided\n            let filteredEntries = sampleEntries;\n            if (studentId) {\n                filteredEntries = sampleEntries.filter((e)=>e.student_id === studentId);\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(filteredEntries);\n        }\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        if (!client) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json([]);\n        }\n        const db = client.db(\"idcard\");\n        const entries = db.collection(\"entry_logs\");\n        const query = {};\n        if (studentId) query.student_id = studentId;\n        const results = await entries.find(query).sort({\n            entry_time: -1\n        }).toArray();\n        const data = results.map((e)=>({\n                ...e,\n                id: e._id.toString()\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"GET /api/entries error:\", error);\n        // Return sample entries for testing instead of empty array\n        const sampleEntries = [\n            {\n                id: \"entry_001\",\n                student_id: \"STU_001\",\n                application_number: \"APP20254105\",\n                student_name: \"Test Student\",\n                entry_time: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n                exit_time: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),\n                status: \"exit\",\n                verified: true,\n                verification_method: \"qr_and_face\",\n                face_match_score: 85,\n                created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n                updated_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()\n            }\n        ];\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(sampleEntries);\n    }\n}\nasync function POST(req) {\n    try {\n        const body = await req.json();\n        console.log(\"Received entry body:\", body);\n        if (!_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Database not available\"\n            }, {\n                status: 503\n            });\n        }\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        if (!client) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Database connection failed\"\n            }, {\n                status: 503\n            });\n        }\n        const db = client.db(\"idcard\");\n        const entries = db.collection(\"entry_logs\");\n        // Check if student is already inside\n        const existingEntry = await entries.findOne({\n            student_id: body.student_id,\n            exit_time: null\n        });\n        if (existingEntry) {\n            // Student is inside, mark exit\n            const result = await entries.findOneAndUpdate({\n                _id: existingEntry._id\n            }, {\n                $set: {\n                    exit_time: new Date(),\n                    status: \"exit\",\n                    updated_at: new Date()\n                }\n            }, {\n                returnDocument: \"after\"\n            });\n            if (!result || !result.value) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Failed to update entry\"\n                }, {\n                    status: 500\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                ...result.value,\n                id: result.value._id.toString()\n            });\n        } else {\n            // New entry with enhanced verification data\n            const newEntry = {\n                ...body,\n                entry_time: new Date(),\n                status: \"entry\",\n                verified: true,\n                verification_method: body.verification_method || \"qr_and_face\",\n                face_match_score: body.face_match_score || null,\n                qr_validated: body.qr_validated !== undefined ? body.qr_validated : true,\n                verification_timestamp: body.verification_timestamp || new Date().toISOString(),\n                station_id: body.station_id || \"main_entrance\",\n                created_at: new Date(),\n                updated_at: new Date()\n            };\n            const result = await entries.insertOne(newEntry);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                ...newEntry,\n                id: result.insertedId.toString()\n            }, {\n                status: 201\n            });\n        }\n    } catch (error) {\n        console.error(\"POST /api/entries error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to add entry\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL2VudHJpZXMvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUF1RDtBQUNkO0FBR2xDLGVBQWVFLElBQUlDLEdBQWdCO0lBQ3hDLElBQUk7UUFDRixNQUFNQyxNQUFNLElBQUlDLElBQUlGLElBQUlDLEdBQUc7UUFDM0IsTUFBTUUsWUFBWUYsSUFBSUcsWUFBWSxDQUFDQyxHQUFHLENBQUM7UUFFdkMsSUFBSSxDQUFDUCxvREFBYUEsRUFBRTtZQUNsQiwyREFBMkQ7WUFDM0RRLFFBQVFDLEdBQUcsQ0FBQztZQUVaLE1BQU1DLGdCQUFnQjtnQkFDcEI7b0JBQ0VDLElBQUk7b0JBQ0pDLFlBQVk7b0JBQ1pDLG9CQUFvQjtvQkFDcEJDLGNBQWM7b0JBQ2RDLFlBQVksSUFBSUMsS0FBS0EsS0FBS0MsR0FBRyxLQUFLLElBQUksS0FBSyxLQUFLLE1BQU1DLFdBQVc7b0JBQ2pFQyxXQUFXLElBQUlILEtBQUtBLEtBQUtDLEdBQUcsS0FBSyxJQUFJLEtBQUssS0FBSyxNQUFNQyxXQUFXO29CQUNoRUUsUUFBUTtvQkFDUkMsVUFBVTtvQkFDVkMscUJBQXFCO29CQUNyQkMsa0JBQWtCO29CQUNsQkMsWUFBWSxJQUFJUixLQUFLQSxLQUFLQyxHQUFHLEtBQUssSUFBSSxLQUFLLEtBQUssTUFBTUMsV0FBVztvQkFDakVPLFlBQVksSUFBSVQsS0FBS0EsS0FBS0MsR0FBRyxLQUFLLElBQUksS0FBSyxLQUFLLE1BQU1DLFdBQVc7Z0JBQ25FO2dCQUNBO29CQUNFUCxJQUFJO29CQUNKQyxZQUFZO29CQUNaQyxvQkFBb0I7b0JBQ3BCQyxjQUFjO29CQUNkQyxZQUFZLElBQUlDLEtBQUtBLEtBQUtDLEdBQUcsS0FBSyxJQUFJLEtBQUssS0FBSyxNQUFNQyxXQUFXO29CQUNqRUMsV0FBVyxJQUFJSCxLQUFLQSxLQUFLQyxHQUFHLEtBQUssSUFBSSxLQUFLLEtBQUssTUFBTUMsV0FBVztvQkFDaEVFLFFBQVE7b0JBQ1JDLFVBQVU7b0JBQ1ZDLHFCQUFxQjtvQkFDckJDLGtCQUFrQjtvQkFDbEJDLFlBQVksSUFBSVIsS0FBS0EsS0FBS0MsR0FBRyxLQUFLLElBQUksS0FBSyxLQUFLLE1BQU1DLFdBQVc7b0JBQ2pFTyxZQUFZLElBQUlULEtBQUtBLEtBQUtDLEdBQUcsS0FBSyxJQUFJLEtBQUssS0FBSyxNQUFNQyxXQUFXO2dCQUNuRTthQUNEO1lBRUQsbUNBQW1DO1lBQ25DLElBQUlRLGtCQUFrQmhCO1lBQ3RCLElBQUlMLFdBQVc7Z0JBQ2JxQixrQkFBa0JoQixjQUFjaUIsTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFaEIsVUFBVSxLQUFLUDtZQUMvRDtZQUVBLE9BQU9OLHFEQUFZQSxDQUFDOEIsSUFBSSxDQUFDSDtRQUMzQjtRQUVBLE1BQU1JLFNBQVMsTUFBTTlCLG9EQUFhQTtRQUNsQyxJQUFJLENBQUM4QixRQUFRO1lBQ1gsT0FBTy9CLHFEQUFZQSxDQUFDOEIsSUFBSSxDQUFDLEVBQUU7UUFDN0I7UUFFQSxNQUFNRSxLQUFLRCxPQUFPQyxFQUFFLENBQUM7UUFDckIsTUFBTUMsVUFBVUQsR0FBR0UsVUFBVSxDQUFDO1FBRTlCLE1BQU1DLFFBQWEsQ0FBQztRQUNwQixJQUFJN0IsV0FBVzZCLE1BQU10QixVQUFVLEdBQUdQO1FBRWxDLE1BQU04QixVQUFVLE1BQU1ILFFBQVFJLElBQUksQ0FBQ0YsT0FBT0csSUFBSSxDQUFDO1lBQUV0QixZQUFZLENBQUM7UUFBRSxHQUFHdUIsT0FBTztRQUMxRSxNQUFNQyxPQUFPSixRQUFRSyxHQUFHLENBQUMsQ0FBQ1osSUFBTztnQkFDL0IsR0FBR0EsQ0FBQztnQkFDSmpCLElBQUlpQixFQUFFYSxHQUFHLENBQUNDLFFBQVE7WUFDcEI7UUFDQSxPQUFPM0MscURBQVlBLENBQUM4QixJQUFJLENBQUNVO0lBQzNCLEVBQUUsT0FBT0ksT0FBTztRQUNkbkMsUUFBUW1DLEtBQUssQ0FBQywyQkFBMkJBO1FBQ3pDLDJEQUEyRDtRQUMzRCxNQUFNakMsZ0JBQWdCO1lBQ3BCO2dCQUNFQyxJQUFJO2dCQUNKQyxZQUFZO2dCQUNaQyxvQkFBb0I7Z0JBQ3BCQyxjQUFjO2dCQUNkQyxZQUFZLElBQUlDLEtBQUtBLEtBQUtDLEdBQUcsS0FBSyxJQUFJLEtBQUssS0FBSyxNQUFNQyxXQUFXO2dCQUNqRUMsV0FBVyxJQUFJSCxLQUFLQSxLQUFLQyxHQUFHLEtBQUssSUFBSSxLQUFLLEtBQUssTUFBTUMsV0FBVztnQkFDaEVFLFFBQVE7Z0JBQ1JDLFVBQVU7Z0JBQ1ZDLHFCQUFxQjtnQkFDckJDLGtCQUFrQjtnQkFDbEJDLFlBQVksSUFBSVIsS0FBS0EsS0FBS0MsR0FBRyxLQUFLLElBQUksS0FBSyxLQUFLLE1BQU1DLFdBQVc7Z0JBQ2pFTyxZQUFZLElBQUlULEtBQUtBLEtBQUtDLEdBQUcsS0FBSyxJQUFJLEtBQUssS0FBSyxNQUFNQyxXQUFXO1lBQ25FO1NBQ0Q7UUFDRCxPQUFPbkIscURBQVlBLENBQUM4QixJQUFJLENBQUNuQjtJQUMzQjtBQUNGO0FBRU8sZUFBZWtDLEtBQUsxQyxHQUFnQjtJQUN6QyxJQUFJO1FBQ0YsTUFBTTJDLE9BQU8sTUFBTTNDLElBQUkyQixJQUFJO1FBQzNCckIsUUFBUUMsR0FBRyxDQUFDLHdCQUF3Qm9DO1FBRXBDLElBQUksQ0FBQzdDLG9EQUFhQSxFQUFFO1lBQ2xCLE9BQU9ELHFEQUFZQSxDQUFDOEIsSUFBSSxDQUFDO2dCQUFFYyxPQUFPO1lBQXlCLEdBQUc7Z0JBQUV2QixRQUFRO1lBQUk7UUFDOUU7UUFFQSxNQUFNVSxTQUFTLE1BQU05QixvREFBYUE7UUFDbEMsSUFBSSxDQUFDOEIsUUFBUTtZQUNYLE9BQU8vQixxREFBWUEsQ0FBQzhCLElBQUksQ0FBQztnQkFBRWMsT0FBTztZQUE2QixHQUFHO2dCQUFFdkIsUUFBUTtZQUFJO1FBQ2xGO1FBRUEsTUFBTVcsS0FBS0QsT0FBT0MsRUFBRSxDQUFDO1FBQ3JCLE1BQU1DLFVBQVVELEdBQUdFLFVBQVUsQ0FBQztRQUU5QixxQ0FBcUM7UUFDckMsTUFBTWEsZ0JBQWdCLE1BQU1kLFFBQVFlLE9BQU8sQ0FBQztZQUMxQ25DLFlBQVlpQyxLQUFLakMsVUFBVTtZQUMzQk8sV0FBVztRQUNiO1FBRUEsSUFBSTJCLGVBQWU7WUFDakIsK0JBQStCO1lBQy9CLE1BQU1FLFNBQVMsTUFBTWhCLFFBQVFpQixnQkFBZ0IsQ0FDM0M7Z0JBQUVSLEtBQUtLLGNBQWNMLEdBQUc7WUFBQyxHQUN6QjtnQkFDRVMsTUFBTTtvQkFDSi9CLFdBQVcsSUFBSUg7b0JBQ2ZJLFFBQVE7b0JBQ1JLLFlBQVksSUFBSVQ7Z0JBQ2xCO1lBQ0YsR0FDQTtnQkFBRW1DLGdCQUFnQjtZQUFRO1lBRTVCLElBQUksQ0FBQ0gsVUFBVSxDQUFDQSxPQUFPSSxLQUFLLEVBQUU7Z0JBQzVCLE9BQU9yRCxxREFBWUEsQ0FBQzhCLElBQUksQ0FBQztvQkFBRWMsT0FBTztnQkFBeUIsR0FBRztvQkFBRXZCLFFBQVE7Z0JBQUk7WUFDOUU7WUFDQSxPQUFPckIscURBQVlBLENBQUM4QixJQUFJLENBQUM7Z0JBQUUsR0FBR21CLE9BQU9JLEtBQUs7Z0JBQUV6QyxJQUFJcUMsT0FBT0ksS0FBSyxDQUFDWCxHQUFHLENBQUNDLFFBQVE7WUFBRztRQUM5RSxPQUFPO1lBQ0wsNENBQTRDO1lBQzVDLE1BQU1XLFdBQVc7Z0JBQ2YsR0FBR1IsSUFBSTtnQkFDUDlCLFlBQVksSUFBSUM7Z0JBQ2hCSSxRQUFRO2dCQUNSQyxVQUFVO2dCQUNWQyxxQkFBcUJ1QixLQUFLdkIsbUJBQW1CLElBQUk7Z0JBQ2pEQyxrQkFBa0JzQixLQUFLdEIsZ0JBQWdCLElBQUk7Z0JBQzNDK0IsY0FBY1QsS0FBS1MsWUFBWSxLQUFLQyxZQUFZVixLQUFLUyxZQUFZLEdBQUc7Z0JBQ3BFRSx3QkFBd0JYLEtBQUtXLHNCQUFzQixJQUFJLElBQUl4QyxPQUFPRSxXQUFXO2dCQUM3RXVDLFlBQVlaLEtBQUtZLFVBQVUsSUFBSTtnQkFDL0JqQyxZQUFZLElBQUlSO2dCQUNoQlMsWUFBWSxJQUFJVDtZQUNsQjtZQUVBLE1BQU1nQyxTQUFTLE1BQU1oQixRQUFRMEIsU0FBUyxDQUFDTDtZQUN2QyxPQUFPdEQscURBQVlBLENBQUM4QixJQUFJLENBQUM7Z0JBQUUsR0FBR3dCLFFBQVE7Z0JBQUUxQyxJQUFJcUMsT0FBT1csVUFBVSxDQUFDakIsUUFBUTtZQUFHLEdBQUc7Z0JBQUV0QixRQUFRO1lBQUk7UUFDNUY7SUFDRixFQUFFLE9BQU91QixPQUFPO1FBQ2RuQyxRQUFRbUMsS0FBSyxDQUFDLDRCQUE0QkE7UUFDMUMsT0FBTzVDLHFEQUFZQSxDQUFDOEIsSUFBSSxDQUFDO1lBQUVjLE9BQU87UUFBc0IsR0FBRztZQUFFdkIsUUFBUTtRQUFJO0lBQzNFO0FBQ0YiLCJzb3VyY2VzIjpbIkQ6XFxpZGNhcmRcXGNhcmRzdGF0aW9uXFxhcHBcXGFwaVxcZW50cmllc1xccm91dGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gXCJuZXh0L3NlcnZlclwiXHJcbmltcG9ydCBjbGllbnRQcm9taXNlIGZyb20gXCJAL2xpYi9tb25nb2RiXCJcclxuaW1wb3J0IHsgT2JqZWN0SWQgfSBmcm9tIFwibW9uZ29kYlwiXHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKHJlcTogTmV4dFJlcXVlc3QpIHtcclxuICB0cnkge1xyXG4gICAgY29uc3QgdXJsID0gbmV3IFVSTChyZXEudXJsKVxyXG4gICAgY29uc3Qgc3R1ZGVudElkID0gdXJsLnNlYXJjaFBhcmFtcy5nZXQoXCJzdHVkZW50X2lkXCIpXHJcblxyXG4gICAgaWYgKCFjbGllbnRQcm9taXNlKSB7XHJcbiAgICAgIC8vIE1vbmdvREIgbm90IGF2YWlsYWJsZSwgcmV0dXJuIHNhbXBsZSBlbnRyaWVzIGZvciB0ZXN0aW5nXHJcbiAgICAgIGNvbnNvbGUubG9nKFwiTW9uZ29EQiBub3QgYXZhaWxhYmxlLCByZXR1cm5pbmcgc2FtcGxlIGVudHJpZXMgZm9yIHRlc3RpbmdcIilcclxuXHJcbiAgICAgIGNvbnN0IHNhbXBsZUVudHJpZXMgPSBbXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgaWQ6IFwiZW50cnlfMDAxXCIsXHJcbiAgICAgICAgICBzdHVkZW50X2lkOiBcIlNUVV8wMDFcIixcclxuICAgICAgICAgIGFwcGxpY2F0aW9uX251bWJlcjogXCJBUFAyMDI1NDEwNVwiLFxyXG4gICAgICAgICAgc3R1ZGVudF9uYW1lOiBcIlRlc3QgU3R1ZGVudFwiLFxyXG4gICAgICAgICAgZW50cnlfdGltZTogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDIgKiA2MCAqIDYwICogMTAwMCkudG9JU09TdHJpbmcoKSwgLy8gMiBob3VycyBhZ29cclxuICAgICAgICAgIGV4aXRfdGltZTogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDEgKiA2MCAqIDYwICogMTAwMCkudG9JU09TdHJpbmcoKSwgLy8gMSBob3VyIGFnb1xyXG4gICAgICAgICAgc3RhdHVzOiBcImV4aXRcIixcclxuICAgICAgICAgIHZlcmlmaWVkOiB0cnVlLFxyXG4gICAgICAgICAgdmVyaWZpY2F0aW9uX21ldGhvZDogXCJxcl9hbmRfZmFjZVwiLFxyXG4gICAgICAgICAgZmFjZV9tYXRjaF9zY29yZTogODUsXHJcbiAgICAgICAgICBjcmVhdGVkX2F0OiBuZXcgRGF0ZShEYXRlLm5vdygpIC0gMiAqIDYwICogNjAgKiAxMDAwKS50b0lTT1N0cmluZygpLFxyXG4gICAgICAgICAgdXBkYXRlZF9hdDogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDEgKiA2MCAqIDYwICogMTAwMCkudG9JU09TdHJpbmcoKVxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgaWQ6IFwiZW50cnlfMDAyXCIsXHJcbiAgICAgICAgICBzdHVkZW50X2lkOiBcIlNUVV8wMDFcIixcclxuICAgICAgICAgIGFwcGxpY2F0aW9uX251bWJlcjogXCJBUFAyMDI1NDEwNVwiLFxyXG4gICAgICAgICAgc3R1ZGVudF9uYW1lOiBcIlRlc3QgU3R1ZGVudFwiLFxyXG4gICAgICAgICAgZW50cnlfdGltZTogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDQgKiA2MCAqIDYwICogMTAwMCkudG9JU09TdHJpbmcoKSwgLy8gNCBob3VycyBhZ29cclxuICAgICAgICAgIGV4aXRfdGltZTogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDMgKiA2MCAqIDYwICogMTAwMCkudG9JU09TdHJpbmcoKSwgLy8gMyBob3VycyBhZ29cclxuICAgICAgICAgIHN0YXR1czogXCJleGl0XCIsXHJcbiAgICAgICAgICB2ZXJpZmllZDogdHJ1ZSxcclxuICAgICAgICAgIHZlcmlmaWNhdGlvbl9tZXRob2Q6IFwicXJfYW5kX2ZhY2VcIixcclxuICAgICAgICAgIGZhY2VfbWF0Y2hfc2NvcmU6IDkyLFxyXG4gICAgICAgICAgY3JlYXRlZF9hdDogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDQgKiA2MCAqIDYwICogMTAwMCkudG9JU09TdHJpbmcoKSxcclxuICAgICAgICAgIHVwZGF0ZWRfYXQ6IG5ldyBEYXRlKERhdGUubm93KCkgLSAzICogNjAgKiA2MCAqIDEwMDApLnRvSVNPU3RyaW5nKClcclxuICAgICAgICB9XHJcbiAgICAgIF1cclxuXHJcbiAgICAgIC8vIEZpbHRlciBieSBzdHVkZW50X2lkIGlmIHByb3ZpZGVkXHJcbiAgICAgIGxldCBmaWx0ZXJlZEVudHJpZXMgPSBzYW1wbGVFbnRyaWVzXHJcbiAgICAgIGlmIChzdHVkZW50SWQpIHtcclxuICAgICAgICBmaWx0ZXJlZEVudHJpZXMgPSBzYW1wbGVFbnRyaWVzLmZpbHRlcihlID0+IGUuc3R1ZGVudF9pZCA9PT0gc3R1ZGVudElkKVxyXG4gICAgICB9XHJcblxyXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oZmlsdGVyZWRFbnRyaWVzKVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGNsaWVudCA9IGF3YWl0IGNsaWVudFByb21pc2VcclxuICAgIGlmICghY2xpZW50KSB7XHJcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihbXSlcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBkYiA9IGNsaWVudC5kYihcImlkY2FyZFwiKVxyXG4gICAgY29uc3QgZW50cmllcyA9IGRiLmNvbGxlY3Rpb24oXCJlbnRyeV9sb2dzXCIpXHJcblxyXG4gICAgY29uc3QgcXVlcnk6IGFueSA9IHt9XHJcbiAgICBpZiAoc3R1ZGVudElkKSBxdWVyeS5zdHVkZW50X2lkID0gc3R1ZGVudElkXHJcblxyXG4gICAgY29uc3QgcmVzdWx0cyA9IGF3YWl0IGVudHJpZXMuZmluZChxdWVyeSkuc29ydCh7IGVudHJ5X3RpbWU6IC0xIH0pLnRvQXJyYXkoKVxyXG4gICAgY29uc3QgZGF0YSA9IHJlc3VsdHMubWFwKChlKSA9PiAoe1xyXG4gICAgICAuLi5lLFxyXG4gICAgICBpZDogZS5faWQudG9TdHJpbmcoKSxcclxuICAgIH0pKVxyXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKGRhdGEpXHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJHRVQgL2FwaS9lbnRyaWVzIGVycm9yOlwiLCBlcnJvcilcclxuICAgIC8vIFJldHVybiBzYW1wbGUgZW50cmllcyBmb3IgdGVzdGluZyBpbnN0ZWFkIG9mIGVtcHR5IGFycmF5XHJcbiAgICBjb25zdCBzYW1wbGVFbnRyaWVzID0gW1xyXG4gICAgICB7XHJcbiAgICAgICAgaWQ6IFwiZW50cnlfMDAxXCIsXHJcbiAgICAgICAgc3R1ZGVudF9pZDogXCJTVFVfMDAxXCIsXHJcbiAgICAgICAgYXBwbGljYXRpb25fbnVtYmVyOiBcIkFQUDIwMjU0MTA1XCIsXHJcbiAgICAgICAgc3R1ZGVudF9uYW1lOiBcIlRlc3QgU3R1ZGVudFwiLFxyXG4gICAgICAgIGVudHJ5X3RpbWU6IG5ldyBEYXRlKERhdGUubm93KCkgLSAyICogNjAgKiA2MCAqIDEwMDApLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgICAgZXhpdF90aW1lOiBuZXcgRGF0ZShEYXRlLm5vdygpIC0gMSAqIDYwICogNjAgKiAxMDAwKS50b0lTT1N0cmluZygpLFxyXG4gICAgICAgIHN0YXR1czogXCJleGl0XCIsXHJcbiAgICAgICAgdmVyaWZpZWQ6IHRydWUsXHJcbiAgICAgICAgdmVyaWZpY2F0aW9uX21ldGhvZDogXCJxcl9hbmRfZmFjZVwiLFxyXG4gICAgICAgIGZhY2VfbWF0Y2hfc2NvcmU6IDg1LFxyXG4gICAgICAgIGNyZWF0ZWRfYXQ6IG5ldyBEYXRlKERhdGUubm93KCkgLSAyICogNjAgKiA2MCAqIDEwMDApLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgICAgdXBkYXRlZF9hdDogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDEgKiA2MCAqIDYwICogMTAwMCkudG9JU09TdHJpbmcoKVxyXG4gICAgICB9XHJcbiAgICBdXHJcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oc2FtcGxlRW50cmllcylcclxuICB9XHJcbn1cclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBQT1NUKHJlcTogTmV4dFJlcXVlc3QpIHtcclxuICB0cnkge1xyXG4gICAgY29uc3QgYm9keSA9IGF3YWl0IHJlcS5qc29uKClcclxuICAgIGNvbnNvbGUubG9nKFwiUmVjZWl2ZWQgZW50cnkgYm9keTpcIiwgYm9keSlcclxuXHJcbiAgICBpZiAoIWNsaWVudFByb21pc2UpIHtcclxuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6IFwiRGF0YWJhc2Ugbm90IGF2YWlsYWJsZVwiIH0sIHsgc3RhdHVzOiA1MDMgfSlcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBjbGllbnQgPSBhd2FpdCBjbGllbnRQcm9taXNlXHJcbiAgICBpZiAoIWNsaWVudCkge1xyXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogXCJEYXRhYmFzZSBjb25uZWN0aW9uIGZhaWxlZFwiIH0sIHsgc3RhdHVzOiA1MDMgfSlcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBkYiA9IGNsaWVudC5kYihcImlkY2FyZFwiKVxyXG4gICAgY29uc3QgZW50cmllcyA9IGRiLmNvbGxlY3Rpb24oXCJlbnRyeV9sb2dzXCIpXHJcblxyXG4gICAgLy8gQ2hlY2sgaWYgc3R1ZGVudCBpcyBhbHJlYWR5IGluc2lkZVxyXG4gICAgY29uc3QgZXhpc3RpbmdFbnRyeSA9IGF3YWl0IGVudHJpZXMuZmluZE9uZSh7XHJcbiAgICAgIHN0dWRlbnRfaWQ6IGJvZHkuc3R1ZGVudF9pZCxcclxuICAgICAgZXhpdF90aW1lOiBudWxsXHJcbiAgICB9KVxyXG5cclxuICAgIGlmIChleGlzdGluZ0VudHJ5KSB7XHJcbiAgICAgIC8vIFN0dWRlbnQgaXMgaW5zaWRlLCBtYXJrIGV4aXRcclxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgZW50cmllcy5maW5kT25lQW5kVXBkYXRlKFxyXG4gICAgICAgIHsgX2lkOiBleGlzdGluZ0VudHJ5Ll9pZCB9LFxyXG4gICAgICAgIHsgXHJcbiAgICAgICAgICAkc2V0OiB7IFxyXG4gICAgICAgICAgICBleGl0X3RpbWU6IG5ldyBEYXRlKCksXHJcbiAgICAgICAgICAgIHN0YXR1czogXCJleGl0XCIsXHJcbiAgICAgICAgICAgIHVwZGF0ZWRfYXQ6IG5ldyBEYXRlKClcclxuICAgICAgICAgIH0gXHJcbiAgICAgICAgfSxcclxuICAgICAgICB7IHJldHVybkRvY3VtZW50OiBcImFmdGVyXCIgfVxyXG4gICAgICApXHJcbiAgICAgIGlmICghcmVzdWx0IHx8ICFyZXN1bHQudmFsdWUpIHtcclxuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogXCJGYWlsZWQgdG8gdXBkYXRlIGVudHJ5XCIgfSwgeyBzdGF0dXM6IDUwMCB9KVxyXG4gICAgICB9XHJcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IC4uLnJlc3VsdC52YWx1ZSwgaWQ6IHJlc3VsdC52YWx1ZS5faWQudG9TdHJpbmcoKSB9KVxyXG4gICAgfSBlbHNlIHtcclxuICAgICAgLy8gTmV3IGVudHJ5IHdpdGggZW5oYW5jZWQgdmVyaWZpY2F0aW9uIGRhdGFcclxuICAgICAgY29uc3QgbmV3RW50cnkgPSB7XHJcbiAgICAgICAgLi4uYm9keSxcclxuICAgICAgICBlbnRyeV90aW1lOiBuZXcgRGF0ZSgpLFxyXG4gICAgICAgIHN0YXR1czogXCJlbnRyeVwiLFxyXG4gICAgICAgIHZlcmlmaWVkOiB0cnVlLFxyXG4gICAgICAgIHZlcmlmaWNhdGlvbl9tZXRob2Q6IGJvZHkudmVyaWZpY2F0aW9uX21ldGhvZCB8fCBcInFyX2FuZF9mYWNlXCIsXHJcbiAgICAgICAgZmFjZV9tYXRjaF9zY29yZTogYm9keS5mYWNlX21hdGNoX3Njb3JlIHx8IG51bGwsXHJcbiAgICAgICAgcXJfdmFsaWRhdGVkOiBib2R5LnFyX3ZhbGlkYXRlZCAhPT0gdW5kZWZpbmVkID8gYm9keS5xcl92YWxpZGF0ZWQgOiB0cnVlLFxyXG4gICAgICAgIHZlcmlmaWNhdGlvbl90aW1lc3RhbXA6IGJvZHkudmVyaWZpY2F0aW9uX3RpbWVzdGFtcCB8fCBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgICAgc3RhdGlvbl9pZDogYm9keS5zdGF0aW9uX2lkIHx8IFwibWFpbl9lbnRyYW5jZVwiLFxyXG4gICAgICAgIGNyZWF0ZWRfYXQ6IG5ldyBEYXRlKCksXHJcbiAgICAgICAgdXBkYXRlZF9hdDogbmV3IERhdGUoKSxcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgZW50cmllcy5pbnNlcnRPbmUobmV3RW50cnkpXHJcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IC4uLm5ld0VudHJ5LCBpZDogcmVzdWx0Lmluc2VydGVkSWQudG9TdHJpbmcoKSB9LCB7IHN0YXR1czogMjAxIH0pXHJcbiAgICB9XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJQT1NUIC9hcGkvZW50cmllcyBlcnJvcjpcIiwgZXJyb3IpXHJcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogXCJGYWlsZWQgdG8gYWRkIGVudHJ5XCIgfSwgeyBzdGF0dXM6IDUwMCB9KVxyXG4gIH1cclxufSAiXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiY2xpZW50UHJvbWlzZSIsIkdFVCIsInJlcSIsInVybCIsIlVSTCIsInN0dWRlbnRJZCIsInNlYXJjaFBhcmFtcyIsImdldCIsImNvbnNvbGUiLCJsb2ciLCJzYW1wbGVFbnRyaWVzIiwiaWQiLCJzdHVkZW50X2lkIiwiYXBwbGljYXRpb25fbnVtYmVyIiwic3R1ZGVudF9uYW1lIiwiZW50cnlfdGltZSIsIkRhdGUiLCJub3ciLCJ0b0lTT1N0cmluZyIsImV4aXRfdGltZSIsInN0YXR1cyIsInZlcmlmaWVkIiwidmVyaWZpY2F0aW9uX21ldGhvZCIsImZhY2VfbWF0Y2hfc2NvcmUiLCJjcmVhdGVkX2F0IiwidXBkYXRlZF9hdCIsImZpbHRlcmVkRW50cmllcyIsImZpbHRlciIsImUiLCJqc29uIiwiY2xpZW50IiwiZGIiLCJlbnRyaWVzIiwiY29sbGVjdGlvbiIsInF1ZXJ5IiwicmVzdWx0cyIsImZpbmQiLCJzb3J0IiwidG9BcnJheSIsImRhdGEiLCJtYXAiLCJfaWQiLCJ0b1N0cmluZyIsImVycm9yIiwiUE9TVCIsImJvZHkiLCJleGlzdGluZ0VudHJ5IiwiZmluZE9uZSIsInJlc3VsdCIsImZpbmRPbmVBbmRVcGRhdGUiLCIkc2V0IiwicmV0dXJuRG9jdW1lbnQiLCJ2YWx1ZSIsIm5ld0VudHJ5IiwicXJfdmFsaWRhdGVkIiwidW5kZWZpbmVkIiwidmVyaWZpY2F0aW9uX3RpbWVzdGFtcCIsInN0YXRpb25faWQiLCJpbnNlcnRPbmUiLCJpbnNlcnRlZElkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/api/entries/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/mongodb.ts":
/*!************************!*\
  !*** ./lib/mongodb.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_0__);\n\nconst uri = process.env.MONGODB_URI || \"mongodb://localhost:27017/idcard\";\nconst options = {};\nlet client = null;\nlet clientPromise = null;\ntry {\n    if (process.env.MONGODB_URI) {\n        client = new mongodb__WEBPACK_IMPORTED_MODULE_0__.MongoClient(uri, options);\n        clientPromise = client.connect();\n    }\n} catch (error) {\n    console.warn(\"MongoDB connection failed, will use local storage:\", error);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (clientPromise);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvbW9uZ29kYi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBcUM7QUFFckMsTUFBTUMsTUFBTUMsUUFBUUMsR0FBRyxDQUFDQyxXQUFXLElBQUk7QUFDdkMsTUFBTUMsVUFBVSxDQUFDO0FBRWpCLElBQUlDLFNBQTZCO0FBQ2pDLElBQUlDLGdCQUE2QztBQUVqRCxJQUFJO0lBQ0YsSUFBSUwsUUFBUUMsR0FBRyxDQUFDQyxXQUFXLEVBQUU7UUFDM0JFLFNBQVMsSUFBSU4sZ0RBQVdBLENBQUNDLEtBQUtJO1FBQzlCRSxnQkFBZ0JELE9BQU9FLE9BQU87SUFDaEM7QUFDRixFQUFFLE9BQU9DLE9BQU87SUFDZEMsUUFBUUMsSUFBSSxDQUFDLHNEQUFzREY7QUFDckU7QUFFQSxpRUFBZUYsYUFBYUEsRUFBQSIsInNvdXJjZXMiOlsiRDpcXGlkY2FyZFxcY2FyZHN0YXRpb25cXGxpYlxcbW9uZ29kYi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBNb25nb0NsaWVudCB9IGZyb20gXCJtb25nb2RiXCJcclxuXHJcbmNvbnN0IHVyaSA9IHByb2Nlc3MuZW52Lk1PTkdPREJfVVJJIHx8IFwibW9uZ29kYjovL2xvY2FsaG9zdDoyNzAxNy9pZGNhcmRcIlxyXG5jb25zdCBvcHRpb25zID0ge31cclxuXHJcbmxldCBjbGllbnQ6IE1vbmdvQ2xpZW50IHwgbnVsbCA9IG51bGxcclxubGV0IGNsaWVudFByb21pc2U6IFByb21pc2U8TW9uZ29DbGllbnQ+IHwgbnVsbCA9IG51bGxcclxuXHJcbnRyeSB7XHJcbiAgaWYgKHByb2Nlc3MuZW52Lk1PTkdPREJfVVJJKSB7XHJcbiAgICBjbGllbnQgPSBuZXcgTW9uZ29DbGllbnQodXJpLCBvcHRpb25zKVxyXG4gICAgY2xpZW50UHJvbWlzZSA9IGNsaWVudC5jb25uZWN0KClcclxuICB9XHJcbn0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgY29uc29sZS53YXJuKFwiTW9uZ29EQiBjb25uZWN0aW9uIGZhaWxlZCwgd2lsbCB1c2UgbG9jYWwgc3RvcmFnZTpcIiwgZXJyb3IpXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGNsaWVudFByb21pc2UiXSwibmFtZXMiOlsiTW9uZ29DbGllbnQiLCJ1cmkiLCJwcm9jZXNzIiwiZW52IiwiTU9OR09EQl9VUkkiLCJvcHRpb25zIiwiY2xpZW50IiwiY2xpZW50UHJvbWlzZSIsImNvbm5lY3QiLCJlcnJvciIsImNvbnNvbGUiLCJ3YXJuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fentries%2Froute&page=%2Fapi%2Fentries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fentries%2Froute.ts&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fentries%2Froute&page=%2Fapi%2Fentries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fentries%2Froute.ts&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_idcard_cardstation_app_api_entries_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/entries/route.ts */ \"(rsc)/./app/api/entries/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/entries/route\",\n        pathname: \"/api/entries\",\n        filename: \"route\",\n        bundlePath: \"app/api/entries/route\"\n    },\n    resolvedPagePath: \"D:\\\\idcard\\\\cardstation\\\\app\\\\api\\\\entries\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_idcard_cardstation_app_api_entries_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fentries%2Froute&page=%2Fapi%2Fentries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fentries%2Froute.ts&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "mongodb":
/*!**************************!*\
  !*** external "mongodb" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongodb");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fentries%2Froute&page=%2Fapi%2Fentries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fentries%2Froute.ts&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();