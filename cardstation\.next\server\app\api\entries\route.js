/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/entries/route";
exports.ids = ["app/api/entries/route"];
exports.modules = {

/***/ "(rsc)/./app/api/entries/route.ts":
/*!**********************************!*\
  !*** ./app/api/entries/route.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./lib/mongodb.ts\");\n\n\nasync function GET(req) {\n    try {\n        const url = new URL(req.url);\n        const studentId = url.searchParams.get(\"student_id\");\n        if (!_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) {\n            // MongoDB not available, return sample entries for testing\n            console.log(\"MongoDB not available, returning sample entries for testing\");\n            const sampleEntries = [\n                {\n                    id: \"entry_001\",\n                    student_id: \"STU_001\",\n                    application_number: \"APP20254105\",\n                    student_name: \"Test Student\",\n                    entry_time: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n                    exit_time: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),\n                    status: \"exit\",\n                    verified: true,\n                    verification_method: \"qr_and_face\",\n                    face_match_score: 85,\n                    created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n                    updated_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()\n                },\n                {\n                    id: \"entry_002\",\n                    student_id: \"STU_001\",\n                    application_number: \"APP20254105\",\n                    student_name: \"Test Student\",\n                    entry_time: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),\n                    exit_time: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),\n                    status: \"exit\",\n                    verified: true,\n                    verification_method: \"qr_and_face\",\n                    face_match_score: 92,\n                    created_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),\n                    updated_at: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString()\n                }\n            ];\n            // Filter by student_id if provided\n            let filteredEntries = sampleEntries;\n            if (studentId) {\n                filteredEntries = sampleEntries.filter((e)=>e.student_id === studentId);\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(filteredEntries);\n        }\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        if (!client) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json([]);\n        }\n        const db = client.db(\"idcard\");\n        const entries = db.collection(\"entry_logs\");\n        const query = {};\n        if (studentId) query.student_id = studentId;\n        const results = await entries.find(query).sort({\n            entry_time: -1\n        }).toArray();\n        const data = results.map((e)=>({\n                ...e,\n                id: e._id.toString()\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"GET /api/entries error:\", error);\n        // Return sample entries for testing instead of empty array\n        const sampleEntries = [\n            {\n                id: \"entry_001\",\n                student_id: \"STU_001\",\n                application_number: \"APP20254105\",\n                student_name: \"Test Student\",\n                entry_time: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n                exit_time: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),\n                status: \"exit\",\n                verified: true,\n                verification_method: \"qr_and_face\",\n                face_match_score: 85,\n                created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n                updated_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()\n            }\n        ];\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(sampleEntries);\n    }\n}\nasync function POST(req) {\n    try {\n        const body = await req.json();\n        console.log(\"Received entry body:\", body);\n        // Enhanced entry/exit logic with fallback support\n        if (!_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) {\n            console.log(\"MongoDB not available, using localStorage simulation\");\n            // Simulate entry/exit logic for testing\n            const now = new Date();\n            const entryId = `entry_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n            // For testing, we'll simulate checking if student was already inside today\n            // In real implementation, this would check localStorage or other storage\n            const isFirstScanToday = true // You can modify this logic\n            ;\n            const newEntry = {\n                id: entryId,\n                student_id: body.student_id,\n                application_number: body.application_number,\n                student_name: body.student_name,\n                entry_time: now.toISOString(),\n                exit_time: isFirstScanToday ? null : now.toISOString(),\n                status: isFirstScanToday ? \"entry\" : \"exit\",\n                verified: true,\n                verification_method: body.verification_method || \"qr_and_face\",\n                face_match_score: body.face_match_score || null,\n                qr_validated: body.qr_validated !== undefined ? body.qr_validated : true,\n                verification_timestamp: body.verification_timestamp || now.toISOString(),\n                station_id: body.station_id || \"main_entrance\",\n                created_at: now.toISOString(),\n                updated_at: now.toISOString()\n            };\n            console.log(\"Simulated entry created:\", newEntry);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(newEntry, {\n                status: 201\n            });\n        }\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        if (!client) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Database connection failed\"\n            }, {\n                status: 503\n            });\n        }\n        const db = client.db(\"idcard\");\n        const entries = db.collection(\"entry_logs\");\n        // Check if student is already inside (has entry today without exit)\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        const tomorrow = new Date(today);\n        tomorrow.setDate(tomorrow.getDate() + 1);\n        const existingEntry = await entries.findOne({\n            student_id: body.student_id,\n            entry_time: {\n                $gte: today,\n                $lt: tomorrow\n            },\n            exit_time: null\n        });\n        if (existingEntry) {\n            // Student is inside, mark exit\n            const result = await entries.findOneAndUpdate({\n                _id: existingEntry._id\n            }, {\n                $set: {\n                    exit_time: new Date(),\n                    status: \"exit\",\n                    updated_at: new Date()\n                }\n            }, {\n                returnDocument: \"after\"\n            });\n            if (!result || !result.value) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Failed to update entry\"\n                }, {\n                    status: 500\n                });\n            }\n            console.log(\"Exit recorded for student:\", body.student_name);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                ...result.value,\n                id: result.value._id.toString()\n            });\n        } else {\n            // New entry with enhanced verification data\n            const newEntry = {\n                ...body,\n                entry_time: new Date(),\n                status: \"entry\",\n                verified: true,\n                verification_method: body.verification_method || \"qr_and_face\",\n                face_match_score: body.face_match_score || null,\n                qr_validated: body.qr_validated !== undefined ? body.qr_validated : true,\n                verification_timestamp: body.verification_timestamp || new Date().toISOString(),\n                station_id: body.station_id || \"main_entrance\",\n                created_at: new Date(),\n                updated_at: new Date()\n            };\n            const result = await entries.insertOne(newEntry);\n            console.log(\"Entry recorded for student:\", body.student_name);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                ...newEntry,\n                id: result.insertedId.toString()\n            }, {\n                status: 201\n            });\n        }\n    } catch (error) {\n        console.error(\"POST /api/entries error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to add entry\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/entries/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/mongodb.ts":
/*!************************!*\
  !*** ./lib/mongodb.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_0__);\n\nconst uri = process.env.MONGODB_URI || \"mongodb://localhost:27017/idcard\";\nconst options = {};\nlet client = null;\nlet clientPromise = null;\ntry {\n    if (process.env.MONGODB_URI) {\n        client = new mongodb__WEBPACK_IMPORTED_MODULE_0__.MongoClient(uri, options);\n        clientPromise = client.connect();\n    }\n} catch (error) {\n    console.warn(\"MongoDB connection failed, will use local storage:\", error);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (clientPromise);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvbW9uZ29kYi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBcUM7QUFFckMsTUFBTUMsTUFBTUMsUUFBUUMsR0FBRyxDQUFDQyxXQUFXLElBQUk7QUFDdkMsTUFBTUMsVUFBVSxDQUFDO0FBRWpCLElBQUlDLFNBQTZCO0FBQ2pDLElBQUlDLGdCQUE2QztBQUVqRCxJQUFJO0lBQ0YsSUFBSUwsUUFBUUMsR0FBRyxDQUFDQyxXQUFXLEVBQUU7UUFDM0JFLFNBQVMsSUFBSU4sZ0RBQVdBLENBQUNDLEtBQUtJO1FBQzlCRSxnQkFBZ0JELE9BQU9FLE9BQU87SUFDaEM7QUFDRixFQUFFLE9BQU9DLE9BQU87SUFDZEMsUUFBUUMsSUFBSSxDQUFDLHNEQUFzREY7QUFDckU7QUFFQSxpRUFBZUYsYUFBYUEsRUFBQSIsInNvdXJjZXMiOlsiRDpcXGlkY2FyZFxcY2FyZHN0YXRpb25cXGxpYlxcbW9uZ29kYi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBNb25nb0NsaWVudCB9IGZyb20gXCJtb25nb2RiXCJcclxuXHJcbmNvbnN0IHVyaSA9IHByb2Nlc3MuZW52Lk1PTkdPREJfVVJJIHx8IFwibW9uZ29kYjovL2xvY2FsaG9zdDoyNzAxNy9pZGNhcmRcIlxyXG5jb25zdCBvcHRpb25zID0ge31cclxuXHJcbmxldCBjbGllbnQ6IE1vbmdvQ2xpZW50IHwgbnVsbCA9IG51bGxcclxubGV0IGNsaWVudFByb21pc2U6IFByb21pc2U8TW9uZ29DbGllbnQ+IHwgbnVsbCA9IG51bGxcclxuXHJcbnRyeSB7XHJcbiAgaWYgKHByb2Nlc3MuZW52Lk1PTkdPREJfVVJJKSB7XHJcbiAgICBjbGllbnQgPSBuZXcgTW9uZ29DbGllbnQodXJpLCBvcHRpb25zKVxyXG4gICAgY2xpZW50UHJvbWlzZSA9IGNsaWVudC5jb25uZWN0KClcclxuICB9XHJcbn0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgY29uc29sZS53YXJuKFwiTW9uZ29EQiBjb25uZWN0aW9uIGZhaWxlZCwgd2lsbCB1c2UgbG9jYWwgc3RvcmFnZTpcIiwgZXJyb3IpXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGNsaWVudFByb21pc2UiXSwibmFtZXMiOlsiTW9uZ29DbGllbnQiLCJ1cmkiLCJwcm9jZXNzIiwiZW52IiwiTU9OR09EQl9VUkkiLCJvcHRpb25zIiwiY2xpZW50IiwiY2xpZW50UHJvbWlzZSIsImNvbm5lY3QiLCJlcnJvciIsImNvbnNvbGUiLCJ3YXJuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fentries%2Froute&page=%2Fapi%2Fentries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fentries%2Froute.ts&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fentries%2Froute&page=%2Fapi%2Fentries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fentries%2Froute.ts&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_idcard_cardstation_app_api_entries_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/entries/route.ts */ \"(rsc)/./app/api/entries/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/entries/route\",\n        pathname: \"/api/entries\",\n        filename: \"route\",\n        bundlePath: \"app/api/entries/route\"\n    },\n    resolvedPagePath: \"D:\\\\idcard\\\\cardstation\\\\app\\\\api\\\\entries\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_idcard_cardstation_app_api_entries_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fentries%2Froute&page=%2Fapi%2Fentries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fentries%2Froute.ts&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "mongodb":
/*!**************************!*\
  !*** external "mongodb" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongodb");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fentries%2Froute&page=%2Fapi%2Fentries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fentries%2Froute.ts&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();