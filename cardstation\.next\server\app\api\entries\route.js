/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/entries/route";
exports.ids = ["app/api/entries/route"];
exports.modules = {

/***/ "(rsc)/./app/api/entries/route.ts":
/*!**********************************!*\
  !*** ./app/api/entries/route.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./lib/mongodb.ts\");\n\n\nasync function GET(req) {\n    try {\n        const url = new URL(req.url);\n        const studentId = url.searchParams.get(\"student_id\");\n        // Always return sample entries for testing to ensure data is visible\n        console.log(\"Returning sample entries for testing\");\n        // Generate current time for reference\n        const now = new Date();\n        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n        // Create sample entries with a mix of entry/exit records\n        const sampleEntries = [\n            // Today's entries (for today's count)\n            {\n                id: \"entry_001\",\n                student_id: \"STU_001\",\n                application_number: \"APP20254105\",\n                student_name: \"Test Student\",\n                entry_time: new Date(today.getTime() + 9 * 60 * 60 * 1000).toISOString(),\n                exit_time: new Date(today.getTime() + 10 * 60 * 60 * 1000).toISOString(),\n                status: \"exit\",\n                verified: true,\n                verification_method: \"qr_and_face\",\n                face_match_score: 85,\n                created_at: new Date(today.getTime() + 9 * 60 * 60 * 1000).toISOString(),\n                updated_at: new Date(today.getTime() + 10 * 60 * 60 * 1000).toISOString()\n            },\n            {\n                id: \"entry_002\",\n                student_id: \"STU_001\",\n                application_number: \"APP20254105\",\n                student_name: \"Test Student\",\n                entry_time: new Date(today.getTime() + 13 * 60 * 60 * 1000).toISOString(),\n                exit_time: null,\n                status: \"entry\",\n                verified: true,\n                verification_method: \"qr_and_face\",\n                face_match_score: 92,\n                created_at: new Date(today.getTime() + 13 * 60 * 60 * 1000).toISOString(),\n                updated_at: new Date(today.getTime() + 13 * 60 * 60 * 1000).toISOString()\n            },\n            // Yesterday's entries\n            {\n                id: \"entry_003\",\n                student_id: \"STU_002\",\n                application_number: \"APP20254106\",\n                student_name: \"Another Student\",\n                entry_time: new Date(today.getTime() - 15 * 60 * 60 * 1000).toISOString(),\n                exit_time: new Date(today.getTime() - 14 * 60 * 60 * 1000).toISOString(),\n                status: \"exit\",\n                verified: true,\n                verification_method: \"qr_and_face\",\n                face_match_score: 88,\n                created_at: new Date(today.getTime() - 15 * 60 * 60 * 1000).toISOString(),\n                updated_at: new Date(today.getTime() - 14 * 60 * 60 * 1000).toISOString()\n            }\n        ];\n        // Filter by student_id if provided\n        let filteredEntries = sampleEntries;\n        if (studentId) {\n            filteredEntries = sampleEntries.filter((e)=>e.student_id === studentId);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(filteredEntries);\n    // MongoDB code commented out for testing - uncomment when MongoDB is available\n    /*\r\n    const client = await clientPromise\r\n    if (!client) {\r\n      return NextResponse.json([])\r\n    }\r\n\r\n    const db = client.db(\"idcard\")\r\n    const entries = db.collection(\"entry_logs\")\r\n\r\n    const query: any = {}\r\n    if (studentId) query.student_id = studentId\r\n\r\n    const results = await entries.find(query).sort({ entry_time: -1 }).toArray()\r\n    const data = results.map((e) => ({\r\n      ...e,\r\n      id: e._id.toString(),\r\n    }))\r\n    return NextResponse.json(data)\r\n    */ } catch (error) {\n        console.error(\"GET /api/entries error:\", error);\n        // Return sample entries for testing instead of empty array\n        const now = new Date();\n        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n        const sampleEntries = [\n            {\n                id: \"entry_001\",\n                student_id: \"STU_001\",\n                application_number: \"APP20254105\",\n                student_name: \"Test Student\",\n                entry_time: new Date(today.getTime() + 9 * 60 * 60 * 1000).toISOString(),\n                exit_time: new Date(today.getTime() + 10 * 60 * 60 * 1000).toISOString(),\n                status: \"exit\",\n                verified: true,\n                verification_method: \"qr_and_face\",\n                face_match_score: 85,\n                created_at: new Date(today.getTime() + 9 * 60 * 60 * 1000).toISOString(),\n                updated_at: new Date(today.getTime() + 10 * 60 * 60 * 1000).toISOString()\n            }\n        ];\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(sampleEntries);\n    }\n}\nasync function POST(req) {\n    try {\n        const body = await req.json();\n        console.log(\"Received entry body:\", body);\n        // Enhanced entry/exit logic with fallback support\n        if (!_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) {\n            console.log(\"MongoDB not available, using localStorage simulation\");\n            // Simulate entry/exit logic for testing\n            const now = new Date();\n            const entryId = `entry_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n            // For testing, we'll simulate checking if student was already inside today\n            // In real implementation, this would check localStorage or other storage\n            const isFirstScanToday = true // You can modify this logic\n            ;\n            const newEntry = {\n                id: entryId,\n                student_id: body.student_id,\n                application_number: body.application_number,\n                student_name: body.student_name,\n                entry_time: now.toISOString(),\n                exit_time: isFirstScanToday ? null : now.toISOString(),\n                status: isFirstScanToday ? \"entry\" : \"exit\",\n                verified: true,\n                verification_method: body.verification_method || \"qr_and_face\",\n                face_match_score: body.face_match_score || null,\n                qr_validated: body.qr_validated !== undefined ? body.qr_validated : true,\n                verification_timestamp: body.verification_timestamp || now.toISOString(),\n                station_id: body.station_id || \"main_entrance\",\n                created_at: now.toISOString(),\n                updated_at: now.toISOString()\n            };\n            console.log(\"Simulated entry created:\", newEntry);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(newEntry, {\n                status: 201\n            });\n        }\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        if (!client) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Database connection failed\"\n            }, {\n                status: 503\n            });\n        }\n        const db = client.db(\"idcard\");\n        const entries = db.collection(\"entry_logs\");\n        // Check if student is already inside (has entry today without exit)\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        const tomorrow = new Date(today);\n        tomorrow.setDate(tomorrow.getDate() + 1);\n        const existingEntry = await entries.findOne({\n            student_id: body.student_id,\n            entry_time: {\n                $gte: today,\n                $lt: tomorrow\n            },\n            exit_time: null\n        });\n        if (existingEntry) {\n            // Student is inside, mark exit\n            const result = await entries.findOneAndUpdate({\n                _id: existingEntry._id\n            }, {\n                $set: {\n                    exit_time: new Date(),\n                    status: \"exit\",\n                    updated_at: new Date()\n                }\n            }, {\n                returnDocument: \"after\"\n            });\n            if (!result || !result.value) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Failed to update entry\"\n                }, {\n                    status: 500\n                });\n            }\n            console.log(\"Exit recorded for student:\", body.student_name);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                ...result.value,\n                id: result.value._id.toString()\n            });\n        } else {\n            // New entry with enhanced verification data\n            const newEntry = {\n                ...body,\n                entry_time: new Date(),\n                status: \"entry\",\n                verified: true,\n                verification_method: body.verification_method || \"qr_and_face\",\n                face_match_score: body.face_match_score || null,\n                qr_validated: body.qr_validated !== undefined ? body.qr_validated : true,\n                verification_timestamp: body.verification_timestamp || new Date().toISOString(),\n                station_id: body.station_id || \"main_entrance\",\n                created_at: new Date(),\n                updated_at: new Date()\n            };\n            const result = await entries.insertOne(newEntry);\n            console.log(\"Entry recorded for student:\", body.student_name);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                ...newEntry,\n                id: result.insertedId.toString()\n            }, {\n                status: 201\n            });\n        }\n    } catch (error) {\n        console.error(\"POST /api/entries error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to add entry\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/entries/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/mongodb.ts":
/*!************************!*\
  !*** ./lib/mongodb.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_0__);\n\nconst uri = process.env.MONGODB_URI || \"mongodb://localhost:27017/idcard\";\nconst options = {};\nlet client = null;\nlet clientPromise = null;\ntry {\n    if (process.env.MONGODB_URI) {\n        client = new mongodb__WEBPACK_IMPORTED_MODULE_0__.MongoClient(uri, options);\n        clientPromise = client.connect();\n    }\n} catch (error) {\n    console.warn(\"MongoDB connection failed, will use local storage:\", error);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (clientPromise);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvbW9uZ29kYi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBcUM7QUFFckMsTUFBTUMsTUFBTUMsUUFBUUMsR0FBRyxDQUFDQyxXQUFXLElBQUk7QUFDdkMsTUFBTUMsVUFBVSxDQUFDO0FBRWpCLElBQUlDLFNBQTZCO0FBQ2pDLElBQUlDLGdCQUE2QztBQUVqRCxJQUFJO0lBQ0YsSUFBSUwsUUFBUUMsR0FBRyxDQUFDQyxXQUFXLEVBQUU7UUFDM0JFLFNBQVMsSUFBSU4sZ0RBQVdBLENBQUNDLEtBQUtJO1FBQzlCRSxnQkFBZ0JELE9BQU9FLE9BQU87SUFDaEM7QUFDRixFQUFFLE9BQU9DLE9BQU87SUFDZEMsUUFBUUMsSUFBSSxDQUFDLHNEQUFzREY7QUFDckU7QUFFQSxpRUFBZUYsYUFBYUEsRUFBQSIsInNvdXJjZXMiOlsiRDpcXGlkY2FyZFxcY2FyZHN0YXRpb25cXGxpYlxcbW9uZ29kYi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBNb25nb0NsaWVudCB9IGZyb20gXCJtb25nb2RiXCJcclxuXHJcbmNvbnN0IHVyaSA9IHByb2Nlc3MuZW52Lk1PTkdPREJfVVJJIHx8IFwibW9uZ29kYjovL2xvY2FsaG9zdDoyNzAxNy9pZGNhcmRcIlxyXG5jb25zdCBvcHRpb25zID0ge31cclxuXHJcbmxldCBjbGllbnQ6IE1vbmdvQ2xpZW50IHwgbnVsbCA9IG51bGxcclxubGV0IGNsaWVudFByb21pc2U6IFByb21pc2U8TW9uZ29DbGllbnQ+IHwgbnVsbCA9IG51bGxcclxuXHJcbnRyeSB7XHJcbiAgaWYgKHByb2Nlc3MuZW52Lk1PTkdPREJfVVJJKSB7XHJcbiAgICBjbGllbnQgPSBuZXcgTW9uZ29DbGllbnQodXJpLCBvcHRpb25zKVxyXG4gICAgY2xpZW50UHJvbWlzZSA9IGNsaWVudC5jb25uZWN0KClcclxuICB9XHJcbn0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgY29uc29sZS53YXJuKFwiTW9uZ29EQiBjb25uZWN0aW9uIGZhaWxlZCwgd2lsbCB1c2UgbG9jYWwgc3RvcmFnZTpcIiwgZXJyb3IpXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGNsaWVudFByb21pc2UiXSwibmFtZXMiOlsiTW9uZ29DbGllbnQiLCJ1cmkiLCJwcm9jZXNzIiwiZW52IiwiTU9OR09EQl9VUkkiLCJvcHRpb25zIiwiY2xpZW50IiwiY2xpZW50UHJvbWlzZSIsImNvbm5lY3QiLCJlcnJvciIsImNvbnNvbGUiLCJ3YXJuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fentries%2Froute&page=%2Fapi%2Fentries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fentries%2Froute.ts&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fentries%2Froute&page=%2Fapi%2Fentries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fentries%2Froute.ts&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_idcard_cardstation_app_api_entries_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/entries/route.ts */ \"(rsc)/./app/api/entries/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/entries/route\",\n        pathname: \"/api/entries\",\n        filename: \"route\",\n        bundlePath: \"app/api/entries/route\"\n    },\n    resolvedPagePath: \"D:\\\\idcard\\\\cardstation\\\\app\\\\api\\\\entries\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_idcard_cardstation_app_api_entries_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fentries%2Froute&page=%2Fapi%2Fentries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fentries%2Froute.ts&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "mongodb":
/*!**************************!*\
  !*** external "mongodb" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongodb");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fentries%2Froute&page=%2Fapi%2Fentries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fentries%2Froute.ts&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();