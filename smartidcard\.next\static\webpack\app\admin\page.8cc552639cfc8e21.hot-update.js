"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./app/admin/page.tsx":
/*!****************************!*\
  !*** ./app/admin/page.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _lib_database_store__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/database-store */ \"(app-pages-browser)/./lib/database-store.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_12__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AdminPanel() {\n    _s();\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingStudent, setEditingStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [copiedText, setCopiedText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastUpdated, setLastUpdated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dataSource, setDataSource] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"local\");\n    const [databaseConnected, setDatabaseConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [storageInfo, setStorageInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        mode: \"Local\",\n        studentsCount: 0,\n        entriesCount: 0\n    });\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalStudents: 0,\n        todayEntries: 0,\n        todayExits: 0,\n        totalEntries: 0\n    });\n    const [newStudent, setNewStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        phone: \"\",\n        email: \"\",\n        class: \"\",\n        department: \"\",\n        schedule: \"\",\n        image: \"\"\n    });\n    const [imagePreview, setImagePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageFile, setImageFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPanel.useEffect\": ()=>{\n            // Check if admin is logged in\n            if (true) {\n                const adminLoggedIn = localStorage.getItem(\"adminLoggedIn\");\n                if (!adminLoggedIn) {\n                    router.push(\"/\");\n                    return;\n                }\n            }\n            setIsAuthenticated(true);\n            checkDatabaseConnection();\n            loadData();\n        }\n    }[\"AdminPanel.useEffect\"], [\n        router\n    ]);\n    // Separate function to refresh only stats (not full page)\n    const refreshStats = async ()=>{\n        try {\n            console.log(\"🔄 Refreshing stats from MongoDB via API...\");\n            let allEntries = [];\n            let todayEntries = [];\n            let studentsData = [];\n            let dataSource = \"local\";\n            try {\n                // Try cardstation API first (localhost:3000)\n                console.log(\"🔍 Trying cardstation API...\");\n                const [cardStudentsRes, cardEntriesRes] = await Promise.all([\n                    fetch('http://localhost:3000/api/students'),\n                    fetch('http://localhost:3000/api/entries')\n                ]);\n                if (cardStudentsRes.ok && cardEntriesRes.ok) {\n                    studentsData = await cardStudentsRes.json();\n                    allEntries = await cardEntriesRes.json();\n                    dataSource = \"cardstation\";\n                    console.log(\"✅ Data fetched from cardstation API\");\n                } else {\n                    throw new Error(\"Cardstation API not available\");\n                }\n            } catch (cardstationError) {\n                console.log(\"⚠️ Cardstation not available, trying local API...\");\n                try {\n                    // Try local API (localhost:3001)\n                    const [localStudentsRes, localEntriesRes] = await Promise.all([\n                        fetch('/api/students'),\n                        fetch('/api/entries')\n                    ]);\n                    if (localStudentsRes.ok && localEntriesRes.ok) {\n                        studentsData = await localStudentsRes.json();\n                        allEntries = await localEntriesRes.json();\n                        dataSource = \"local-api\";\n                        console.log(\"✅ Data fetched from local API\");\n                    } else {\n                        throw new Error(\"Local API not available\");\n                    }\n                } catch (localApiError) {\n                    console.log(\"⚠️ APIs not available, using database store...\");\n                    // Final fallback to database store\n                    allEntries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getAllEntries();\n                    todayEntries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getTodayEntries();\n                    studentsData = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getStudents();\n                    dataSource = \"database\";\n                }\n            }\n            // Filter today's entries if we got data from API\n            if (dataSource !== \"database\") {\n                const today = new Date().toDateString();\n                todayEntries = allEntries.filter((entry)=>{\n                    const entryDate = new Date(entry.entryTime || entry.entry_time).toDateString();\n                    return entryDate === today;\n                });\n            }\n            console.log(\"📊 Raw data:\", {\n                source: dataSource,\n                allEntries: allEntries.length,\n                todayEntries: todayEntries.length,\n                todayEntriesData: todayEntries,\n                students: studentsData.length\n            });\n            // Debug: Show sample entry data\n            if (allEntries.length > 0) {\n                console.log(\"📝 Sample entry:\", allEntries[0]);\n            }\n            if (todayEntries.length > 0) {\n                console.log(\"📅 Sample today entry:\", todayEntries[0]);\n            }\n            const entryCount = todayEntries.filter((e)=>e.status === 'entry').length;\n            const exitCount = todayEntries.filter((e)=>e.status === 'exit').length;\n            setStats({\n                totalStudents: studentsData.length,\n                todayEntries: entryCount,\n                todayExits: exitCount,\n                totalEntries: allEntries.length\n            });\n            setDataSource(dataSource);\n            setLastUpdated(new Date());\n            console.log(\"✅ Stats refreshed:\", {\n                source: dataSource,\n                totalStudents: studentsData.length,\n                todayEntries: entryCount,\n                todayExits: exitCount,\n                totalActivity: entryCount + exitCount,\n                allEntries: allEntries.length\n            });\n        } catch (error) {\n            console.error(\"❌ Error refreshing stats:\", error);\n        }\n    };\n    // Auto-reload only stats every 5 seconds (not full page)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPanel.useEffect\": ()=>{\n            if (!isAuthenticated) return;\n            const interval = setInterval({\n                \"AdminPanel.useEffect.interval\": ()=>{\n                    refreshStats() // Only refresh stats, not full page\n                    ;\n                }\n            }[\"AdminPanel.useEffect.interval\"], 5000) // 5 seconds\n            ;\n            return ({\n                \"AdminPanel.useEffect\": ()=>clearInterval(interval)\n            })[\"AdminPanel.useEffect\"];\n        }\n    }[\"AdminPanel.useEffect\"], [\n        isAuthenticated\n    ]);\n    const checkDatabaseConnection = async ()=>{\n        try {\n            // Check if we can connect to MongoDB via API\n            const studentsRes = await fetch('/api/students');\n            const entriesRes = await fetch('/api/entries');\n            if (studentsRes.ok && entriesRes.ok) {\n                const students = await studentsRes.json();\n                const entries = await entriesRes.json();\n                setDatabaseConnected(true);\n                setStorageInfo({\n                    mode: \"MongoDB Cloud\",\n                    studentsCount: students.length,\n                    entriesCount: entries.length\n                });\n                console.log(\"✅ MongoDB connection verified\");\n            } else {\n                throw new Error(\"API not responding\");\n            }\n        } catch (error) {\n            console.log(\"⚠️ MongoDB not available, using local storage\");\n            setDatabaseConnected(false);\n            const storageInfo = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getStorageInfo();\n            setStorageInfo(storageInfo);\n        }\n    };\n    const loadData = async ()=>{\n        try {\n            setLoading(true);\n            // Load students from local database (for admin management)\n            const studentsData = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getStudents();\n            setStudents(studentsData);\n            // Load stats from cardstation if available\n            await refreshStats();\n            const storageInfo = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getStorageInfo();\n            setStorageInfo(storageInfo);\n        } catch (error) {\n            console.error(\"Error loading data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleRefresh = ()=>{\n        loadData() // Full page refresh\n        ;\n    };\n    const handleStatsRefresh = ()=>{\n        refreshStats() // Only stats refresh\n        ;\n    };\n    const handleLogout = ()=>{\n        if (true) {\n            localStorage.removeItem(\"adminLoggedIn\");\n            localStorage.removeItem(\"adminUsername\");\n        }\n        router.push(\"/\");\n    };\n    // Handle image file selection\n    const handleImageSelect = (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (!file) return;\n        // Validate file type\n        if (!file.type.startsWith(\"image/\")) {\n            alert(\"Please select a valid image file (JPG, PNG, GIF, etc.)\");\n            return;\n        }\n        // Validate file size (max 5MB)\n        if (file.size > 5 * 1024 * 1024) {\n            alert(\"Image size should be less than 5MB\");\n            return;\n        }\n        setImageFile(file);\n        // Create preview\n        const reader = new FileReader();\n        reader.onload = (e)=>{\n            var _e_target;\n            const result = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n            setImagePreview(result);\n            setNewStudent({\n                ...newStudent,\n                image: result\n            });\n        };\n        reader.readAsDataURL(file);\n    };\n    // Remove selected image\n    const removeImage = ()=>{\n        setImageFile(null);\n        setImagePreview(null);\n        setNewStudent({\n            ...newStudent,\n            image: \"\"\n        });\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    // Take photo using camera\n    const takePhoto = async ()=>{\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({\n                video: true\n            });\n            // Create a video element to capture the stream\n            const video = document.createElement(\"video\");\n            video.srcObject = stream;\n            video.autoplay = true;\n            // Create a modal or popup to show camera feed\n            const modal = document.createElement(\"div\");\n            modal.style.cssText = \"\\n        position: fixed;\\n        top: 0;\\n        left: 0;\\n        width: 100%;\\n        height: 100%;\\n        background: rgba(0,0,0,0.8);\\n        display: flex;\\n        justify-content: center;\\n        align-items: center;\\n        z-index: 1000;\\n      \";\n            const container = document.createElement(\"div\");\n            container.style.cssText = \"\\n        background: white;\\n        padding: 20px;\\n        border-radius: 10px;\\n        text-align: center;\\n      \";\n            const canvas = document.createElement(\"canvas\");\n            const captureBtn = document.createElement(\"button\");\n            captureBtn.textContent = \"Capture Photo\";\n            captureBtn.style.cssText = \"\\n        background: #3b82f6;\\n        color: white;\\n        padding: 10px 20px;\\n        border: none;\\n        border-radius: 5px;\\n        margin: 10px;\\n        cursor: pointer;\\n      \";\n            const cancelBtn = document.createElement(\"button\");\n            cancelBtn.textContent = \"Cancel\";\n            cancelBtn.style.cssText = \"\\n        background: #6b7280;\\n        color: white;\\n        padding: 10px 20px;\\n        border: none;\\n        border-radius: 5px;\\n        margin: 10px;\\n        cursor: pointer;\\n      \";\n            container.appendChild(video);\n            container.appendChild(document.createElement(\"br\"));\n            container.appendChild(captureBtn);\n            container.appendChild(cancelBtn);\n            modal.appendChild(container);\n            document.body.appendChild(modal);\n            // Capture photo\n            captureBtn.onclick = ()=>{\n                canvas.width = video.videoWidth;\n                canvas.height = video.videoHeight;\n                const ctx = canvas.getContext(\"2d\");\n                ctx === null || ctx === void 0 ? void 0 : ctx.drawImage(video, 0, 0);\n                const imageData = canvas.toDataURL(\"image/jpeg\", 0.8);\n                setImagePreview(imageData);\n                setNewStudent({\n                    ...newStudent,\n                    image: imageData\n                });\n                // Stop camera and close modal\n                stream.getTracks().forEach((track)=>track.stop());\n                document.body.removeChild(modal);\n            };\n            // Cancel\n            cancelBtn.onclick = ()=>{\n                stream.getTracks().forEach((track)=>track.stop());\n                document.body.removeChild(modal);\n            };\n        } catch (error) {\n            alert(\"Camera access denied or not available\");\n        }\n    };\n    const validateForm = ()=>{\n        if (!newStudent.name.trim()) {\n            alert(\"Student name is required\");\n            return false;\n        }\n        if (!newStudent.phone.trim()) {\n            alert(\"Phone number is required\");\n            return false;\n        }\n        if (newStudent.phone.length !== 10 || !/^\\d+$/.test(newStudent.phone)) {\n            alert(\"Phone number must be exactly 10 digits\");\n            return false;\n        }\n        if (!newStudent.class) {\n            alert(\"Class selection is required\");\n            return false;\n        }\n        if (!newStudent.image) {\n            alert(\"Student photo is required. Please upload an image or take a photo.\");\n            return false;\n        }\n        return true;\n    };\n    const handleAddStudent = async ()=>{\n        if (!validateForm()) return;\n        // Check if phone number already exists\n        const existingStudent = students.find((s)=>s.phone === newStudent.phone);\n        if (existingStudent) {\n            alert(\"Phone number already exists!\");\n            return;\n        }\n        setLoading(true);\n        try {\n            const applicationNumber = _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.generateApplicationNumber();\n            const student = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.addStudent({\n                ...newStudent,\n                application_number: applicationNumber,\n                image_url: newStudent.image\n            });\n            await loadData();\n            resetForm();\n            alert(\"Student Added Successfully!\\n\\nName: \".concat(student.name, \"\\nApplication Number: \").concat(applicationNumber, \"\\nPhone: \").concat(student.phone, \"\\n\\nPlease provide Application Number and Phone Number to the student for login.\\n\\nData saved in \").concat(storageInfo.mode, \" storage.\"));\n        } catch (error) {\n            alert(\"Error adding student. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleEditStudent = (student)=>{\n        setEditingStudent(student);\n        setNewStudent({\n            name: student.name,\n            phone: student.phone,\n            email: student.email || \"\",\n            class: student.class,\n            department: student.department || \"\",\n            schedule: student.schedule || \"\",\n            image: student.image_url || \"\"\n        });\n        setImagePreview(student.image_url || null);\n        setShowAddForm(false);\n    };\n    const handleUpdateStudent = async ()=>{\n        if (!validateForm() || !editingStudent) return;\n        // Check if phone number already exists (excluding current student)\n        const existingStudent = students.find((s)=>s.phone === newStudent.phone && s.id !== editingStudent.id);\n        if (existingStudent) {\n            alert(\"Phone number already exists!\");\n            return;\n        }\n        setLoading(true);\n        try {\n            await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.updateStudent(editingStudent.id, {\n                name: newStudent.name,\n                phone: newStudent.phone,\n                email: newStudent.email || null,\n                class: newStudent.class,\n                department: newStudent.department || null,\n                schedule: newStudent.schedule || null,\n                image_url: newStudent.image\n            });\n            await loadData();\n            resetForm();\n            alert(\"Student updated successfully!\\n\\nData saved in \".concat(storageInfo.mode, \" storage.\"));\n        } catch (error) {\n            alert(\"Error updating student. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDeleteStudent = async (student)=>{\n        if (confirm(\"Are you sure you want to delete \".concat(student.name, \"?\\n\\nThis action cannot be undone.\"))) {\n            try {\n                setLoading(true);\n                await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.deleteStudent(student.id);\n                await loadData();\n                alert(\"Student deleted successfully!\\n\\nData updated in \".concat(storageInfo.mode, \" storage.\"));\n            } catch (error) {\n                alert(\"Error deleting student. Please try again.\");\n            } finally{\n                setLoading(false);\n            }\n        }\n    };\n    const copyToClipboard = async (text, type)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n            setCopiedText(\"\".concat(type, \"-\").concat(text));\n            setTimeout(()=>setCopiedText(null), 2000);\n        } catch (error) {\n            alert(\"Failed to copy to clipboard\");\n        }\n    };\n    const resetForm = ()=>{\n        setNewStudent({\n            name: \"\",\n            phone: \"\",\n            email: \"\",\n            class: \"\",\n            department: \"\",\n            schedule: \"\",\n            image: \"\"\n        });\n        setImagePreview(null);\n        setImageFile(null);\n        setShowAddForm(false);\n        setEditingStudent(null);\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    const today = new Date().toISOString().slice(0, 10);\n    const totalStudents = students.length;\n    // Replace the following with your actual attendance/logs array if available\n    // For demonstration, using an empty array as placeholder\n    const logs = [] // Replace with actual logs source\n    ;\n    const todaysEntries = logs.filter((e)=>e.type === \"entry\" && e.timestamp.slice(0, 10) === today).length;\n    const todaysExits = logs.filter((e)=>e.type === \"exit\" && e.timestamp.slice(0, 10) === today).length;\n    const totalEntries = logs.filter((e)=>e.type === \"entry\").length;\n    const remainingStudents = totalStudents - todaysExits;\n    if (!isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 556,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-3xl\",\n                                            children: \"Admin Panel\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 567,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            className: \"text-lg\",\n                                            children: [\n                                                \"Student Management System - \",\n                                                storageInfo.mode,\n                                                \" Storage\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 568,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 566,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleRefresh,\n                                            variant: \"outline\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 574,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Refresh\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleLogout,\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Logout\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_12___default()), {\n                                            href: \"/\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                size: \"lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Home\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 581,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 565,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 564,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 563,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.Alert, {\n                    className: databaseConnected ? \"border-green-200 bg-green-50\" : \"border-yellow-200 bg-yellow-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            className: \"h-4 w-4 \".concat(databaseConnected ? \"text-green-600\" : \"text-yellow-600\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 594,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.AlertDescription, {\n                            className: databaseConnected ? \"text-green-800\" : \"text-yellow-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: [\n                                        storageInfo.mode,\n                                        \" Storage Active:\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 13\n                                }, this),\n                                \" \",\n                                databaseConnected ? \"Data syncs across all devices automatically\" : \"Data saved locally on this device (\".concat(storageInfo.studentsCount, \" students, \").concat(storageInfo.entriesCount, \" entries)\")\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 595,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 593,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"bg-blue-50 border-blue-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 sm:p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl sm:text-4xl font-bold text-blue-600 mb-2\",\n                                        children: stats.totalStudents\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 608,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm sm:text-base font-medium text-blue-700\",\n                                        children: \"Total Students\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-500 mt-1\",\n                                        children: \"Registered\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 614,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 607,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 606,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"bg-green-50 border-green-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 sm:p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl sm:text-4xl font-bold text-green-600 mb-2\",\n                                        children: stats.todayEntries\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm sm:text-base font-medium text-green-700\",\n                                        children: \"Total Entries\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-green-500 mt-1\",\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 622,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 621,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"bg-red-50 border-red-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 sm:p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl sm:text-4xl font-bold text-red-600 mb-2\",\n                                        children: stats.todayExits\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 638,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm sm:text-base font-medium text-red-700\",\n                                        children: \"Total Exits\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-red-500 mt-1\",\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 644,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 637,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 636,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"bg-purple-50 border-purple-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 sm:p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl sm:text-4xl font-bold text-purple-600 mb-2\",\n                                        children: stats.todayEntries + stats.todayExits\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 653,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm sm:text-base font-medium text-purple-700\",\n                                        children: \"Total Activity\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-purple-500 mt-1\",\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 659,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 652,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 651,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 604,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center gap-2 text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 669,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Auto-refreshing every 5 seconds\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 670,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs px-2 py-1 rounded \".concat(dataSource === 'cardstation' ? 'bg-green-100 text-green-700' : dataSource === 'local-api' ? 'bg-blue-100 text-blue-700' : 'bg-yellow-100 text-yellow-700'),\n                                    children: dataSource === 'cardstation' ? '📡 Cardstation Data' : dataSource === 'local-api' ? '🗄️ MongoDB Data' : '💾 Local Storage'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 671,\n                                    columnNumber: 13\n                                }, this),\n                                lastUpdated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: [\n                                        \"• \",\n                                        lastUpdated.toLocaleTimeString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 683,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 668,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: handleStatsRefresh,\n                            variant: \"outline\",\n                            size: \"sm\",\n                            className: \"text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"mr-1 h-3 w-3\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 694,\n                                    columnNumber: 13\n                                }, this),\n                                \"Refresh Stats\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 688,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 667,\n                    columnNumber: 9\n                }, this),\n                !showAddForm && !editingStudent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>setShowAddForm(true),\n                            className: \"w-full h-16 text-lg\",\n                            disabled: loading,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"mr-2 h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 704,\n                                    columnNumber: 17\n                                }, this),\n                                \"Add New Student\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 703,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 702,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 701,\n                    columnNumber: 11\n                }, this),\n                (showAddForm || editingStudent) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: editingStudent ? \"Edit Student\" : \"Add New Student\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 715,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: [\n                                        editingStudent ? \"Update student information\" : \"Fill required fields to register a new student\",\n                                        \" - Data will be saved in \",\n                                        storageInfo.mode,\n                                        \" storage\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 716,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 714,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            className: \"text-base font-semibold\",\n                                            children: \"Student Photo *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 724,\n                                            columnNumber: 17\n                                        }, this),\n                                        imagePreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: imagePreview || \"/placeholder.svg\",\n                                                            alt: \"Student preview\",\n                                                            className: \"w-32 h-32 rounded-full border-4 border-blue-200 object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 730,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: removeImage,\n                                                            size: \"sm\",\n                                                            variant: \"destructive\",\n                                                            className: \"absolute -top-2 -right-2 h-6 w-6 rounded-full p-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 741,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 735,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 729,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-green-600\",\n                                                            children: \"✅ Photo uploaded successfully\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 745,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: ()=>{\n                                                                var _fileInputRef_current;\n                                                                return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                            },\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 747,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Change Photo\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 746,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 744,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 728,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-16 w-16 mx-auto text-gray-400 mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 754,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4\",\n                                                    children: \"Upload student photo (Required)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 755,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: ()=>{\n                                                                var _fileInputRef_current;\n                                                                return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                            },\n                                                            variant: \"outline\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 758,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Upload Photo\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 757,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: takePhoto,\n                                                            variant: \"outline\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 762,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Take Photo\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 761,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 756,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-2\",\n                                                    children: \"Supported formats: JPG, PNG, GIF (Max 5MB)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 766,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 753,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            ref: fileInputRef,\n                                            type: \"file\",\n                                            accept: \"image/*\",\n                                            onChange: handleImageSelect,\n                                            className: \"hidden\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 771,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 723,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_9__.Separator, {}, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 780,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"name\",\n                                                    children: \"Student Name *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 785,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"name\",\n                                                    value: newStudent.name,\n                                                    onChange: (e)=>setNewStudent({\n                                                            ...newStudent,\n                                                            name: e.target.value\n                                                        }),\n                                                    placeholder: \"Enter full name\",\n                                                    disabled: loading\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 786,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 784,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"phone\",\n                                                    children: \"Phone Number *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 795,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"phone\",\n                                                    value: newStudent.phone,\n                                                    onChange: (e)=>setNewStudent({\n                                                            ...newStudent,\n                                                            phone: e.target.value\n                                                        }),\n                                                    placeholder: \"10-digit phone number\",\n                                                    maxLength: 10,\n                                                    disabled: loading\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 796,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 794,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"email\",\n                                                    children: \"Email Address\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 806,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"email\",\n                                                    type: \"email\",\n                                                    value: newStudent.email,\n                                                    onChange: (e)=>setNewStudent({\n                                                            ...newStudent,\n                                                            email: e.target.value\n                                                        }),\n                                                    placeholder: \"<EMAIL>\",\n                                                    disabled: loading\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 807,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 805,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"class\",\n                                                    children: \"Class *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 817,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: newStudent.class,\n                                                    onValueChange: (value)=>setNewStudent({\n                                                            ...newStudent,\n                                                            class: value\n                                                        }),\n                                                    disabled: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select class\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 824,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 823,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"10th-A\",\n                                                                    children: \"10th A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 827,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"10th-B\",\n                                                                    children: \"10th B\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 828,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"10th-C\",\n                                                                    children: \"10th C\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 829,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"11th-A\",\n                                                                    children: \"11th A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 830,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"11th-B\",\n                                                                    children: \"11th B\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 831,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"11th-C\",\n                                                                    children: \"11th C\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 832,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"12th-A\",\n                                                                    children: \"12th A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 833,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"12th-B\",\n                                                                    children: \"12th B\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 834,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"12th-C\",\n                                                                    children: \"12th C\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 835,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 826,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 818,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 816,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"department\",\n                                                    children: \"Department\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 840,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: newStudent.department,\n                                                    onValueChange: (value)=>setNewStudent({\n                                                            ...newStudent,\n                                                            department: value\n                                                        }),\n                                                    disabled: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select department\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 847,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 846,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Science\",\n                                                                    children: \"Science\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 850,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Commerce\",\n                                                                    children: \"Commerce\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 851,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Arts\",\n                                                                    children: \"Arts\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 852,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Computer Science\",\n                                                                    children: \"Computer Science\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 853,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 849,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 841,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 839,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"schedule\",\n                                                    children: \"Time Schedule\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 858,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: newStudent.schedule,\n                                                    onValueChange: (value)=>setNewStudent({\n                                                            ...newStudent,\n                                                            schedule: value\n                                                        }),\n                                                    disabled: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select schedule\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 865,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 864,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Morning Shift (8:00 AM - 2:00 PM)\",\n                                                                    children: \"Morning Shift (8:00 AM - 2:00 PM)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 868,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Afternoon Shift (2:00 PM - 8:00 PM)\",\n                                                                    children: \"Afternoon Shift (2:00 PM - 8:00 PM)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 871,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Full Day (8:00 AM - 4:00 PM)\",\n                                                                    children: \"Full Day (8:00 AM - 4:00 PM)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 874,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 867,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 859,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 857,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 783,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_9__.Separator, {}, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 880,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        editingStudent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleUpdateStudent,\n                                            className: \"flex-1\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 885,\n                                                    columnNumber: 21\n                                                }, this),\n                                                loading ? \"Updating...\" : \"Update Student\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 884,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleAddStudent,\n                                            className: \"flex-1\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 890,\n                                                    columnNumber: 21\n                                                }, this),\n                                                loading ? \"Adding...\" : \"Add Student\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 889,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: resetForm,\n                                            variant: \"outline\",\n                                            className: \"flex-1 bg-transparent\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 895,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Cancel\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 894,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 882,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 721,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 713,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: [\n                                        \"Registered Students (\",\n                                        students.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 906,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: [\n                                        \"All registered students with their login credentials - Stored in \",\n                                        storageInfo.mode,\n                                        \" storage\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 907,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 905,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: students.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"h-16 w-16 mx-auto text-gray-300 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 914,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-500 mb-2\",\n                                        children: \"No students registered yet\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 915,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: 'Click \"Add New Student\" to get started'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 916,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 913,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: students.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: student.image_url || \"/placeholder.svg?height=60&width=60\",\n                                                        alt: student.name,\n                                                        className: \"w-12 h-12 rounded-full border-2 border-gray-200 object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 923,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-lg\",\n                                                                children: student.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 929,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: [\n                                                                    student.class,\n                                                                    \" \",\n                                                                    student.department && \"- \".concat(student.department)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 930,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: student.phone\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 933,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            student.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: student.email\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 934,\n                                                                columnNumber: 43\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 928,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 922,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"font-mono text-xs\",\n                                                                        children: [\n                                                                            \"App: \",\n                                                                            student.application_number\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 942,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"ghost\",\n                                                                        onClick: ()=>copyToClipboard(student.application_number, \"app\"),\n                                                                        className: \"h-6 w-6 p-0\",\n                                                                        children: copiedText === \"app-\".concat(student.application_number) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 952,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 954,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 945,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 941,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                        variant: \"secondary\",\n                                                                        className: \"text-xs\",\n                                                                        children: [\n                                                                            \"Phone: \",\n                                                                            student.phone\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 959,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"ghost\",\n                                                                        onClick: ()=>copyToClipboard(student.phone, \"phone\"),\n                                                                        className: \"h-6 w-6 p-0\",\n                                                                        children: copiedText === \"phone-\".concat(student.phone) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 969,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 971,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 962,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 958,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 940,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleEditStudent(student),\n                                                                disabled: loading,\n                                                                className: \"h-8 w-8 p-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 986,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 979,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"destructive\",\n                                                                onClick: ()=>handleDeleteStudent(student),\n                                                                disabled: loading,\n                                                                className: \"h-8 w-8 p-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 995,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 988,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 978,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 938,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, student.id, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 921,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 919,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 911,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 904,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: [\n                                    \"Admin Instructions - \",\n                                    storageInfo.mode,\n                                    \" Storage\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 1009,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 1008,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-blue-700 mb-2\",\n                                                children: \"Required Fields:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 1014,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Student Name (Full name required)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1016,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Phone Number (10 digits, unique)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1017,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Class Selection (from dropdown)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1018,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Student Photo (Upload or camera)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1019,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCDD Email (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1020,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCDD Department (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1021,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCDD Schedule (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1022,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 1015,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 1013,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-green-700 mb-2\",\n                                                children: \"Photo Requirements:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 1026,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Clear face photo required\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1028,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 JPG, PNG, GIF formats supported\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1029,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Maximum file size: 5MB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1030,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Upload from device or take with camera\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1031,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Used for face verification at station\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1032,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Can be changed during editing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1033,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 1027,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 1025,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 1012,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 1011,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 1007,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 561,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 560,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPanel, \"gDv0hcFIWUxGhFUhf6ke2DYOoWQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AdminPanel;\nfunction StatCard(param) {\n    let { icon, value, label, color } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow p-6 flex items-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-\".concat(color, \"-500 text-3xl mr-4\"),\n                children: icon\n            }, void 0, false, {\n                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 1047,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-2xl font-bold\",\n                        children: value\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 1049,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600\",\n                        children: label\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 1050,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 1048,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 1046,\n        columnNumber: 5\n    }, this);\n}\n_c1 = StatCard;\nconst UserIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n        className: \"h-6 w-6 text-blue-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 1056,\n        columnNumber: 24\n    }, undefined);\n_c2 = UserIcon;\nconst EntryIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n        className: \"h-6 w-6 text-green-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 1057,\n        columnNumber: 25\n    }, undefined);\n_c3 = EntryIcon;\nconst ExitIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n        className: \"h-6 w-6 text-red-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 1058,\n        columnNumber: 24\n    }, undefined);\n_c4 = ExitIcon;\nconst TotalIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n        className: \"h-6 w-6 text-purple-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 1059,\n        columnNumber: 25\n    }, undefined);\n_c5 = TotalIcon;\nconst RemainIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n        className: \"h-6 w-6 text-orange-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 1060,\n        columnNumber: 26\n    }, undefined);\n_c6 = RemainIcon;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"AdminPanel\");\n$RefreshReg$(_c1, \"StatCard\");\n$RefreshReg$(_c2, \"UserIcon\");\n$RefreshReg$(_c3, \"EntryIcon\");\n$RefreshReg$(_c4, \"ExitIcon\");\n$RefreshReg$(_c5, \"TotalIcon\");\n$RefreshReg$(_c6, \"RemainIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/page.tsx\n"));

/***/ })

});