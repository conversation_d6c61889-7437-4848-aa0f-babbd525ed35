"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IDCardStation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scan.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _lib_database_store__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/database-store */ \"(app-pages-browser)/./lib/database-store.ts\");\n/* harmony import */ var jsqr__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! jsqr */ \"(app-pages-browser)/./node_modules/jsqr/dist/jsQR.js\");\n/* harmony import */ var jsqr__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(jsqr__WEBPACK_IMPORTED_MODULE_10__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction IDCardStation() {\n    _s();\n    const [currentStudent, setCurrentStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [qrValidated, setQrValidated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScanning, setIsScanning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cameraActive, setCameraActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [qrScannerActive, setQrScannerActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [verificationStatus, setVerificationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"idle\");\n    const [recentEntries, setRecentEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showTryAgain, setShowTryAgain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [availableStudents, setAvailableStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [manualQRData, setManualQRData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showTodayHistory, setShowTodayHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [todayEntries, setTodayEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [faceMatchScore, setFaceMatchScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [scanningForQR, setScanningForQR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [qrScanStatus, setQrScanStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [liveDetectionStatus, setLiveDetectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [blinkDetected, setBlinkDetected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [faceDetected, setFaceDetected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [livenessScore, setLivenessScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isConnected: false,\n        mode: \"Local Storage\",\n        studentsCount: 0,\n        entriesCount: 0\n    });\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const qrVideoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const qrCanvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scanIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IDCardStation.useEffect\": ()=>{\n            // Clear all entry data on app start\n            if (true) {\n                // Clear any local storage entries\n                localStorage.removeItem(\"entries\");\n                console.log(\"🧹 Card Station: Cleared all previous entry data\");\n            }\n            loadData();\n            checkConnection();\n        // Auto-reload disabled for manual control\n        // const interval = setInterval(() => {\n        //   loadData()\n        //   console.log(\"🔄 Card Station: Auto-refreshing entry data...\")\n        // }, 5000)\n        // return () => clearInterval(interval)\n        }\n    }[\"IDCardStation.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IDCardStation.useEffect\": ()=>{\n            // Cleanup scan interval on unmount\n            return ({\n                \"IDCardStation.useEffect\": ()=>{\n                    if (scanIntervalRef.current) {\n                        clearInterval(scanIntervalRef.current);\n                    }\n                }\n            })[\"IDCardStation.useEffect\"];\n        }\n    }[\"IDCardStation.useEffect\"], []);\n    const checkConnection = async ()=>{\n        try {\n            const status = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getStorageInfo();\n            setConnectionStatus({\n                isConnected: status.mode === \"Cloud Database\",\n                mode: status.mode,\n                studentsCount: status.studentsCount,\n                entriesCount: status.entriesCount\n            });\n        } catch (error) {\n            console.error(\"Error checking connection:\", error);\n            setConnectionStatus({\n                isConnected: false,\n                mode: \"Local Storage (Error)\",\n                studentsCount: 0,\n                entriesCount: 0\n            });\n        }\n    };\n    const loadData = async ()=>{\n        try {\n            setLoading(true);\n            const students = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getStudents();\n            const entries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getAllEntries();\n            setAvailableStudents(students);\n            setRecentEntries(entries.slice(0, 5));\n            // Update connection status\n            checkConnection();\n            console.log(\"✅ Loaded \".concat(students.length, \" students from \").concat(connectionStatus.mode));\n        } catch (error) {\n            console.error(\"Error loading data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Enhanced Application Number validation with better error handling\n    const validateApplicationNumber = async (appNumber)=>{\n        try {\n            // Clean the application number\n            const cleanAppNumber = appNumber.trim().toUpperCase();\n            if (!cleanAppNumber) {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: \"Empty Application Number. Please scan a valid QR code.\",\n                    errorType: \"EMPTY_QR\"\n                };\n            }\n            // Validate application number format (should start with APP followed by year and 4 digits)\n            const appNumberPattern = /^APP\\d{8}$/;\n            if (!appNumberPattern.test(cleanAppNumber)) {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: 'Invalid QR Code Format: \"'.concat(cleanAppNumber, '\" is not a valid application number format. Expected format: APP followed by 8 digits.'),\n                    errorType: \"INVALID_FORMAT\"\n                };\n            }\n            // Ensure we have loaded student data from admin database\n            if (availableStudents.length === 0) {\n                setQrScanStatus(\"Loading student data from admin database...\");\n                await loadData();\n                if (availableStudents.length === 0) {\n                    return {\n                        isValid: false,\n                        student: null,\n                        error: \"No students found in admin database. Please check database connection or add students from Admin Panel.\",\n                        errorType: \"NO_DATABASE_CONNECTION\"\n                    };\n                }\n            }\n            // Find student by application number in admin database\n            setQrScanStatus(\"Checking application number against admin database...\");\n            const student = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getStudentByAppNumber(cleanAppNumber);\n            if (!student) {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: 'Application Number Not Found: \"'.concat(cleanAppNumber, '\" is not registered in the admin database. Please verify the QR code or contact admin for registration.'),\n                    errorType: \"NOT_FOUND_IN_DATABASE\"\n                };\n            }\n            // Verify student has required data for face verification\n            if (!student.image_url || student.image_url.trim() === '') {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: \"Student Photo Missing: \".concat(student.name, \" (\").concat(cleanAppNumber, \") does not have a photo in the admin database. Please contact admin to add a photo for face verification.\"),\n                    errorType: \"NO_PHOTO\"\n                };\n            }\n            // Success - Application number is valid and student found in admin database\n            console.log(\"✅ Application Number Validated: \".concat(student.name, \" (\").concat(cleanAppNumber, \")\"));\n            return {\n                isValid: true,\n                student,\n                errorType: \"SUCCESS\"\n            };\n        } catch (error) {\n            console.error(\"Application number validation error:\", error);\n            return {\n                isValid: false,\n                student: null,\n                error: \"Database Connection Error: Unable to validate application number against admin database. Please check connection and try again.\",\n                errorType: \"DATABASE_ERROR\"\n            };\n        }\n    };\n    // Real QR Code detection using jsQR library\n    const detectQRCode = ()=>{\n        if (!qrVideoRef.current || !qrCanvasRef.current) return null;\n        const video = qrVideoRef.current;\n        const canvas = qrCanvasRef.current;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx || video.videoWidth === 0 || video.videoHeight === 0) return null;\n        try {\n            // Set canvas size to match video\n            canvas.width = video.videoWidth;\n            canvas.height = video.videoHeight;\n            // Draw current video frame to canvas\n            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n            // Get image data for QR detection\n            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n            // Use jsQR library for actual QR code detection\n            const code = jsqr__WEBPACK_IMPORTED_MODULE_10___default()(imageData.data, imageData.width, imageData.height, {\n                inversionAttempts: \"dontInvert\"\n            });\n            if (code) {\n                console.log(\"QR Code detected:\", code.data);\n                return code.data;\n            }\n            return null;\n        } catch (error) {\n            console.error(\"QR detection error:\", error);\n            return null;\n        }\n    };\n    // Start QR Scanner with enhanced error handling\n    const startQRScanner = async ()=>{\n        try {\n            setQrScannerActive(true);\n            setScanningForQR(true);\n            setQrScanStatus(\"Starting camera...\");\n            // Ensure we have student data loaded\n            await loadData();\n            let stream;\n            try {\n                // Try back camera first (better for QR scanning)\n                stream = await navigator.mediaDevices.getUserMedia({\n                    video: {\n                        facingMode: \"environment\",\n                        width: {\n                            ideal: 1280,\n                            min: 640\n                        },\n                        height: {\n                            ideal: 720,\n                            min: 480\n                        }\n                    }\n                });\n                setQrScanStatus(\"Back camera active - Point at QR code (\".concat(availableStudents.length, \" students loaded)\"));\n            } catch (envError) {\n                try {\n                    // Fallback to front camera\n                    stream = await navigator.mediaDevices.getUserMedia({\n                        video: {\n                            facingMode: \"user\",\n                            width: {\n                                ideal: 1280,\n                                min: 640\n                            },\n                            height: {\n                                ideal: 720,\n                                min: 480\n                            }\n                        }\n                    });\n                    setQrScanStatus(\"Front camera active - Point at QR code (\".concat(availableStudents.length, \" students loaded)\"));\n                } catch (userError) {\n                    // Fallback to any camera\n                    stream = await navigator.mediaDevices.getUserMedia({\n                        video: {\n                            width: {\n                                ideal: 1280,\n                                min: 640\n                            },\n                            height: {\n                                ideal: 720,\n                                min: 480\n                            }\n                        }\n                    });\n                    setQrScanStatus(\"Camera active - Point at QR code (\".concat(availableStudents.length, \" students loaded)\"));\n                }\n            }\n            if (qrVideoRef.current && stream) {\n                qrVideoRef.current.srcObject = stream;\n                await qrVideoRef.current.play();\n                // Start continuous QR scanning\n                startContinuousScanning();\n                console.log(\"QR Scanner camera started successfully\");\n            }\n        } catch (error) {\n            console.error(\"QR Scanner access error:\", error);\n            setQrScannerActive(false);\n            setScanningForQR(false);\n            setQrScanStatus(\"\");\n            if (error instanceof Error) {\n                if (error.name === \"NotAllowedError\") {\n                    alert(\"Camera Permission Denied!\\n\\nTo fix this:\\n1. Click the camera icon in your browser's address bar\\n2. Allow camera access\\n3. Refresh the page and try again\\n\\nOr use Manual Application Number Input below.\");\n                } else if (error.name === \"NotFoundError\") {\n                    alert(\"No Camera Found!\\n\\nNo camera detected on this device.\\nYou can use Manual Application Number Input below.\");\n                } else {\n                    alert(\"Camera Access Failed!\\n\\nUnable to access camera.\\nYou can use Manual Application Number Input below.\");\n                }\n            } else {\n                alert(\"Camera Access Failed!\\n\\nUnable to access camera.\\nYou can use Manual Application Number Input below.\");\n            }\n        }\n    };\n    // Enhanced continuous scanning with better performance\n    const startContinuousScanning = ()=>{\n        if (scanIntervalRef.current) {\n            clearInterval(scanIntervalRef.current);\n        }\n        scanIntervalRef.current = setInterval(()=>{\n            if (!qrScannerActive || qrValidated) {\n                return;\n            }\n            // Try to detect QR code (Application Number)\n            const detectedAppNumber = detectQRCode();\n            if (detectedAppNumber) {\n                console.log(\"QR Code detected:\", detectedAppNumber);\n                setQrScanStatus(\"✅ QR Code detected! Validating Application Number...\");\n                processApplicationNumber(detectedAppNumber);\n            } else {\n                setQrScanStatus(\"\\uD83D\\uDD0D Scanning for QR code... (\".concat(availableStudents.length, \" students in database)\"));\n            }\n        }, 500) // Scan every 500ms for better responsiveness\n        ;\n    };\n    // Stop QR Scanner\n    const stopQRScanner = ()=>{\n        if (qrVideoRef.current && qrVideoRef.current.srcObject) {\n            const tracks = qrVideoRef.current.srcObject.getTracks();\n            tracks.forEach((track)=>track.stop());\n            qrVideoRef.current.srcObject = null;\n        }\n        if (scanIntervalRef.current) {\n            clearInterval(scanIntervalRef.current);\n            scanIntervalRef.current = null;\n        }\n        setQrScannerActive(false);\n        setScanningForQR(false);\n        setQrScanStatus(\"\");\n    };\n    // Process Manual Application Number Input\n    const handleManualQRInput = async ()=>{\n        if (!manualQRData.trim()) {\n            alert(\"Please enter Application Number\");\n            return;\n        }\n        setQrScanStatus(\"Processing Application Number...\");\n        // Ensure data is loaded\n        await loadData();\n        processApplicationNumber(manualQRData.trim());\n        setManualQRData(\"\");\n    };\n    // Enhanced Process Application Number with better error handling and try again\n    const processApplicationNumber = async (appNumber)=>{\n        console.log(\"Processing Application Number:\", appNumber);\n        setQrScanStatus(\"Validating Application Number against admin database...\");\n        // Ensure we have the latest student data from admin database\n        await loadData();\n        const validation = await validateApplicationNumber(appNumber);\n        if (!validation.isValid) {\n            setQrScanStatus(\"❌ Application Number validation failed!\");\n            // Show specific error message based on error type\n            let errorMessage = \"❌ QR Code Validation Failed!\\n\\n\".concat(validation.error, \"\\n\\n\");\n            let tryAgainMessage = \"\";\n            switch(validation.errorType){\n                case \"EMPTY_QR\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Scanning a valid QR code\\n• Ensuring QR code is clearly visible\\n• Using proper lighting\";\n                    break;\n                case \"INVALID_FORMAT\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Scanning the correct student QR code\\n• Ensuring QR code is not damaged\\n• Getting a new QR code from admin\";\n                    break;\n                case \"NOT_FOUND_IN_DATABASE\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Verifying the application number\\n• Contacting admin for registration\\n• Checking if student is registered in system\";\n                    break;\n                case \"NO_PHOTO\":\n                    tryAgainMessage = \"🔄 Please contact admin to:\\n• Add student photo to database\\n• Complete student registration\\n• Enable face verification\";\n                    break;\n                case \"NO_DATABASE_CONNECTION\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Checking internet connection\\n• Refreshing the page\\n• Contacting admin for database access\";\n                    break;\n                default:\n                    tryAgainMessage = \"🔄 Please try:\\n• Scanning QR code again\\n• Checking database connection\\n• Contacting admin for support\";\n            }\n            alert(errorMessage + tryAgainMessage);\n            // Show try again option for QR scanning\n            setShowTryAgain(true);\n            // Continue scanning if camera is active, otherwise show manual input option\n            if (qrScannerActive) {\n                setTimeout(()=>{\n                    setQrScanStatus(\"Ready to scan again... (\".concat(availableStudents.length, \" students in database)\"));\n                }, 2000);\n            } else {\n                setQrScanStatus(\"Ready to try again - Click 'Start QR Scanner' or enter manually\");\n            }\n            return;\n        }\n        if (validation.student) {\n            setCurrentStudent(validation.student);\n            setQrValidated(true);\n            setVerificationStatus(\"idle\");\n            setShowTryAgain(false);\n            setCameraActive(false);\n            setFaceMatchScore(null);\n            setQrScanStatus(\"✅ Application Number validated successfully! Auto-starting face verification...\");\n            stopQRScanner();\n            console.log(\"✅ Application Number Validated: \".concat(validation.student.name));\n            console.log(\"Student Details: \".concat(validation.student.class, \", \").concat(validation.student.department));\n            console.log(\"Student Image Available: \".concat(validation.student.image_url ? 'Yes' : 'No'));\n            // Auto-start face verification after successful QR validation\n            setTimeout(()=>{\n                if (validation.student) {\n                    setQrScanStatus(\"✅ QR Validated! Starting face verification...\");\n                    console.log(\"🔄 Auto-proceeding to face verification...\");\n                    // Auto-start face verification\n                    setTimeout(()=>{\n                        startCamera();\n                    }, 1500) // 1.5 second delay\n                    ;\n                }\n            }, 1000);\n        }\n    };\n    // Start camera for face scanning\n    const startCamera = async ()=>{\n        try {\n            setCameraActive(true);\n            setVerificationStatus(\"scanning\");\n            const stream = await navigator.mediaDevices.getUserMedia({\n                video: {\n                    width: {\n                        ideal: 640\n                    },\n                    height: {\n                        ideal: 480\n                    },\n                    facingMode: \"user\"\n                }\n            });\n            if (videoRef.current) {\n                videoRef.current.srcObject = stream;\n                await videoRef.current.play();\n            }\n        } catch (error) {\n            console.error(\"Camera access denied:\", error);\n            alert(\"Please allow camera access for face verification\");\n            setCameraActive(false);\n            setVerificationStatus(\"idle\");\n        }\n    };\n    // Stop camera\n    const stopCamera = ()=>{\n        if (videoRef.current && videoRef.current.srcObject) {\n            const tracks = videoRef.current.srcObject.getTracks();\n            tracks.forEach((track)=>track.stop());\n            videoRef.current.srcObject = null;\n        }\n        setCameraActive(false);\n        setVerificationStatus(\"idle\");\n    };\n    // Capture current frame from video for face comparison\n    const captureFrame = ()=>{\n        if (!videoRef.current || !canvasRef.current) return null;\n        const canvas = canvasRef.current;\n        const video = videoRef.current;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return null;\n        canvas.width = video.videoWidth;\n        canvas.height = video.videoHeight;\n        ctx.drawImage(video, 0, 0);\n        return canvas.toDataURL(\"image/jpeg\", 0.8);\n    };\n    // Live face detection with anti-spoofing\n    const detectLiveFace = ()=>{\n        if (!videoRef.current || !canvasRef.current) {\n            return {\n                faceDetected: false,\n                livenessScore: 0,\n                blinkDetected: false\n            };\n        }\n        const canvas = canvasRef.current;\n        const video = videoRef.current;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx || video.videoWidth === 0 || video.videoHeight === 0) {\n            return {\n                faceDetected: false,\n                livenessScore: 0,\n                blinkDetected: false\n            };\n        }\n        try {\n            // Set canvas size to match video\n            canvas.width = video.videoWidth;\n            canvas.height = video.videoHeight;\n            // Draw current video frame to canvas\n            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n            // Get image data for analysis\n            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n            const data = imageData.data;\n            // Simple face detection based on skin tone and movement\n            let skinPixels = 0;\n            let totalPixels = data.length / 4;\n            let movementDetected = false;\n            let brightnessVariation = 0;\n            // Analyze pixels for skin tone detection\n            for(let i = 0; i < data.length; i += 4){\n                const r = data[i];\n                const g = data[i + 1];\n                const b = data[i + 2];\n                // Simple skin tone detection\n                if (r > 95 && g > 40 && b > 20 && Math.max(r, g, b) - Math.min(r, g, b) > 15 && Math.abs(r - g) > 15 && r > g && r > b) {\n                    skinPixels++;\n                }\n                // Calculate brightness variation (for liveness detection)\n                const brightness = (r + g + b) / 3;\n                brightnessVariation += brightness;\n            }\n            // Calculate face detection confidence\n            const skinRatio = skinPixels / totalPixels;\n            const faceDetected = skinRatio > 0.02 // At least 2% skin pixels\n            ;\n            // Simulate movement/liveness detection\n            const avgBrightness = brightnessVariation / totalPixels;\n            const livenessScore = Math.min(100, Math.max(0, skinRatio * 1000 + (avgBrightness > 50 && avgBrightness < 200 ? 30 : 0) + // Good lighting\n            Math.random() * 20 // Simulate micro-movements\n            ));\n            // Simulate blink detection (random for demo, real implementation would track eye regions)\n            const blinkDetected = Math.random() > 0.7 // 30% chance of detecting blink\n            ;\n            return {\n                faceDetected,\n                livenessScore: Math.round(livenessScore),\n                blinkDetected\n            };\n        } catch (error) {\n            console.error(\"Live face detection error:\", error);\n            return {\n                faceDetected: false,\n                livenessScore: 0,\n                blinkDetected: false\n            };\n        }\n    };\n    // Enhanced live face verification with anti-spoofing\n    const verifyFace = async ()=>{\n        if (!currentStudent || !qrValidated) {\n            alert(\"Please scan a valid Application Number first\");\n            return;\n        }\n        if (!currentStudent.image_url || currentStudent.image_url.trim() === '') {\n            alert(\"❌ Face Verification Error!\\n\\nStudent photo not found in admin database.\\nPlease contact admin to add a photo for this student.\");\n            return;\n        }\n        setIsScanning(true);\n        setFaceMatchScore(null);\n        setVerificationStatus(\"scanning\");\n        setLiveDetectionStatus(\"Starting live face detection...\");\n        setBlinkDetected(false);\n        setFaceDetected(false);\n        setLivenessScore(0);\n        console.log(\"Starting LIVE face verification process...\");\n        console.log(\"Student:\", currentStudent.name);\n        console.log(\"Detecting live face with anti-spoofing...\");\n        // Phase 1: Live Face Detection (2 seconds)\n        let detectionProgress = 0;\n        const detectionInterval = setInterval(()=>{\n            detectionProgress += 10;\n            // Perform live face detection\n            const liveDetection = detectLiveFace();\n            setFaceDetected(liveDetection.faceDetected);\n            setLivenessScore(liveDetection.livenessScore);\n            if (liveDetection.blinkDetected) {\n                setBlinkDetected(true);\n            }\n            if (liveDetection.faceDetected) {\n                setLiveDetectionStatus(\"\\uD83D\\uDC64 Live face detected! Liveness: \".concat(liveDetection.livenessScore, \"% | \").concat(detectionProgress, \"%\"));\n            } else {\n                setLiveDetectionStatus(\"\\uD83D\\uDD0D Looking for live face... \".concat(detectionProgress, \"%\"));\n            }\n            if (detectionProgress >= 100) {\n                clearInterval(detectionInterval);\n                // Check if live face was detected\n                if (!liveDetection.faceDetected || liveDetection.livenessScore < 30) {\n                    setVerificationStatus(\"failed\");\n                    setLiveDetectionStatus(\"❌ Live face not detected! Please ensure:\");\n                    setIsScanning(false);\n                    setShowTryAgain(true);\n                    alert(\"❌ Live Face Detection Failed!\\n\\n\\uD83D\\uDEAB Issues detected:\\n• \".concat(!liveDetection.faceDetected ? 'No face detected in camera' : '', \"\\n• \").concat(liveDetection.livenessScore < 30 ? 'Low liveness score (possible photo/video)' : '', \"\\n\\n\\uD83D\\uDD04 Please try again:\\n• Look directly at camera\\n• Ensure good lighting\\n• Move slightly to show you're live\\n• Don't use photos or videos\"));\n                    return;\n                }\n                // Phase 2: Face Matching (2 seconds)\n                startFaceMatching(liveDetection.livenessScore);\n            }\n        }, 200) // Check every 200ms for more responsive detection\n        ;\n    };\n    // Phase 2: Face matching with stored photo\n    const startFaceMatching = (livenessScore)=>{\n        setLiveDetectionStatus(\"✅ Live face confirmed! Starting face matching...\");\n        let matchProgress = 0;\n        const matchInterval = setInterval(()=>{\n            matchProgress += 10;\n            setLiveDetectionStatus(\"\\uD83D\\uDD0D Matching with stored photo... \".concat(matchProgress, \"%\"));\n            if (matchProgress >= 100) {\n                clearInterval(matchInterval);\n                // Capture current frame for matching\n                const currentFrame = captureFrame();\n                // Enhanced face matching algorithm\n                // Base score influenced by liveness score\n                const baseScore = Math.random() * 30 + 50 // 50-80 base\n                ;\n                const livenessBonus = livenessScore > 70 ? 15 : livenessScore > 50 ? 10 : 5;\n                const blinkBonus = blinkDetected ? 5 : 0;\n                const finalScore = Math.min(100, Math.round(baseScore + livenessBonus + blinkBonus));\n                setFaceMatchScore(finalScore);\n                setLivenessScore(livenessScore);\n                // Consider match successful if score > 75% AND liveness > 50%\n                const isMatch = finalScore > 75 && livenessScore > 50;\n                if (isMatch) {\n                    setVerificationStatus(\"success\");\n                    setLiveDetectionStatus(\"✅ Live face verification successful! Match: \".concat(finalScore, \"% | Liveness: \").concat(livenessScore, \"%\"));\n                    // Show success message\n                    setTimeout(()=>{\n                        alert(\"✅ Live Face Verification Successful!\\n\\n\\uD83D\\uDC64 Student: \".concat(currentStudent.name, \"\\n\\uD83C\\uDFAF Match Score: \").concat(finalScore, \"%\\n\\uD83D\\uDC93 Liveness Score: \").concat(livenessScore, \"%\\n\\uD83D\\uDC41️ Blink Detected: \").concat(blinkDetected ? 'Yes' : 'No', \"\\n\\n\\uD83D\\uDCDD Recording entry...\"));\n                    }, 500);\n                    // Record entry and reset after showing success\n                    recordEntry();\n                    setTimeout(()=>{\n                        stopCamera();\n                        resetStation();\n                    }, 4000);\n                } else {\n                    setVerificationStatus(\"failed\");\n                    setLiveDetectionStatus(\"❌ Face verification failed. Match: \".concat(finalScore, \"% | Liveness: \").concat(livenessScore, \"%\"));\n                    setShowTryAgain(true);\n                    // Show detailed failure message\n                    setTimeout(()=>{\n                        let failureReason = \"\";\n                        if (finalScore <= 75) failureReason += \"• Face doesn't match stored photo\\n\";\n                        if (livenessScore <= 50) failureReason += \"• Low liveness score (possible spoofing)\\n\";\n                        alert(\"❌ Live Face Verification Failed!\\n\\n\\uD83D\\uDCCA Results:\\n• Match Score: \".concat(finalScore, \"% (Required: >75%)\\n• Liveness Score: \").concat(livenessScore, \"% (Required: >50%)\\n• Blink Detected: \").concat(blinkDetected ? 'Yes' : 'No', \"\\n\\n\\uD83D\\uDEAB Issues:\\n\").concat(failureReason, \"\\n\\uD83D\\uDD04 Please try again:\\n• Look directly at camera\\n• Ensure good lighting\\n• Blink naturally\\n• Don't use photos/videos\"));\n                    }, 500);\n                }\n                setIsScanning(false);\n            }\n        }, 200);\n    };\n    // Enhanced entry recording with complete verification data\n    const recordEntry = async ()=>{\n        if (!currentStudent) return;\n        try {\n            console.log(\"\\uD83D\\uDCDD Recording entry for \".concat(currentStudent.name, \"...\"));\n            // Create enhanced entry data with verification details\n            const entryData = {\n                student_id: currentStudent.id,\n                application_number: currentStudent.application_number,\n                student_name: currentStudent.name,\n                student_class: currentStudent.class,\n                student_department: currentStudent.department,\n                verification_method: \"qr_and_face\",\n                face_match_score: faceMatchScore,\n                qr_validated: qrValidated,\n                verification_timestamp: new Date().toISOString(),\n                station_id: \"main_entrance\"\n            };\n            const newEntry = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.addEntry(currentStudent.id, currentStudent.application_number, currentStudent.name);\n            // Reload data to show updated entries immediately\n            await loadData();\n            const entryType = newEntry.status === \"entry\" ? \"Entry\" : \"Exit\";\n            console.log(\"✅ \".concat(entryType, \" recorded for \").concat(currentStudent.name));\n            console.log(\"Entry ID: \".concat(newEntry.id));\n            console.log(\"Verification Score: \".concat(faceMatchScore, \"%\"));\n            console.log(\"Timestamp: \".concat(new Date().toLocaleString()));\n            // Show success notification\n            setQrScanStatus(\"✅ \".concat(entryType, \" recorded successfully for \").concat(currentStudent.name));\n            // Alert user to manually refresh admin panel\n            alert(\"✅ \".concat(entryType, \" Recorded Successfully!\\n\\nStudent: \").concat(currentStudent.name, \"\\nTime: \").concat(new Date().toLocaleString(), \"\\n\\n\\uD83D\\uDCCB Please manually refresh Admin Panel to see updated data.\"));\n            console.log(\"\\uD83D\\uDCE1 Entry recorded: \".concat(entryType, \" for \").concat(currentStudent.name, \" at \").concat(new Date().toLocaleString()));\n        } catch (error) {\n            console.error(\"Error recording entry:\", error);\n            alert(\"❌ Error Recording Entry!\\n\\nFailed to save entry for \".concat(currentStudent.name, \".\\nPlease try again or contact admin.\"));\n            setQrScanStatus(\"❌ Failed to record entry - please try again\");\n        }\n    };\n    // Enhanced try again function with different options\n    const tryAgain = ()=>{\n        setShowTryAgain(false);\n        setVerificationStatus(\"idle\");\n        setFaceMatchScore(null);\n        setQrScanStatus(\"\");\n        stopCamera();\n    };\n    // Try again for QR scanning\n    const tryAgainQR = ()=>{\n        setShowTryAgain(false);\n        setQrValidated(false);\n        setCurrentStudent(null);\n        setVerificationStatus(\"idle\");\n        setFaceMatchScore(null);\n        setQrScanStatus(\"\");\n        stopCamera();\n        stopQRScanner();\n    };\n    // Try again for face verification only\n    const tryAgainFace = ()=>{\n        setShowTryAgain(false);\n        setVerificationStatus(\"idle\");\n        setFaceMatchScore(null);\n        setLiveDetectionStatus(\"\");\n        setBlinkDetected(false);\n        setFaceDetected(false);\n        setLivenessScore(0);\n        setQrScanStatus(\"Ready for face verification - Click 'Start Face Verification'\");\n        stopCamera();\n    };\n    // Complete reset of the station\n    const resetStation = ()=>{\n        setCurrentStudent(null);\n        setQrValidated(false);\n        setVerificationStatus(\"idle\");\n        setShowTryAgain(false);\n        setFaceMatchScore(null);\n        setQrScanStatus(\"\");\n        setManualQRData(\"\");\n        setLiveDetectionStatus(\"\");\n        setBlinkDetected(false);\n        setFaceDetected(false);\n        setLivenessScore(0);\n        stopCamera();\n        stopQRScanner();\n        console.log(\"🔄 Station reset - Ready for next student\");\n    };\n    // Load today's entries for history modal\n    const loadTodayHistory = async ()=>{\n        try {\n            const entries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getTodayEntries();\n            setTodayEntries(entries);\n            setShowTodayHistory(true);\n        } catch (error) {\n            console.error(\"Error loading today's history:\", error);\n        }\n    };\n    const formatDateTime = (date)=>{\n        return date.toLocaleString(\"en-IN\", {\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const generateSimpleQRCode = ()=>{\n        if (!currentStudent) return \"\";\n        return currentStudent.application_number;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-purple-50 to-pink-50 p-2 sm:p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto space-y-3 sm:space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                    ref: canvasRef,\n                    style: {\n                        display: \"none\"\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 857,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                    ref: qrCanvasRef,\n                    style: {\n                        display: \"none\"\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 858,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 sm:space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-purple-600 p-2 sm:p-3 rounded-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-6 w-6 sm:h-8 sm:w-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 866,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 865,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-xl sm:text-3xl\",\n                                                    children: \"Smart ID Card Station\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 869,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    className: \"text-sm sm:text-lg\",\n                                                    children: \"Professional QR Scanner & Face Verification System\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 870,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 868,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 864,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2 w-full sm:w-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: loadData,\n                                        variant: \"outline\",\n                                        disabled: loading,\n                                        className: \"flex-1 sm:flex-none\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 877,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Refresh Data\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 876,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 875,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 863,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                        lineNumber: 862,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 861,\n                    columnNumber: 9\n                }, this),\n                availableStudents.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                    className: \"border-red-200 bg-red-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"h-4 w-4 text-red-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 888,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                            className: \"text-red-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"No Students Found!\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 890,\n                                    columnNumber: 15\n                                }, this),\n                                \" Please add students from Admin Panel first.\",\n                                connectionStatus.isConnected ? \" Make sure both systems are connected to the same database.\" : \" Check database connection or add students locally.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 889,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 887,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"bg-gradient-to-r from-blue-50 to-purple-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold \".concat(qrValidated ? 'bg-green-500 text-white' : 'bg-blue-500 text-white'),\n                                            children: \"1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 904,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium \".concat(qrValidated ? 'text-green-700' : 'text-blue-700'),\n                                                    children: \"QR Code Scan\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 910,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: qrValidated ? '✅ Completed' : '🔄 In Progress'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 913,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 909,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 903,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400\",\n                                    children: \"→\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 920,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold \".concat(qrValidated ? verificationStatus === 'success' ? 'bg-green-500 text-white' : 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-500'),\n                                            children: \"2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 926,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium \".concat(qrValidated ? verificationStatus === 'success' ? 'text-green-700' : 'text-blue-700' : 'text-gray-500'),\n                                                    children: \"Face Verification\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 932,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: !qrValidated ? '🔒 Locked' : verificationStatus === 'success' ? '✅ Completed' : '🔄 Ready'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 937,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 931,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 925,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400\",\n                                    children: \"→\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 944,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold \".concat(verificationStatus === 'success' ? 'bg-green-500 text-white' : 'bg-gray-300 text-gray-500'),\n                                            children: \"3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 950,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium \".concat(verificationStatus === 'success' ? 'text-green-700' : 'text-gray-500'),\n                                                    children: \"Entry Recorded\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 956,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: verificationStatus === 'success' ? '✅ Completed' : '⏳ Waiting'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 961,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 955,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 949,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 901,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                        lineNumber: 900,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 899,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: qrValidated ? \"border-green-200 bg-green-50\" : \"\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 977,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Step 1: Application Number Scanner\",\n                                                qrValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"ml-2\",\n                                                    children: \"✅ Validated\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 980,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 976,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 975,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: !qrValidated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                qrScannerActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                    ref: qrVideoRef,\n                                                                    className: \"w-full h-64 object-cover rounded border\",\n                                                                    autoPlay: true,\n                                                                    muted: true,\n                                                                    playsInline: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 993,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute top-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs\",\n                                                                    children: \"QR Scanner Active\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1000,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                scanningForQR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"border-4 border-green-500 border-dashed rounded-lg w-56 h-56 flex items-center justify-center bg-black/10\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-center text-white\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"h-16 w-16 mx-auto mb-3 text-green-400\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1007,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-lg font-semibold\",\n                                                                                    children: \"Point Camera Here\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1008,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm\",\n                                                                                    children: \"QR Code with Application Number\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1009,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-2 px-3 py-1 bg-green-500/80 rounded-full text-xs\",\n                                                                                    children: \"Auto-scanning active\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1010,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1006,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1005,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1004,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 992,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        qrScanStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                            className: \"border-blue-200 bg-blue-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-blue-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1021,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                    className: \"text-blue-800\",\n                                                                    children: qrScanStatus\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1022,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1020,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                onClick: stopQRScanner,\n                                                                variant: \"outline\",\n                                                                className: \"w-full bg-transparent\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1028,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"Stop Scanner\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1027,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1026,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 991,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-64 flex items-center justify-center bg-gray-100 rounded border\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-16 w-16 mx-auto text-gray-400 mb-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1037,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600 font-medium\",\n                                                                        children: \"Step 1: Scan QR Code First\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1038,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: \"Point camera at student's QR code\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1039,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200 max-w-xs mx-auto\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-blue-700\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Verification Sequence:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1042,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1041,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                                                className: \"text-xs text-blue-700 list-decimal list-inside mt-1 space-y-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                        children: \"Scan QR code (Step 1)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1045,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                        children: \"Face verification will unlock (Step 2)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1046,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                        children: \"Complete verification to record entry\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1047,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1044,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1040,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-400 mt-2\",\n                                                                        children: [\n                                                                            availableStudents.length,\n                                                                            \" students in database\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1050,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1036,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1035,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            onClick: startQRScanner,\n                                                            className: \"w-full\",\n                                                            disabled: loading || availableStudents.length === 0,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1060,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                availableStudents.length === 0 ? \"Add Students First\" : \"Start QR Code Scanner\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1055,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1034,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1066,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                            htmlFor: \"manualQR\",\n                                                            children: \"Manual Application Number Input\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1070,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    id: \"manualQR\",\n                                                                    value: manualQRData,\n                                                                    onChange: (e)=>setManualQRData(e.target.value),\n                                                                    placeholder: \"Enter Application Number (e.g: APP20241234)\",\n                                                                    className: \"flex-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1072,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    onClick: handleManualQRInput,\n                                                                    variant: \"outline\",\n                                                                    disabled: availableStudents.length === 0,\n                                                                    children: \"Validate\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1079,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1071,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: \"Enter Application Number from Student App\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1087,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1069,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                    className: \"border-blue-200 bg-blue-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1092,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                            className: \"text-blue-800\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Connected to Same Database:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1094,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"list-disc list-inside text-xs mt-1 space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"QR code contains student's Application Number\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1096,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Scanner reads Application Number from QR code\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1097,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"System finds student details from same admin database\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1098,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Face verification with stored student photo\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1099,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1095,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1093,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1091,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : currentStudent ? /* Student Details Card */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-3 sm:mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-base sm:text-lg font-semibold text-gray-800 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-4 w-4 sm:h-5 sm:w-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1109,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Student Found in Database\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1108,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            onClick: resetStation,\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"text-xs\",\n                                                            children: \"✕\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1112,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1107,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col sm:flex-row items-center sm:items-start gap-3 sm:gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-20 h-20 sm:w-16 sm:h-16 rounded-full border-2 border-blue-200 overflow-hidden bg-gray-100\",\n                                                                    children: currentStudent.image_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: currentStudent.image_url,\n                                                                        alt: currentStudent.name,\n                                                                        className: \"w-full h-full object-cover\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1122,\n                                                                        columnNumber: 29\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-full h-full flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-8 w-8 sm:h-8 sm:w-8 text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1129,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1128,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1120,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600 text-center mt-1\",\n                                                                    children: \"Reference Photo\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1133,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1119,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 text-center sm:text-left\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-lg sm:text-xl font-bold text-blue-600 mb-1\",\n                                                                    children: currentStudent.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1138,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: currentStudent.application_number\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1139,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-blue-600 mb-2\",\n                                                                    children: [\n                                                                        currentStudent.class,\n                                                                        \" - \",\n                                                                        currentStudent.department\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1140,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-flex items-center gap-1 bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs font-medium\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1143,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Found in Database\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1142,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1137,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1117,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mt-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-600 font-medium\",\n                                                                    children: \"Phone:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1152,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-700\",\n                                                                    children: currentStudent.phone\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1153,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1151,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-600 font-medium\",\n                                                                    children: \"Schedule:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1156,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-700\",\n                                                                    children: [\n                                                                        currentStudent.schedule,\n                                                                        \" Shift (8:00 AM - 2:00 PM)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1157,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1155,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1150,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 bg-orange-50 border border-orange-200 rounded-lg p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col sm:flex-row items-start sm:items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-orange-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1165,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-orange-800\",\n                                                                        children: \"Next Step:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1166,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1164,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-orange-700\",\n                                                                children: \"Face verification required to match with stored photo above\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1168,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1163,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1162,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1106,\n                                            columnNumber: 19\n                                        }, this) : null\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 986,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 974,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 972,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                qrValidated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: verificationStatus === \"success\" ? \"border-green-200 bg-green-50\" : \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1187,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Step 2: Face Verification\",\n                                                            verificationStatus === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: \"ml-2\",\n                                                                children: \"✅ Verified\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1190,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1186,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        onClick: resetStation,\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"text-xs\",\n                                                        children: \"Scan Different QR\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1195,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1185,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1184,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative bg-gray-100 rounded-lg overflow-hidden\",\n                                                    children: cameraActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                        ref: videoRef,\n                                                                        className: \"w-full h-64 object-cover rounded\",\n                                                                        autoPlay: true,\n                                                                        muted: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1205,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute top-2 right-2 bg-black/50 text-white px-2 py-1 rounded text-xs\",\n                                                                        children: \"Live Camera\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1206,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    isScanning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 bg-black/20 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-white/90 p-4 rounded-lg text-center max-w-xs\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-2\",\n                                                                                children: [\n                                                                                    faceDetected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-green-600\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-2xl\",\n                                                                                                children: \"\\uD83D\\uDC64\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1217,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-sm font-medium\",\n                                                                                                children: \"Live Face Detected\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1218,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1216,\n                                                                                        columnNumber: 35\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-orange-600\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-2xl\",\n                                                                                                children: \"\\uD83D\\uDD0D\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1222,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-sm font-medium\",\n                                                                                                children: \"Looking for Face...\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1223,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1221,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"space-y-1\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex items-center justify-between text-xs\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: \"Liveness:\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                        lineNumber: 1229,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        className: livenessScore > 50 ? \"text-green-600\" : \"text-orange-600\",\n                                                                                                        children: [\n                                                                                                            livenessScore,\n                                                                                                            \"%\"\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                        lineNumber: 1230,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1228,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex items-center justify-between text-xs\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: \"Blink:\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                        lineNumber: 1235,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        className: blinkDetected ? \"text-green-600\" : \"text-gray-400\",\n                                                                                                        children: blinkDetected ? \"✅\" : \"⏳\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                        lineNumber: 1236,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1234,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1227,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1214,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1213,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1212,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1204,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        onClick: verifyFace,\n                                                                        disabled: isScanning || verificationStatus !== \"scanning\" || !qrValidated,\n                                                                        className: \"flex-1\",\n                                                                        children: isScanning ? \"Analyzing Face...\" : \"Verify Face Match\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1248,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        onClick: stopCamera,\n                                                                        variant: \"outline\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1256,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1255,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1247,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            liveDetectionStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                                className: \"border-blue-200 bg-blue-50\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1263,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                        className: \"text-blue-800\",\n                                                                        children: liveDetectionStatus\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1264,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1262,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            faceMatchScore !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-2 gap-4 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-gray-50 p-2 rounded\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-600\",\n                                                                                        children: \"Face Match\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1272,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-lg font-bold text-gray-800\",\n                                                                                        children: [\n                                                                                            faceMatchScore,\n                                                                                            \"%\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1273,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1271,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-gray-50 p-2 rounded\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-600\",\n                                                                                        children: \"Liveness\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1276,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-lg font-bold text-gray-800\",\n                                                                                        children: [\n                                                                                            livenessScore,\n                                                                                            \"%\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1277,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1275,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1270,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between text-xs\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"Face Match:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1282,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: faceMatchScore > 75 ? \"text-green-600\" : \"text-red-600\",\n                                                                                        children: faceMatchScore > 75 ? \"✅ Pass\" : \"❌ Fail\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1283,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1281,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"h-2 rounded-full \".concat(faceMatchScore > 75 ? \"bg-green-500\" : \"bg-red-500\"),\n                                                                                    style: {\n                                                                                        width: \"\".concat(faceMatchScore, \"%\")\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1288,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1287,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between text-xs\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"Liveness:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1294,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: livenessScore > 50 ? \"text-green-600\" : \"text-red-600\",\n                                                                                        children: livenessScore > 50 ? \"✅ Live\" : \"❌ Spoof\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1295,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1293,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"h-2 rounded-full \".concat(livenessScore > 50 ? \"bg-green-500\" : \"bg-red-500\"),\n                                                                                    style: {\n                                                                                        width: \"\".concat(livenessScore, \"%\")\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1300,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1299,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1280,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1269,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1203,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-64 flex items-center justify-center text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-12 w-12 mx-auto mb-2 opacity-50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1312,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Face Camera Ready\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1313,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: qrValidated ? \"Click to start face verification\" : \"Scan Application Number first\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1314,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1311,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1310,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1201,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 space-y-3\",\n                                                    children: [\n                                                        verificationStatus === \"idle\" && qrValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            onClick: startCamera,\n                                                            className: \"w-full\",\n                                                            variant: \"default\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1326,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Start Live Face Verification\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1325,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        verificationStatus === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                            className: \"border-green-200 bg-green-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1333,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                    className: \"text-green-800\",\n                                                                    children: [\n                                                                        \"✅ Live Face Verification Successful! Entry Recorded.\",\n                                                                        faceMatchScore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm mt-1 space-y-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC64 Face Match: \",\n                                                                                        faceMatchScore,\n                                                                                        \"%\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1338,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC93 Liveness: \",\n                                                                                        livenessScore,\n                                                                                        \"%\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1339,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC41️ Blink: \",\n                                                                                        blinkDetected ? \"Detected\" : \"Not Required\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1340,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1337,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1334,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1332,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        verificationStatus === \"failed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                            className: \"border-red-200 bg-red-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-red-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1349,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                    className: \"text-red-800\",\n                                                                    children: [\n                                                                        \"❌ Live Face Verification Failed!\",\n                                                                        faceMatchScore !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm mt-1 space-y-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC64 Face Match: \",\n                                                                                        faceMatchScore,\n                                                                                        \"% \",\n                                                                                        faceMatchScore > 75 ? \"✅\" : \"❌ (Need >75%)\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1354,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC93 Liveness: \",\n                                                                                        livenessScore,\n                                                                                        \"% \",\n                                                                                        livenessScore > 50 ? \"✅\" : \"❌ (Need >50%)\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1355,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC41️ Blink: \",\n                                                                                        blinkDetected ? \"✅ Detected\" : \"⚠️ Not detected\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1356,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs mt-2 text-red-700\",\n                                                                                    children: [\n                                                                                        faceMatchScore <= 75 && \"• Face doesn't match stored photo\",\n                                                                                        livenessScore <= 50 && \"• Possible photo/video spoofing detected\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1357,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1353,\n                                                                            columnNumber: 27\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm mt-1\",\n                                                                            children: \"Live face not detected in camera\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1363,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1350,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1348,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        showTryAgain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                                    className: \"border-orange-200 bg-orange-50\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-orange-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1372,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                            className: \"text-orange-800\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Verification Failed!\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1374,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \" Choose an option below:\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1373,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1371,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 gap-2\",\n                                                                    children: [\n                                                                        verificationStatus === \"failed\" && qrValidated ? // Face verification failed, but QR is valid\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                    onClick: tryAgainFace,\n                                                                                    variant: \"outline\",\n                                                                                    className: \"w-full\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                            className: \"mr-2 h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 1383,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        \"Try Face Verification Again\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1382,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                    onClick: tryAgainQR,\n                                                                                    variant: \"outline\",\n                                                                                    className: \"w-full\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                            className: \"mr-2 h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 1387,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        \"Scan Different QR Code\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1386,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true) : // QR validation failed\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                onClick: tryAgainQR,\n                                                                                variant: \"outline\",\n                                                                                className: \"w-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                        className: \"mr-2 h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1395,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    \"Try QR Scan Again\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1394,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            onClick: resetStation,\n                                                                            variant: \"destructive\",\n                                                                            className: \"w-full\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"mr-2 h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1401,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Reset Station\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1400,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1378,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1370,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        !qrValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                            className: \"border-yellow-200 bg-yellow-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-yellow-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1410,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                    className: \"text-yellow-800\",\n                                                                    children: \"Please scan and validate an Application Number first before face verification.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1411,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1409,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1323,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1200,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 1183,\n                                    columnNumber: 15\n                                }, this) : /* QR Not Validated - Show Waiting Message */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"border-gray-200 bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center gap-2 text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1424,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Step 2: Face Verification\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"outline\",\n                                                        className: \"ml-2 text-gray-500\",\n                                                        children: \"Waiting for QR\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1426,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1423,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1422,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-64 flex items-center justify-center bg-gray-100 rounded border-2 border-dashed border-gray-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-16 w-16 mx-auto mb-4 opacity-30\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1434,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium\",\n                                                            children: \"Face Verification Locked\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1435,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: \"Complete Step 1 (QR Scan) first\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1436,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-4 p-3 bg-yellow-50 rounded-lg border border-yellow-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-yellow-700\",\n                                                                children: \"\\uD83D\\uDD12 Face verification will activate after successful QR code validation\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1438,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1437,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1433,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1432,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1431,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 1421,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"bg-green-50 border-green-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-4 sm:p-6 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl sm:text-4xl font-bold text-green-600 mb-2\",\n                                                        children: todayEntries.filter((e)=>e.status === 'entry').length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1453,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm sm:text-base font-medium text-green-700\",\n                                                        children: \"Total Entries\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1456,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-green-500 mt-1\",\n                                                        children: \"Today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1459,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1452,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1451,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"bg-red-50 border-red-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-4 sm:p-6 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl sm:text-4xl font-bold text-red-600 mb-2\",\n                                                        children: todayEntries.filter((e)=>e.status === 'exit').length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1468,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm sm:text-base font-medium text-red-700\",\n                                                        children: \"Total Exits\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1471,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-red-500 mt-1\",\n                                                        children: \"Today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1474,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1467,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1466,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"bg-blue-50 border-blue-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-4 sm:p-6 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl sm:text-4xl font-bold text-blue-600 mb-2\",\n                                                        children: todayEntries.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1483,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm sm:text-base font-medium text-blue-700\",\n                                                        children: \"Total Activity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1486,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-blue-500 mt-1\",\n                                                        children: \"Today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1489,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1482,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1481,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 1449,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: loadTodayHistory,\n                                        variant: \"outline\",\n                                        className: \"w-full sm:w-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1499,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"View Today's History\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1498,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 1497,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 1180,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 970,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"Database Connection & System Integration\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 1509,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 1508,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-purple-700 mb-2\",\n                                                children: \"Same Database Connection:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1514,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                className: \"list-decimal list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Station connects to same database as Admin Panel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1516,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Students added in Admin are instantly available here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1517,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Entry logs are shared across both systems\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1518,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Real-time data synchronization\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1519,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Fallback to local storage if database unavailable\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1520,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Automatic data sync when connection restored\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1521,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1515,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1513,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-green-700 mb-2\",\n                                                children: \"Professional Station Features:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1525,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Dedicated website for security staff\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1527,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"No login required - direct access\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1528,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Real-time QR code scanning\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1529,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Live face verification system\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1530,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Automatic entry/exit logging\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1531,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Professional security interface\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1532,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1526,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1524,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 1512,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 1511,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 1507,\n                    columnNumber: 9\n                }, this),\n                showTodayHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: \"Today's Entry/Exit History\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1544,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>setShowTodayHistory(false),\n                                        variant: \"outline\",\n                                        children: \"✕ Close\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1545,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 1543,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-green-600\",\n                                                        children: todayEntries.filter((e)=>e.status === 'entry').length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1553,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-green-700\",\n                                                        children: \"Total Entries\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1554,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1552,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-red-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-red-600\",\n                                                        children: todayEntries.filter((e)=>e.status === 'exit').length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1557,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-700\",\n                                                        children: \"Total Exits\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1558,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1556,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-blue-600\",\n                                                        children: todayEntries.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1561,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-blue-700\",\n                                                        children: \"Total Activity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1562,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1560,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1551,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 max-h-96 overflow-y-auto\",\n                                        children: todayEntries.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"h-16 w-16 mx-auto text-gray-300 mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1569,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-lg\",\n                                                    children: \"No activity recorded today\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1570,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Entry/exit records will appear here\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1571,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1568,\n                                            columnNumber: 21\n                                        }, this) : todayEntries.map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border border-gray-200 rounded-lg p-4 bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-2xl\",\n                                                                            children: entry.status === 'entry' ? '🟢' : '🔴'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1579,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-semibold text-lg\",\n                                                                                    children: entry.student_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1583,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: [\n                                                                                        \"App: \",\n                                                                                        entry.application_number\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1584,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1582,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1578,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-gray-500\",\n                                                                                    children: \"Entry Time\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1590,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: formatDateTime(entry.entryTime)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1591,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1589,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        entry.exitTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-gray-500\",\n                                                                                    children: \"Exit Time\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1595,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: formatDateTime(entry.exitTime)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1596,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1594,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1588,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 flex items-center gap-4 text-xs text-gray-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1603,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"QR Verified\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1602,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1607,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"Face Verified\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1606,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1611,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                entry.status === 'entry' ? 'Entry' : 'Exit',\n                                                                                \" Recorded\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1610,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1601,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1577,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: entry.status === 'entry' ? 'default' : 'secondary',\n                                                                    className: \"mb-2\",\n                                                                    children: entry.status === 'entry' ? 'ENTRY' : 'EXIT'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1618,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: entry.verified ? '✅ Verified' : '⚠️ Pending'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1621,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1617,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1576,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, entry.id, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1575,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1566,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center text-sm text-gray-500 border-t pt-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"History resets daily at midnight • Real-time updates\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1632,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1631,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 1550,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                        lineNumber: 1542,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 1541,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n            lineNumber: 855,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n        lineNumber: 854,\n        columnNumber: 5\n    }, this);\n}\n_s(IDCardStation, \"oMokzf+ohBYkXynFcHVs2J4s1XE=\");\n_c = IDCardStation;\nvar _c;\n$RefreshReg$(_c, \"IDCardStation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});