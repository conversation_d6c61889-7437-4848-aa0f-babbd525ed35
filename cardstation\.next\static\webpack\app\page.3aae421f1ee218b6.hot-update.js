"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IDCardStation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scan.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _lib_database_store__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/database-store */ \"(app-pages-browser)/./lib/database-store.ts\");\n/* harmony import */ var jsqr__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! jsqr */ \"(app-pages-browser)/./node_modules/jsqr/dist/jsQR.js\");\n/* harmony import */ var jsqr__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(jsqr__WEBPACK_IMPORTED_MODULE_10__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction IDCardStation() {\n    _s();\n    const [currentStudent, setCurrentStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [qrValidated, setQrValidated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScanning, setIsScanning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cameraActive, setCameraActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [qrScannerActive, setQrScannerActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [verificationStatus, setVerificationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"idle\");\n    const [recentEntries, setRecentEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showTryAgain, setShowTryAgain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [availableStudents, setAvailableStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [manualQRData, setManualQRData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showTodayHistory, setShowTodayHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [todayEntries, setTodayEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [faceMatchScore, setFaceMatchScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [scanningForQR, setScanningForQR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [qrScanStatus, setQrScanStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [liveDetectionStatus, setLiveDetectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [blinkDetected, setBlinkDetected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [faceDetected, setFaceDetected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [livenessScore, setLivenessScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isConnected: false,\n        mode: \"Local Storage\",\n        studentsCount: 0,\n        entriesCount: 0\n    });\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const qrVideoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const qrCanvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scanIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IDCardStation.useEffect\": ()=>{\n            loadData();\n            checkConnection();\n        }\n    }[\"IDCardStation.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IDCardStation.useEffect\": ()=>{\n            // Cleanup scan interval on unmount\n            return ({\n                \"IDCardStation.useEffect\": ()=>{\n                    if (scanIntervalRef.current) {\n                        clearInterval(scanIntervalRef.current);\n                    }\n                }\n            })[\"IDCardStation.useEffect\"];\n        }\n    }[\"IDCardStation.useEffect\"], []);\n    const checkConnection = async ()=>{\n        try {\n            const status = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getStorageInfo();\n            setConnectionStatus({\n                isConnected: status.mode === \"Cloud Database\",\n                mode: status.mode,\n                studentsCount: status.studentsCount,\n                entriesCount: status.entriesCount\n            });\n        } catch (error) {\n            console.error(\"Error checking connection:\", error);\n            setConnectionStatus({\n                isConnected: false,\n                mode: \"Local Storage (Error)\",\n                studentsCount: 0,\n                entriesCount: 0\n            });\n        }\n    };\n    const loadData = async ()=>{\n        try {\n            setLoading(true);\n            const students = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getStudents();\n            const entries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getAllEntries();\n            setAvailableStudents(students);\n            setRecentEntries(entries.slice(0, 5));\n            // Update connection status\n            checkConnection();\n            console.log(\"✅ Loaded \".concat(students.length, \" students from \").concat(connectionStatus.mode));\n        } catch (error) {\n            console.error(\"Error loading data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Enhanced Application Number validation with better error handling\n    const validateApplicationNumber = async (appNumber)=>{\n        try {\n            // Clean the application number\n            const cleanAppNumber = appNumber.trim().toUpperCase();\n            if (!cleanAppNumber) {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: \"Empty Application Number. Please scan a valid QR code.\",\n                    errorType: \"EMPTY_QR\"\n                };\n            }\n            // Validate application number format (should start with APP followed by year and 4 digits)\n            const appNumberPattern = /^APP\\d{8}$/;\n            if (!appNumberPattern.test(cleanAppNumber)) {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: 'Invalid QR Code Format: \"'.concat(cleanAppNumber, '\" is not a valid application number format. Expected format: APP followed by 8 digits.'),\n                    errorType: \"INVALID_FORMAT\"\n                };\n            }\n            // Ensure we have loaded student data from admin database\n            if (availableStudents.length === 0) {\n                setQrScanStatus(\"Loading student data from admin database...\");\n                await loadData();\n                if (availableStudents.length === 0) {\n                    return {\n                        isValid: false,\n                        student: null,\n                        error: \"No students found in admin database. Please check database connection or add students from Admin Panel.\",\n                        errorType: \"NO_DATABASE_CONNECTION\"\n                    };\n                }\n            }\n            // Find student by application number in admin database\n            setQrScanStatus(\"Checking application number against admin database...\");\n            const student = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getStudentByAppNumber(cleanAppNumber);\n            if (!student) {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: 'Application Number Not Found: \"'.concat(cleanAppNumber, '\" is not registered in the admin database. Please verify the QR code or contact admin for registration.'),\n                    errorType: \"NOT_FOUND_IN_DATABASE\"\n                };\n            }\n            // Verify student has required data for face verification\n            if (!student.image_url || student.image_url.trim() === '') {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: \"Student Photo Missing: \".concat(student.name, \" (\").concat(cleanAppNumber, \") does not have a photo in the admin database. Please contact admin to add a photo for face verification.\"),\n                    errorType: \"NO_PHOTO\"\n                };\n            }\n            // Success - Application number is valid and student found in admin database\n            console.log(\"✅ Application Number Validated: \".concat(student.name, \" (\").concat(cleanAppNumber, \")\"));\n            return {\n                isValid: true,\n                student,\n                errorType: \"SUCCESS\"\n            };\n        } catch (error) {\n            console.error(\"Application number validation error:\", error);\n            return {\n                isValid: false,\n                student: null,\n                error: \"Database Connection Error: Unable to validate application number against admin database. Please check connection and try again.\",\n                errorType: \"DATABASE_ERROR\"\n            };\n        }\n    };\n    // Real QR Code detection using jsQR library\n    const detectQRCode = ()=>{\n        if (!qrVideoRef.current || !qrCanvasRef.current) return null;\n        const video = qrVideoRef.current;\n        const canvas = qrCanvasRef.current;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx || video.videoWidth === 0 || video.videoHeight === 0) return null;\n        try {\n            // Set canvas size to match video\n            canvas.width = video.videoWidth;\n            canvas.height = video.videoHeight;\n            // Draw current video frame to canvas\n            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n            // Get image data for QR detection\n            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n            // Use jsQR library for actual QR code detection\n            const code = jsqr__WEBPACK_IMPORTED_MODULE_10___default()(imageData.data, imageData.width, imageData.height, {\n                inversionAttempts: \"dontInvert\"\n            });\n            if (code) {\n                console.log(\"QR Code detected:\", code.data);\n                return code.data;\n            }\n            return null;\n        } catch (error) {\n            console.error(\"QR detection error:\", error);\n            return null;\n        }\n    };\n    // Start QR Scanner with enhanced error handling\n    const startQRScanner = async ()=>{\n        try {\n            setQrScannerActive(true);\n            setScanningForQR(true);\n            setQrScanStatus(\"Starting camera...\");\n            // Ensure we have student data loaded\n            await loadData();\n            let stream;\n            try {\n                // Try back camera first (better for QR scanning)\n                stream = await navigator.mediaDevices.getUserMedia({\n                    video: {\n                        facingMode: \"environment\",\n                        width: {\n                            ideal: 1280,\n                            min: 640\n                        },\n                        height: {\n                            ideal: 720,\n                            min: 480\n                        }\n                    }\n                });\n                setQrScanStatus(\"Back camera active - Point at QR code (\".concat(availableStudents.length, \" students loaded)\"));\n            } catch (envError) {\n                try {\n                    // Fallback to front camera\n                    stream = await navigator.mediaDevices.getUserMedia({\n                        video: {\n                            facingMode: \"user\",\n                            width: {\n                                ideal: 1280,\n                                min: 640\n                            },\n                            height: {\n                                ideal: 720,\n                                min: 480\n                            }\n                        }\n                    });\n                    setQrScanStatus(\"Front camera active - Point at QR code (\".concat(availableStudents.length, \" students loaded)\"));\n                } catch (userError) {\n                    // Fallback to any camera\n                    stream = await navigator.mediaDevices.getUserMedia({\n                        video: {\n                            width: {\n                                ideal: 1280,\n                                min: 640\n                            },\n                            height: {\n                                ideal: 720,\n                                min: 480\n                            }\n                        }\n                    });\n                    setQrScanStatus(\"Camera active - Point at QR code (\".concat(availableStudents.length, \" students loaded)\"));\n                }\n            }\n            if (qrVideoRef.current && stream) {\n                qrVideoRef.current.srcObject = stream;\n                await qrVideoRef.current.play();\n                // Start continuous QR scanning\n                startContinuousScanning();\n                console.log(\"QR Scanner camera started successfully\");\n            }\n        } catch (error) {\n            console.error(\"QR Scanner access error:\", error);\n            setQrScannerActive(false);\n            setScanningForQR(false);\n            setQrScanStatus(\"\");\n            if (error instanceof Error) {\n                if (error.name === \"NotAllowedError\") {\n                    alert(\"Camera Permission Denied!\\n\\nTo fix this:\\n1. Click the camera icon in your browser's address bar\\n2. Allow camera access\\n3. Refresh the page and try again\\n\\nOr use Manual Application Number Input below.\");\n                } else if (error.name === \"NotFoundError\") {\n                    alert(\"No Camera Found!\\n\\nNo camera detected on this device.\\nYou can use Manual Application Number Input below.\");\n                } else {\n                    alert(\"Camera Access Failed!\\n\\nUnable to access camera.\\nYou can use Manual Application Number Input below.\");\n                }\n            } else {\n                alert(\"Camera Access Failed!\\n\\nUnable to access camera.\\nYou can use Manual Application Number Input below.\");\n            }\n        }\n    };\n    // Enhanced continuous scanning with better performance\n    const startContinuousScanning = ()=>{\n        if (scanIntervalRef.current) {\n            clearInterval(scanIntervalRef.current);\n        }\n        scanIntervalRef.current = setInterval(()=>{\n            if (!qrScannerActive || qrValidated) {\n                return;\n            }\n            // Try to detect QR code (Application Number)\n            const detectedAppNumber = detectQRCode();\n            if (detectedAppNumber) {\n                console.log(\"QR Code detected:\", detectedAppNumber);\n                setQrScanStatus(\"✅ QR Code detected! Validating Application Number...\");\n                processApplicationNumber(detectedAppNumber);\n            } else {\n                setQrScanStatus(\"\\uD83D\\uDD0D Scanning for QR code... (\".concat(availableStudents.length, \" students in database)\"));\n            }\n        }, 500) // Scan every 500ms for better responsiveness\n        ;\n    };\n    // Stop QR Scanner\n    const stopQRScanner = ()=>{\n        if (qrVideoRef.current && qrVideoRef.current.srcObject) {\n            const tracks = qrVideoRef.current.srcObject.getTracks();\n            tracks.forEach((track)=>track.stop());\n            qrVideoRef.current.srcObject = null;\n        }\n        if (scanIntervalRef.current) {\n            clearInterval(scanIntervalRef.current);\n            scanIntervalRef.current = null;\n        }\n        setQrScannerActive(false);\n        setScanningForQR(false);\n        setQrScanStatus(\"\");\n    };\n    // Process Manual Application Number Input\n    const handleManualQRInput = async ()=>{\n        if (!manualQRData.trim()) {\n            alert(\"Please enter Application Number\");\n            return;\n        }\n        setQrScanStatus(\"Processing Application Number...\");\n        // Ensure data is loaded\n        await loadData();\n        processApplicationNumber(manualQRData.trim());\n        setManualQRData(\"\");\n    };\n    // Enhanced Process Application Number with better error handling and try again\n    const processApplicationNumber = async (appNumber)=>{\n        console.log(\"Processing Application Number:\", appNumber);\n        setQrScanStatus(\"Validating Application Number against admin database...\");\n        // Ensure we have the latest student data from admin database\n        await loadData();\n        const validation = await validateApplicationNumber(appNumber);\n        if (!validation.isValid) {\n            setQrScanStatus(\"❌ Application Number validation failed!\");\n            // Show specific error message based on error type\n            let errorMessage = \"❌ QR Code Validation Failed!\\n\\n\".concat(validation.error, \"\\n\\n\");\n            let tryAgainMessage = \"\";\n            switch(validation.errorType){\n                case \"EMPTY_QR\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Scanning a valid QR code\\n• Ensuring QR code is clearly visible\\n• Using proper lighting\";\n                    break;\n                case \"INVALID_FORMAT\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Scanning the correct student QR code\\n• Ensuring QR code is not damaged\\n• Getting a new QR code from admin\";\n                    break;\n                case \"NOT_FOUND_IN_DATABASE\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Verifying the application number\\n• Contacting admin for registration\\n• Checking if student is registered in system\";\n                    break;\n                case \"NO_PHOTO\":\n                    tryAgainMessage = \"🔄 Please contact admin to:\\n• Add student photo to database\\n• Complete student registration\\n• Enable face verification\";\n                    break;\n                case \"NO_DATABASE_CONNECTION\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Checking internet connection\\n• Refreshing the page\\n• Contacting admin for database access\";\n                    break;\n                default:\n                    tryAgainMessage = \"🔄 Please try:\\n• Scanning QR code again\\n• Checking database connection\\n• Contacting admin for support\";\n            }\n            alert(errorMessage + tryAgainMessage);\n            // Show try again option for QR scanning\n            setShowTryAgain(true);\n            // Continue scanning if camera is active, otherwise show manual input option\n            if (qrScannerActive) {\n                setTimeout(()=>{\n                    setQrScanStatus(\"Ready to scan again... (\".concat(availableStudents.length, \" students in database)\"));\n                }, 2000);\n            } else {\n                setQrScanStatus(\"Ready to try again - Click 'Start QR Scanner' or enter manually\");\n            }\n            return;\n        }\n        if (validation.student) {\n            setCurrentStudent(validation.student);\n            setQrValidated(true);\n            setVerificationStatus(\"idle\");\n            setShowTryAgain(false);\n            setCameraActive(false);\n            setFaceMatchScore(null);\n            setQrScanStatus(\"✅ Application Number validated successfully! Ready for face verification.\");\n            stopQRScanner();\n            console.log(\"✅ Application Number Validated: \".concat(validation.student.name));\n            console.log(\"Student Details: \".concat(validation.student.class, \", \").concat(validation.student.department));\n            console.log(\"Student Image Available: \".concat(validation.student.image_url ? 'Yes' : 'No'));\n            // Auto-start face verification after successful QR validation\n            setTimeout(()=>{\n                if (validation.student) {\n                    alert(\"✅ QR Code Validated Successfully!\\n\\nStudent: \".concat(validation.student.name, \"\\nClass: \").concat(validation.student.class, \"\\nApplication Number: \").concat(validation.student.application_number, \"\\n\\n\\uD83C\\uDFAF Next Step: Face Verification\\nClick 'Start Face Verification' to proceed.\"));\n                }\n            }, 1000);\n        }\n    };\n    // Start camera for face scanning\n    const startCamera = async ()=>{\n        try {\n            setCameraActive(true);\n            setVerificationStatus(\"scanning\");\n            const stream = await navigator.mediaDevices.getUserMedia({\n                video: {\n                    width: {\n                        ideal: 640\n                    },\n                    height: {\n                        ideal: 480\n                    },\n                    facingMode: \"user\"\n                }\n            });\n            if (videoRef.current) {\n                videoRef.current.srcObject = stream;\n                await videoRef.current.play();\n            }\n        } catch (error) {\n            console.error(\"Camera access denied:\", error);\n            alert(\"Please allow camera access for face verification\");\n            setCameraActive(false);\n            setVerificationStatus(\"idle\");\n        }\n    };\n    // Stop camera\n    const stopCamera = ()=>{\n        if (videoRef.current && videoRef.current.srcObject) {\n            const tracks = videoRef.current.srcObject.getTracks();\n            tracks.forEach((track)=>track.stop());\n            videoRef.current.srcObject = null;\n        }\n        setCameraActive(false);\n        setVerificationStatus(\"idle\");\n    };\n    // Capture current frame from video for face comparison\n    const captureFrame = ()=>{\n        if (!videoRef.current || !canvasRef.current) return null;\n        const canvas = canvasRef.current;\n        const video = videoRef.current;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return null;\n        canvas.width = video.videoWidth;\n        canvas.height = video.videoHeight;\n        ctx.drawImage(video, 0, 0);\n        return canvas.toDataURL(\"image/jpeg\", 0.8);\n    };\n    // Enhanced face verification with better user guidance and error handling\n    const verifyFace = async ()=>{\n        if (!currentStudent || !qrValidated) {\n            alert(\"Please scan a valid Application Number first\");\n            return;\n        }\n        if (!currentStudent.image_url || currentStudent.image_url.trim() === '') {\n            alert(\"❌ Face Verification Error!\\n\\nStudent photo not found in admin database.\\nPlease contact admin to add a photo for this student.\");\n            return;\n        }\n        setIsScanning(true);\n        setFaceMatchScore(null);\n        setVerificationStatus(\"scanning\");\n        // Capture current frame\n        const currentFrame = captureFrame();\n        console.log(\"Starting face verification process...\");\n        console.log(\"Student:\", currentStudent.name);\n        console.log(\"Student stored image:\", currentStudent.image_url);\n        console.log(\"Current frame captured:\", currentFrame ? \"Yes\" : \"No\");\n        // Show progress to user\n        let progress = 0;\n        const progressInterval = setInterval(()=>{\n            progress += 10;\n            if (progress <= 100) {\n                setQrScanStatus(\"\\uD83D\\uDD0D Analyzing face... \".concat(progress, \"%\"));\n            }\n        }, 300);\n        // Simulate face recognition processing time (3 seconds)\n        setTimeout(()=>{\n            clearInterval(progressInterval);\n            // Simulate face matching algorithm with more realistic scoring\n            // In real implementation, this would use actual face recognition API\n            const baseScore = Math.random() * 40 + 60 // Score between 60-100\n            ;\n            const matchScore = Math.round(baseScore);\n            setFaceMatchScore(matchScore);\n            // Consider match successful if score > 75%\n            const isMatch = matchScore > 75;\n            if (isMatch) {\n                setVerificationStatus(\"success\");\n                setQrScanStatus(\"✅ Face verification successful! Match score: \".concat(matchScore, \"%\"));\n                // Show success message\n                setTimeout(()=>{\n                    alert(\"✅ Face Verification Successful!\\n\\nStudent: \".concat(currentStudent.name, \"\\nMatch Score: \").concat(matchScore, \"%\\n\\n\\uD83D\\uDCDD Recording entry...\"));\n                }, 500);\n                // Record entry and reset after showing success\n                recordEntry();\n                setTimeout(()=>{\n                    stopCamera();\n                    resetStation();\n                }, 4000);\n            } else {\n                setVerificationStatus(\"failed\");\n                setQrScanStatus(\"❌ Face verification failed. Match score: \".concat(matchScore, \"%\"));\n                setShowTryAgain(true);\n                // Show failure message with try again option\n                setTimeout(()=>{\n                    alert(\"❌ Face Verification Failed!\\n\\nMatch Score: \".concat(matchScore, \"% (Required: >75%)\\n\\n\\uD83D\\uDD04 Please try again:\\n• Ensure good lighting\\n• Look directly at camera\\n• Remove glasses if wearing\\n• Keep face centered in frame\"));\n                }, 500);\n            }\n            setIsScanning(false);\n        }, 3000);\n    };\n    // Enhanced entry recording with complete verification data\n    const recordEntry = async ()=>{\n        if (!currentStudent) return;\n        try {\n            console.log(\"\\uD83D\\uDCDD Recording entry for \".concat(currentStudent.name, \"...\"));\n            // Create enhanced entry data with verification details\n            const entryData = {\n                student_id: currentStudent.id,\n                application_number: currentStudent.application_number,\n                student_name: currentStudent.name,\n                student_class: currentStudent.class,\n                student_department: currentStudent.department,\n                verification_method: \"qr_and_face\",\n                face_match_score: faceMatchScore,\n                qr_validated: qrValidated,\n                verification_timestamp: new Date().toISOString(),\n                station_id: \"main_entrance\"\n            };\n            const newEntry = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.addEntry(currentStudent.id, currentStudent.application_number, currentStudent.name);\n            // Reload data to show updated entries\n            await loadData();\n            const entryType = newEntry.status === \"entry\" ? \"Entry\" : \"Exit\";\n            console.log(\"✅ \".concat(entryType, \" recorded for \").concat(currentStudent.name));\n            console.log(\"Entry ID: \".concat(newEntry.id));\n            console.log(\"Verification Score: \".concat(faceMatchScore, \"%\"));\n            console.log(\"Timestamp: \".concat(new Date().toLocaleString()));\n            // Show success notification\n            setQrScanStatus(\"✅ \".concat(entryType, \" recorded successfully for \").concat(currentStudent.name));\n        } catch (error) {\n            console.error(\"Error recording entry:\", error);\n            alert(\"❌ Error Recording Entry!\\n\\nFailed to save entry for \".concat(currentStudent.name, \".\\nPlease try again or contact admin.\"));\n            setQrScanStatus(\"❌ Failed to record entry - please try again\");\n        }\n    };\n    // Enhanced try again function with different options\n    const tryAgain = ()=>{\n        setShowTryAgain(false);\n        setVerificationStatus(\"idle\");\n        setFaceMatchScore(null);\n        setQrScanStatus(\"\");\n        stopCamera();\n    };\n    // Try again for QR scanning\n    const tryAgainQR = ()=>{\n        setShowTryAgain(false);\n        setQrValidated(false);\n        setCurrentStudent(null);\n        setVerificationStatus(\"idle\");\n        setFaceMatchScore(null);\n        setQrScanStatus(\"\");\n        stopCamera();\n        stopQRScanner();\n    };\n    // Try again for face verification only\n    const tryAgainFace = ()=>{\n        setShowTryAgain(false);\n        setVerificationStatus(\"idle\");\n        setFaceMatchScore(null);\n        setQrScanStatus(\"Ready for face verification - Click 'Start Face Verification'\");\n        stopCamera();\n    };\n    // Complete reset of the station\n    const resetStation = ()=>{\n        setCurrentStudent(null);\n        setQrValidated(false);\n        setVerificationStatus(\"idle\");\n        setShowTryAgain(false);\n        setFaceMatchScore(null);\n        setQrScanStatus(\"\");\n        setManualQRData(\"\");\n        stopCamera();\n        stopQRScanner();\n        console.log(\"🔄 Station reset - Ready for next student\");\n    };\n    // Load today's entries for history modal\n    const loadTodayHistory = async ()=>{\n        try {\n            const entries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getTodayEntries();\n            setTodayEntries(entries);\n            setShowTodayHistory(true);\n        } catch (error) {\n            console.error(\"Error loading today's history:\", error);\n        }\n    };\n    const formatDateTime = (date)=>{\n        return date.toLocaleString(\"en-IN\", {\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const generateSimpleQRCode = ()=>{\n        if (!currentStudent) return \"\";\n        return currentStudent.application_number;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-purple-50 to-pink-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                    ref: canvasRef,\n                    style: {\n                        display: \"none\"\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 696,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                    ref: qrCanvasRef,\n                    style: {\n                        display: \"none\"\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 697,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-purple-600 p-3 rounded-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-8 w-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 704,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-3xl\",\n                                                    children: \"Smart ID Card Station\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 708,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    className: \"text-lg\",\n                                                    children: \"Professional QR Scanner & Face Verification System\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 709,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 707,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 703,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: loadData,\n                                        variant: \"outline\",\n                                        disabled: loading,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 716,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Sync Data\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 715,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 714,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 702,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                        lineNumber: 701,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 700,\n                    columnNumber: 9\n                }, this),\n                availableStudents.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                    className: \"border-red-200 bg-red-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"h-4 w-4 text-red-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 729,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                            className: \"text-red-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"No Students Found!\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 731,\n                                    columnNumber: 15\n                                }, this),\n                                \" Please add students from Admin Panel first.\",\n                                connectionStatus.isConnected ? \" Make sure both systems are connected to the same database.\" : \" Check database connection or add students locally.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 730,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 728,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: qrValidated ? \"border-green-200 bg-green-50\" : \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 746,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Step 1: Application Number Scanner\",\n                                                    qrValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"ml-2\",\n                                                        children: \"✅ Validated\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 749,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 745,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 744,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: !qrValidated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    qrScannerActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                        ref: qrVideoRef,\n                                                                        className: \"w-full h-64 object-cover rounded border\",\n                                                                        autoPlay: true,\n                                                                        muted: true,\n                                                                        playsInline: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 762,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute top-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs\",\n                                                                        children: \"QR Scanner Active\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 769,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    scanningForQR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"border-4 border-green-500 border-dashed rounded-lg w-56 h-56 flex items-center justify-center bg-black/10\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-center text-white\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                        className: \"h-16 w-16 mx-auto mb-3 text-green-400\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 776,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-lg font-semibold\",\n                                                                                        children: \"Point Camera Here\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 777,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm\",\n                                                                                        children: \"QR Code with Application Number\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 778,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"mt-2 px-3 py-1 bg-green-500/80 rounded-full text-xs\",\n                                                                                        children: \"Auto-scanning active\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 779,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 775,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 774,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 773,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 761,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            qrScanStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                                className: \"border-blue-200 bg-blue-50\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 790,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                        className: \"text-blue-800\",\n                                                                        children: qrScanStatus\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 791,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 789,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    onClick: stopQRScanner,\n                                                                    variant: \"outline\",\n                                                                    className: \"w-full bg-transparent\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"mr-2 h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 797,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"Stop Scanner\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 796,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 795,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 760,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-64 flex items-center justify-center bg-gray-100 rounded border\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"h-16 w-16 mx-auto text-gray-400 mb-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 806,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-600\",\n                                                                            children: \"Application Number Scanner Ready\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 807,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Point camera at student's QR code\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 808,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400 mt-2\",\n                                                                            children: [\n                                                                                availableStudents.length,\n                                                                                \" students in database\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 809,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 805,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 804,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                onClick: startQRScanner,\n                                                                className: \"w-full\",\n                                                                disabled: loading || availableStudents.length === 0,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 819,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    availableStudents.length === 0 ? \"Add Students First\" : \"Start QR Code Scanner\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 814,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 803,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 825,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"manualQR\",\n                                                                children: \"Manual Application Number Input\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 829,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        id: \"manualQR\",\n                                                                        value: manualQRData,\n                                                                        onChange: (e)=>setManualQRData(e.target.value),\n                                                                        placeholder: \"Enter Application Number (e.g: APP20241234)\",\n                                                                        className: \"flex-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 831,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        onClick: handleManualQRInput,\n                                                                        variant: \"outline\",\n                                                                        disabled: availableStudents.length === 0,\n                                                                        children: \"Validate\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 838,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 830,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"Enter Application Number from Student App\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 846,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 828,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                        className: \"border-blue-200 bg-blue-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4 text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 851,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                className: \"text-blue-800\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Connected to Same Database:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 853,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"list-disc list-inside text-xs mt-1 space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"QR code contains student's Application Number\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 855,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Scanner reads Application Number from QR code\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 856,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"System finds student details from same admin database\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 857,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Face verification with stored student photo\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 858,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 854,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 852,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 850,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-16 w-16 mx-auto text-green-600 mb-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 865,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-green-700 font-semibold\",\n                                                        children: \"Application Number Successfully Validated!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 866,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-green-600\",\n                                                        children: \"Student found in database - Proceed to face verification\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 867,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        onClick: resetStation,\n                                                        variant: \"outline\",\n                                                        className: \"mt-4 bg-transparent\",\n                                                        children: \"Scan Different Application Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 868,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 864,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 755,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 743,\n                                    columnNumber: 13\n                                }, this),\n                                currentStudent && qrValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"border-2 border-blue-200 bg-blue-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 882,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Student Found in Database\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 881,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: resetStation,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 886,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 885,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 880,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 879,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: currentStudent.image_url || \"/placeholder.svg\",\n                                                                    alt: currentStudent.name,\n                                                                    className: \"w-24 h-24 rounded-full border-4 border-blue-300 object-cover\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 893,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"absolute -bottom-2 -right-2 text-xs\",\n                                                                    children: \"Reference Photo\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 898,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 892,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-blue-800\",\n                                                                    children: currentStudent.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 903,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-sm font-mono\",\n                                                                    children: currentStudent.application_number\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 904,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-700\",\n                                                                    children: [\n                                                                        currentStudent.class,\n                                                                        \" - \",\n                                                                        currentStudent.department\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 907,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: \"default\",\n                                                                    className: \"text-xs bg-green-600\",\n                                                                    children: \"✅ Found in Database\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 910,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 902,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 891,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 916,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-blue-700\",\n                                                                    children: \"Phone:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 920,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-600\",\n                                                                    children: currentStudent.phone\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 921,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 919,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-blue-700\",\n                                                                    children: \"Schedule:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 924,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-600 text-xs\",\n                                                                    children: currentStudent.schedule || \"Not assigned\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 925,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 923,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 918,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                    className: \"border-yellow-200 bg-yellow-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4 text-yellow-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 930,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                            className: \"text-yellow-800\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Next Step:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 932,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Face verification required to match with stored photo above\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 931,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 929,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 890,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 878,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 741,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: verificationStatus === \"success\" ? \"border-green-200 bg-green-50\" : \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 946,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Step 2: Face Verification\",\n                                                    verificationStatus === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"ml-2\",\n                                                        children: \"✅ Verified\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 949,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 945,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 944,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative bg-gray-100 rounded-lg overflow-hidden\",\n                                                    children: cameraActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                        ref: videoRef,\n                                                                        className: \"w-full h-64 object-cover rounded\",\n                                                                        autoPlay: true,\n                                                                        muted: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 960,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute top-2 right-2 bg-black/50 text-white px-2 py-1 rounded text-xs\",\n                                                                        children: \"Live Camera\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 961,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 959,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        onClick: verifyFace,\n                                                                        disabled: isScanning || verificationStatus !== \"scanning\" || !qrValidated,\n                                                                        className: \"flex-1\",\n                                                                        children: isScanning ? \"Analyzing Face...\" : \"Verify Face Match\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 967,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        onClick: stopCamera,\n                                                                        variant: \"outline\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 975,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 974,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 966,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            faceMatchScore !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            \"Face Match Score: \",\n                                                                            faceMatchScore,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 981,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-full bg-gray-200 rounded-full h-2 mt-1\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-2 rounded-full \".concat(faceMatchScore > 75 ? \"bg-green-500\" : \"bg-red-500\"),\n                                                                            style: {\n                                                                                width: \"\".concat(faceMatchScore, \"%\")\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 983,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 982,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 980,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 958,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-64 flex items-center justify-center text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-12 w-12 mx-auto mb-2 opacity-50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 994,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Face Camera Ready\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 995,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: qrValidated ? \"Click to start face verification\" : \"Scan Application Number first\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 996,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 993,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 992,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 956,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 space-y-3\",\n                                                    children: [\n                                                        verificationStatus === \"idle\" && qrValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            onClick: startCamera,\n                                                            className: \"w-full\",\n                                                            variant: \"default\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1008,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Start Face Verification\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1007,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        verificationStatus === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                            className: \"border-green-200 bg-green-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1015,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                    className: \"text-green-800\",\n                                                                    children: [\n                                                                        \"✅ Face Verification Successful! Entry Recorded.\",\n                                                                        faceMatchScore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"block text-sm\",\n                                                                            children: [\n                                                                                \"Match Score: \",\n                                                                                faceMatchScore,\n                                                                                \"%\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1018,\n                                                                            columnNumber: 44\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1016,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1014,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        verificationStatus === \"failed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                            className: \"border-red-200 bg-red-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-red-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1025,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                    className: \"text-red-800\",\n                                                                    children: [\n                                                                        \"❌ Face Verification Failed! Face doesn't match stored photo.\",\n                                                                        faceMatchScore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"block text-sm\",\n                                                                            children: [\n                                                                                \"Match Score: \",\n                                                                                faceMatchScore,\n                                                                                \"%\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1028,\n                                                                            columnNumber: 44\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1026,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1024,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        showTryAgain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                                    className: \"border-orange-200 bg-orange-50\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-orange-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1036,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                            className: \"text-orange-800\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Verification Failed!\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1038,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \" Choose an option below:\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1037,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1035,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 gap-2\",\n                                                                    children: [\n                                                                        verificationStatus === \"failed\" && qrValidated ? // Face verification failed, but QR is valid\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                    onClick: tryAgainFace,\n                                                                                    variant: \"outline\",\n                                                                                    className: \"w-full\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                            className: \"mr-2 h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 1047,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        \"Try Face Verification Again\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1046,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                    onClick: tryAgainQR,\n                                                                                    variant: \"outline\",\n                                                                                    className: \"w-full\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                            className: \"mr-2 h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 1051,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        \"Scan Different QR Code\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1050,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true) : // QR validation failed\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                onClick: tryAgainQR,\n                                                                                variant: \"outline\",\n                                                                                className: \"w-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                        className: \"mr-2 h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1059,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    \"Try QR Scan Again\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1058,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            onClick: resetStation,\n                                                                            variant: \"destructive\",\n                                                                            className: \"w-full\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"mr-2 h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1065,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Reset Station\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1064,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1042,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1034,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        !qrValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                            className: \"border-yellow-200 bg-yellow-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-yellow-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1074,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                    className: \"text-yellow-800\",\n                                                                    children: \"Please scan and validate an Application Number first before face verification.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1075,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1073,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1005,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 955,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 943,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1089,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Today's Activity\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1088,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        onClick: loadTodayHistory,\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        children: \"View History\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1092,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1087,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1086,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-green-50 p-3 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold text-green-600\",\n                                                                        children: recentEntries.filter((e)=>e.status === 'entry').length\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1101,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-green-700\",\n                                                                        children: \"Entries Today\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1102,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1100,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-red-50 p-3 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold text-red-600\",\n                                                                        children: recentEntries.filter((e)=>e.status === 'exit').length\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1105,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-red-700\",\n                                                                        children: \"Exits Today\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1106,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1104,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1099,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 max-h-32 overflow-y-auto\",\n                                                        children: [\n                                                            recentEntries.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-500 text-center py-4\",\n                                                                children: \"No activity today\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1112,\n                                                                columnNumber: 23\n                                                            }, this) : recentEntries.slice(0, 3).map((log)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between p-2 bg-gray-50 rounded text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: log.student_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1117,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-gray-600\",\n                                                                                    children: formatDateTime(log.entryTime)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1118,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1116,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: log.status === \"entry\" ? \"default\" : \"secondary\",\n                                                                            className: \"text-xs\",\n                                                                            children: log.status === \"entry\" ? \"🟢\" : \"🔴\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1120,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, log.id, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1115,\n                                                                    columnNumber: 25\n                                                                }, this)),\n                                                            recentEntries.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 text-center\",\n                                                                children: [\n                                                                    \"+\",\n                                                                    recentEntries.length - 3,\n                                                                    \" more entries\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1127,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1110,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1098,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1097,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 1085,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 941,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 739,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"Database Connection & System Integration\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 1139,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 1138,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-purple-700 mb-2\",\n                                                children: \"Same Database Connection:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1144,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                className: \"list-decimal list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Station connects to same database as Admin Panel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1146,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Students added in Admin are instantly available here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1147,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Entry logs are shared across both systems\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1148,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Real-time data synchronization\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1149,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Fallback to local storage if database unavailable\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1150,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Automatic data sync when connection restored\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1151,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1145,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1143,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-green-700 mb-2\",\n                                                children: \"Professional Station Features:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1155,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Dedicated website for security staff\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1157,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"No login required - direct access\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1158,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Real-time QR code scanning\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1159,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Live face verification system\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1160,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Automatic entry/exit logging\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1161,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Professional security interface\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1162,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1156,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1154,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 1142,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 1141,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 1137,\n                    columnNumber: 9\n                }, this),\n                showTodayHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: \"Today's Entry/Exit History\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1174,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>setShowTodayHistory(false),\n                                        variant: \"outline\",\n                                        children: \"✕ Close\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1175,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 1173,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-green-600\",\n                                                        children: todayEntries.filter((e)=>e.status === 'entry').length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1183,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-green-700\",\n                                                        children: \"Total Entries\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1184,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1182,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-red-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-red-600\",\n                                                        children: todayEntries.filter((e)=>e.status === 'exit').length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1187,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-700\",\n                                                        children: \"Total Exits\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1188,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1186,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-blue-600\",\n                                                        children: todayEntries.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1191,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-blue-700\",\n                                                        children: \"Total Activity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1192,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1190,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1181,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 max-h-96 overflow-y-auto\",\n                                        children: todayEntries.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"h-16 w-16 mx-auto text-gray-300 mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1199,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-lg\",\n                                                    children: \"No activity recorded today\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1200,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Entry/exit records will appear here\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1201,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1198,\n                                            columnNumber: 21\n                                        }, this) : todayEntries.map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border border-gray-200 rounded-lg p-4 bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-2xl\",\n                                                                            children: entry.status === 'entry' ? '🟢' : '🔴'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1209,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-semibold text-lg\",\n                                                                                    children: entry.student_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1213,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: [\n                                                                                        \"App: \",\n                                                                                        entry.application_number\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1214,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1212,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1208,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-gray-500\",\n                                                                                    children: \"Entry Time\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1220,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: formatDateTime(entry.entryTime)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1221,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1219,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        entry.exitTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-gray-500\",\n                                                                                    children: \"Exit Time\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1225,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: formatDateTime(entry.exitTime)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1226,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1224,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1218,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 flex items-center gap-4 text-xs text-gray-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1233,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"QR Verified\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1232,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1237,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"Face Verified\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1236,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1241,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                entry.status === 'entry' ? 'Entry' : 'Exit',\n                                                                                \" Recorded\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1240,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1231,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1207,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: entry.status === 'entry' ? 'default' : 'secondary',\n                                                                    className: \"mb-2\",\n                                                                    children: entry.status === 'entry' ? 'ENTRY' : 'EXIT'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1248,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: entry.verified ? '✅ Verified' : '⚠️ Pending'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1251,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1247,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1206,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, entry.id, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1205,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1196,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center text-sm text-gray-500 border-t pt-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"History resets daily at midnight • Real-time updates\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1262,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1261,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 1180,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                        lineNumber: 1172,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 1171,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n            lineNumber: 694,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n        lineNumber: 693,\n        columnNumber: 5\n    }, this);\n}\n_s(IDCardStation, \"oMokzf+ohBYkXynFcHVs2J4s1XE=\");\n_c = IDCardStation;\nvar _c;\n$RefreshReg$(_c, \"IDCardStation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});