/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/students/route";
exports.ids = ["app/api/students/route"];
exports.modules = {

/***/ "(rsc)/./app/api/students/route.ts":
/*!***********************************!*\
  !*** ./app/api/students/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./lib/mongodb.ts\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nasync function GET(req) {\n    try {\n        const url = new URL(req.url);\n        const appNumber = url.searchParams.get(\"application_number\");\n        const phone = url.searchParams.get(\"phone\");\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        const db = client.db(\"idcard\");\n        const students = db.collection(\"students\");\n        const query = {};\n        if (appNumber) query.application_number = appNumber;\n        if (phone) query.phone = phone;\n        const results = await students.find(query).sort({\n            createdAt: -1\n        }).toArray();\n        const data = results.map((s)=>({\n                ...s,\n                id: s._id.toString()\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch students\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(req) {\n    try {\n        const body = await req.json();\n        console.log(\"Received body:\", body);\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        const db = client.db(\"idcard\");\n        const students = db.collection(\"students\");\n        const newStudent = {\n            ...body,\n            createdAt: new Date(),\n            updatedAt: new Date()\n        };\n        const result = await students.insertOne(newStudent);\n        console.log(\"Insert result:\", result);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            ...newStudent,\n            id: result.insertedId.toString()\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"POST /api/students error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to add student\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PUT(req) {\n    try {\n        const { id, ...updates } = await req.json();\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        const db = client.db(\"smartidcard\");\n        const students = db.collection(\"students\");\n        const result = await students.findOneAndUpdate({\n            _id: new mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId(id)\n        }, {\n            $set: {\n                ...updates,\n                updatedAt: new Date()\n            }\n        }, {\n            returnDocument: \"after\"\n        });\n        if (!result || !result.value) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Student not found\"\n            }, {\n                status: 404\n            });\n        }\n        const updated = result.value;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            ...updated,\n            id: updated._id.toString()\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to update student\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(req) {\n    try {\n        const { id } = await req.json();\n        if (!id) return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Missing id\"\n        }, {\n            status: 400\n        });\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        const db = client.db(\"smartidcard\");\n        const students = db.collection(\"students\");\n        const result = await students.deleteOne({\n            _id: new mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId(id)\n        });\n        if (result.deletedCount === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Student not found\"\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to delete student\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL3N0dWRlbnRzL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQXVEO0FBQ2Q7QUFDUDtBQUUzQixlQUFlRyxJQUFJQyxHQUFnQjtJQUN4QyxJQUFJO1FBQ0YsTUFBTUMsTUFBTSxJQUFJQyxJQUFJRixJQUFJQyxHQUFHO1FBQzNCLE1BQU1FLFlBQVlGLElBQUlHLFlBQVksQ0FBQ0MsR0FBRyxDQUFDO1FBQ3ZDLE1BQU1DLFFBQVFMLElBQUlHLFlBQVksQ0FBQ0MsR0FBRyxDQUFDO1FBQ25DLE1BQU1FLFNBQVMsTUFBTVYsb0RBQWFBO1FBQ2xDLE1BQU1XLEtBQUtELE9BQU9DLEVBQUUsQ0FBQztRQUNyQixNQUFNQyxXQUFXRCxHQUFHRSxVQUFVLENBQUM7UUFFL0IsTUFBTUMsUUFBYSxDQUFDO1FBQ3BCLElBQUlSLFdBQVdRLE1BQU1DLGtCQUFrQixHQUFHVDtRQUMxQyxJQUFJRyxPQUFPSyxNQUFNTCxLQUFLLEdBQUdBO1FBRXpCLE1BQU1PLFVBQVUsTUFBTUosU0FBU0ssSUFBSSxDQUFDSCxPQUFPSSxJQUFJLENBQUM7WUFBRUMsV0FBVyxDQUFDO1FBQUUsR0FBR0MsT0FBTztRQUMxRSxNQUFNQyxPQUFPTCxRQUFRTSxHQUFHLENBQUMsQ0FBQ0MsSUFBTztnQkFDL0IsR0FBR0EsQ0FBQztnQkFDSkMsSUFBSUQsRUFBRUUsR0FBRyxDQUFDQyxRQUFRO1lBQ3BCO1FBQ0EsT0FBTzNCLHFEQUFZQSxDQUFDNEIsSUFBSSxDQUFDTjtJQUMzQixFQUFFLE9BQU9PLE9BQU87UUFDZCxPQUFPN0IscURBQVlBLENBQUM0QixJQUFJLENBQUM7WUFBRUMsT0FBTztRQUEyQixHQUFHO1lBQUVDLFFBQVE7UUFBSTtJQUNoRjtBQUNGO0FBRU8sZUFBZUMsS0FBSzNCLEdBQWdCO0lBQ3pDLElBQUk7UUFDRixNQUFNNEIsT0FBTyxNQUFNNUIsSUFBSXdCLElBQUk7UUFDM0JLLFFBQVFDLEdBQUcsQ0FBQyxrQkFBa0JGO1FBQzlCLE1BQU1yQixTQUFTLE1BQU1WLG9EQUFhQTtRQUNsQyxNQUFNVyxLQUFLRCxPQUFPQyxFQUFFLENBQUM7UUFDckIsTUFBTUMsV0FBV0QsR0FBR0UsVUFBVSxDQUFDO1FBRS9CLE1BQU1xQixhQUFhO1lBQ2pCLEdBQUdILElBQUk7WUFDUFosV0FBVyxJQUFJZ0I7WUFDZkMsV0FBVyxJQUFJRDtRQUNqQjtRQUVBLE1BQU1FLFNBQVMsTUFBTXpCLFNBQVMwQixTQUFTLENBQUNKO1FBQ3hDRixRQUFRQyxHQUFHLENBQUMsa0JBQWtCSTtRQUM5QixPQUFPdEMscURBQVlBLENBQUM0QixJQUFJLENBQUM7WUFBRSxHQUFHTyxVQUFVO1lBQUVWLElBQUlhLE9BQU9FLFVBQVUsQ0FBQ2IsUUFBUTtRQUFHLEdBQUc7WUFBRUcsUUFBUTtRQUFJO0lBQzlGLEVBQUUsT0FBT0QsT0FBTztRQUNkSSxRQUFRSixLQUFLLENBQUMsNkJBQTZCQTtRQUMzQyxPQUFPN0IscURBQVlBLENBQUM0QixJQUFJLENBQUM7WUFBRUMsT0FBTztRQUF3QixHQUFHO1lBQUVDLFFBQVE7UUFBSTtJQUM3RTtBQUNGO0FBRU8sZUFBZVcsSUFBSXJDLEdBQWdCO0lBQ3hDLElBQUk7UUFDRixNQUFNLEVBQUVxQixFQUFFLEVBQUUsR0FBR2lCLFNBQVMsR0FBRyxNQUFNdEMsSUFBSXdCLElBQUk7UUFDekMsTUFBTWpCLFNBQVMsTUFBTVYsb0RBQWFBO1FBQ2xDLE1BQU1XLEtBQUtELE9BQU9DLEVBQUUsQ0FBQztRQUNyQixNQUFNQyxXQUFXRCxHQUFHRSxVQUFVLENBQUM7UUFFL0IsTUFBTXdCLFNBQVMsTUFBTXpCLFNBQVM4QixnQkFBZ0IsQ0FDNUM7WUFBRWpCLEtBQUssSUFBSXhCLDZDQUFRQSxDQUFDdUI7UUFBSSxHQUN4QjtZQUFFbUIsTUFBTTtnQkFBRSxHQUFHRixPQUFPO2dCQUFFTCxXQUFXLElBQUlEO1lBQU87UUFBRSxHQUM5QztZQUFFUyxnQkFBZ0I7UUFBUTtRQUU1QixJQUFJLENBQUNQLFVBQVUsQ0FBQ0EsT0FBT1EsS0FBSyxFQUFFO1lBQzVCLE9BQU85QyxxREFBWUEsQ0FBQzRCLElBQUksQ0FBQztnQkFBRUMsT0FBTztZQUFvQixHQUFHO2dCQUFFQyxRQUFRO1lBQUk7UUFDekU7UUFDQSxNQUFNaUIsVUFBVVQsT0FBT1EsS0FBSztRQUM1QixPQUFPOUMscURBQVlBLENBQUM0QixJQUFJLENBQUM7WUFBRSxHQUFHbUIsT0FBTztZQUFFdEIsSUFBSXNCLFFBQVFyQixHQUFHLENBQUNDLFFBQVE7UUFBRztJQUNwRSxFQUFFLE9BQU9FLE9BQU87UUFDZCxPQUFPN0IscURBQVlBLENBQUM0QixJQUFJLENBQUM7WUFBRUMsT0FBTztRQUEyQixHQUFHO1lBQUVDLFFBQVE7UUFBSTtJQUNoRjtBQUNGO0FBRU8sZUFBZWtCLE9BQU81QyxHQUFnQjtJQUMzQyxJQUFJO1FBQ0YsTUFBTSxFQUFFcUIsRUFBRSxFQUFFLEdBQUcsTUFBTXJCLElBQUl3QixJQUFJO1FBQzdCLElBQUksQ0FBQ0gsSUFBSSxPQUFPekIscURBQVlBLENBQUM0QixJQUFJLENBQUM7WUFBRUMsT0FBTztRQUFhLEdBQUc7WUFBRUMsUUFBUTtRQUFJO1FBRXpFLE1BQU1uQixTQUFTLE1BQU1WLG9EQUFhQTtRQUNsQyxNQUFNVyxLQUFLRCxPQUFPQyxFQUFFLENBQUM7UUFDckIsTUFBTUMsV0FBV0QsR0FBR0UsVUFBVSxDQUFDO1FBRS9CLE1BQU13QixTQUFTLE1BQU16QixTQUFTb0MsU0FBUyxDQUFDO1lBQUV2QixLQUFLLElBQUl4Qiw2Q0FBUUEsQ0FBQ3VCO1FBQUk7UUFDaEUsSUFBSWEsT0FBT1ksWUFBWSxLQUFLLEdBQUc7WUFDN0IsT0FBT2xELHFEQUFZQSxDQUFDNEIsSUFBSSxDQUFDO2dCQUFFQyxPQUFPO1lBQW9CLEdBQUc7Z0JBQUVDLFFBQVE7WUFBSTtRQUN6RTtRQUNBLE9BQU85QixxREFBWUEsQ0FBQzRCLElBQUksQ0FBQztZQUFFdUIsU0FBUztRQUFLO0lBQzNDLEVBQUUsT0FBT3RCLE9BQU87UUFDZCxPQUFPN0IscURBQVlBLENBQUM0QixJQUFJLENBQUM7WUFBRUMsT0FBTztRQUEyQixHQUFHO1lBQUVDLFFBQVE7UUFBSTtJQUNoRjtBQUNGIiwic291cmNlcyI6WyJEOlxcaWRjYXJkXFxzbWFydGlkY2FyZFxcYXBwXFxhcGlcXHN0dWRlbnRzXFxyb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSBcIm5leHQvc2VydmVyXCJcbmltcG9ydCBjbGllbnRQcm9taXNlIGZyb20gXCJAL2xpYi9tb25nb2RiXCJcbmltcG9ydCB7IE9iamVjdElkIH0gZnJvbSBcIm1vbmdvZGJcIlxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKHJlcTogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCB1cmwgPSBuZXcgVVJMKHJlcS51cmwpXG4gICAgY29uc3QgYXBwTnVtYmVyID0gdXJsLnNlYXJjaFBhcmFtcy5nZXQoXCJhcHBsaWNhdGlvbl9udW1iZXJcIilcbiAgICBjb25zdCBwaG9uZSA9IHVybC5zZWFyY2hQYXJhbXMuZ2V0KFwicGhvbmVcIilcbiAgICBjb25zdCBjbGllbnQgPSBhd2FpdCBjbGllbnRQcm9taXNlXG4gICAgY29uc3QgZGIgPSBjbGllbnQuZGIoXCJpZGNhcmRcIilcbiAgICBjb25zdCBzdHVkZW50cyA9IGRiLmNvbGxlY3Rpb24oXCJzdHVkZW50c1wiKVxuXG4gICAgY29uc3QgcXVlcnk6IGFueSA9IHt9XG4gICAgaWYgKGFwcE51bWJlcikgcXVlcnkuYXBwbGljYXRpb25fbnVtYmVyID0gYXBwTnVtYmVyXG4gICAgaWYgKHBob25lKSBxdWVyeS5waG9uZSA9IHBob25lXG5cbiAgICBjb25zdCByZXN1bHRzID0gYXdhaXQgc3R1ZGVudHMuZmluZChxdWVyeSkuc29ydCh7IGNyZWF0ZWRBdDogLTEgfSkudG9BcnJheSgpXG4gICAgY29uc3QgZGF0YSA9IHJlc3VsdHMubWFwKChzKSA9PiAoe1xuICAgICAgLi4ucyxcbiAgICAgIGlkOiBzLl9pZC50b1N0cmluZygpLFxuICAgIH0pKVxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihkYXRhKVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiBcIkZhaWxlZCB0byBmZXRjaCBzdHVkZW50c1wiIH0sIHsgc3RhdHVzOiA1MDAgfSlcbiAgfVxufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUE9TVChyZXE6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgYm9keSA9IGF3YWl0IHJlcS5qc29uKClcbiAgICBjb25zb2xlLmxvZyhcIlJlY2VpdmVkIGJvZHk6XCIsIGJvZHkpXG4gICAgY29uc3QgY2xpZW50ID0gYXdhaXQgY2xpZW50UHJvbWlzZVxuICAgIGNvbnN0IGRiID0gY2xpZW50LmRiKFwiaWRjYXJkXCIpXG4gICAgY29uc3Qgc3R1ZGVudHMgPSBkYi5jb2xsZWN0aW9uKFwic3R1ZGVudHNcIilcblxuICAgIGNvbnN0IG5ld1N0dWRlbnQgPSB7XG4gICAgICAuLi5ib2R5LFxuICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpLFxuICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLFxuICAgIH1cblxuICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHN0dWRlbnRzLmluc2VydE9uZShuZXdTdHVkZW50KVxuICAgIGNvbnNvbGUubG9nKFwiSW5zZXJ0IHJlc3VsdDpcIiwgcmVzdWx0KVxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IC4uLm5ld1N0dWRlbnQsIGlkOiByZXN1bHQuaW5zZXJ0ZWRJZC50b1N0cmluZygpIH0sIHsgc3RhdHVzOiAyMDEgfSlcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKFwiUE9TVCAvYXBpL3N0dWRlbnRzIGVycm9yOlwiLCBlcnJvcilcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogXCJGYWlsZWQgdG8gYWRkIHN0dWRlbnRcIiB9LCB7IHN0YXR1czogNTAwIH0pXG4gIH1cbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBVVChyZXE6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgeyBpZCwgLi4udXBkYXRlcyB9ID0gYXdhaXQgcmVxLmpzb24oKVxuICAgIGNvbnN0IGNsaWVudCA9IGF3YWl0IGNsaWVudFByb21pc2VcbiAgICBjb25zdCBkYiA9IGNsaWVudC5kYihcInNtYXJ0aWRjYXJkXCIpXG4gICAgY29uc3Qgc3R1ZGVudHMgPSBkYi5jb2xsZWN0aW9uKFwic3R1ZGVudHNcIilcblxuICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHN0dWRlbnRzLmZpbmRPbmVBbmRVcGRhdGUoXG4gICAgICB7IF9pZDogbmV3IE9iamVjdElkKGlkKSB9LFxuICAgICAgeyAkc2V0OiB7IC4uLnVwZGF0ZXMsIHVwZGF0ZWRBdDogbmV3IERhdGUoKSB9IH0sXG4gICAgICB7IHJldHVybkRvY3VtZW50OiBcImFmdGVyXCIgfVxuICAgIClcbiAgICBpZiAoIXJlc3VsdCB8fCAhcmVzdWx0LnZhbHVlKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogXCJTdHVkZW50IG5vdCBmb3VuZFwiIH0sIHsgc3RhdHVzOiA0MDQgfSlcbiAgICB9XG4gICAgY29uc3QgdXBkYXRlZCA9IHJlc3VsdC52YWx1ZVxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IC4uLnVwZGF0ZWQsIGlkOiB1cGRhdGVkLl9pZC50b1N0cmluZygpIH0pXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6IFwiRmFpbGVkIHRvIHVwZGF0ZSBzdHVkZW50XCIgfSwgeyBzdGF0dXM6IDUwMCB9KVxuICB9XG59XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBERUxFVEUocmVxOiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnN0IHsgaWQgfSA9IGF3YWl0IHJlcS5qc29uKClcbiAgICBpZiAoIWlkKSByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogXCJNaXNzaW5nIGlkXCIgfSwgeyBzdGF0dXM6IDQwMCB9KVxuXG4gICAgY29uc3QgY2xpZW50ID0gYXdhaXQgY2xpZW50UHJvbWlzZVxuICAgIGNvbnN0IGRiID0gY2xpZW50LmRiKFwic21hcnRpZGNhcmRcIilcbiAgICBjb25zdCBzdHVkZW50cyA9IGRiLmNvbGxlY3Rpb24oXCJzdHVkZW50c1wiKVxuXG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgc3R1ZGVudHMuZGVsZXRlT25lKHsgX2lkOiBuZXcgT2JqZWN0SWQoaWQpIH0pXG4gICAgaWYgKHJlc3VsdC5kZWxldGVkQ291bnQgPT09IDApIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiBcIlN0dWRlbnQgbm90IGZvdW5kXCIgfSwgeyBzdGF0dXM6IDQwNCB9KVxuICAgIH1cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBzdWNjZXNzOiB0cnVlIH0pXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6IFwiRmFpbGVkIHRvIGRlbGV0ZSBzdHVkZW50XCIgfSwgeyBzdGF0dXM6IDUwMCB9KVxuICB9XG59Il0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsImNsaWVudFByb21pc2UiLCJPYmplY3RJZCIsIkdFVCIsInJlcSIsInVybCIsIlVSTCIsImFwcE51bWJlciIsInNlYXJjaFBhcmFtcyIsImdldCIsInBob25lIiwiY2xpZW50IiwiZGIiLCJzdHVkZW50cyIsImNvbGxlY3Rpb24iLCJxdWVyeSIsImFwcGxpY2F0aW9uX251bWJlciIsInJlc3VsdHMiLCJmaW5kIiwic29ydCIsImNyZWF0ZWRBdCIsInRvQXJyYXkiLCJkYXRhIiwibWFwIiwicyIsImlkIiwiX2lkIiwidG9TdHJpbmciLCJqc29uIiwiZXJyb3IiLCJzdGF0dXMiLCJQT1NUIiwiYm9keSIsImNvbnNvbGUiLCJsb2ciLCJuZXdTdHVkZW50IiwiRGF0ZSIsInVwZGF0ZWRBdCIsInJlc3VsdCIsImluc2VydE9uZSIsImluc2VydGVkSWQiLCJQVVQiLCJ1cGRhdGVzIiwiZmluZE9uZUFuZFVwZGF0ZSIsIiRzZXQiLCJyZXR1cm5Eb2N1bWVudCIsInZhbHVlIiwidXBkYXRlZCIsIkRFTEVURSIsImRlbGV0ZU9uZSIsImRlbGV0ZWRDb3VudCIsInN1Y2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/api/students/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/mongodb.ts":
/*!************************!*\
  !*** ./lib/mongodb.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_0__);\n\nconst uri = process.env.MONGODB_URI || \"\";\nconst options = {};\nlet client;\nlet clientPromise;\nif (!process.env.MONGODB_URI) {\n    throw new Error(\"Please add your MongoDB URI to .env\");\n}\nclient = new mongodb__WEBPACK_IMPORTED_MODULE_0__.MongoClient(uri, options);\nclientPromise = client.connect();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (clientPromise);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvbW9uZ29kYi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBcUM7QUFFckMsTUFBTUMsTUFBTUMsUUFBUUMsR0FBRyxDQUFDQyxXQUFXLElBQUk7QUFDdkMsTUFBTUMsVUFBVSxDQUFDO0FBRWpCLElBQUlDO0FBQ0osSUFBSUM7QUFFSixJQUFJLENBQUNMLFFBQVFDLEdBQUcsQ0FBQ0MsV0FBVyxFQUFFO0lBQzVCLE1BQU0sSUFBSUksTUFBTTtBQUNsQjtBQUVBRixTQUFTLElBQUlOLGdEQUFXQSxDQUFDQyxLQUFLSTtBQUM5QkUsZ0JBQWdCRCxPQUFPRyxPQUFPO0FBRTlCLGlFQUFlRixhQUFhQSxFQUFBIiwic291cmNlcyI6WyJEOlxcaWRjYXJkXFxzbWFydGlkY2FyZFxcbGliXFxtb25nb2RiLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE1vbmdvQ2xpZW50IH0gZnJvbSBcIm1vbmdvZGJcIlxyXG5cclxuY29uc3QgdXJpID0gcHJvY2Vzcy5lbnYuTU9OR09EQl9VUkkgfHwgXCJcIlxyXG5jb25zdCBvcHRpb25zID0ge31cclxuXHJcbmxldCBjbGllbnRcclxubGV0IGNsaWVudFByb21pc2U6IFByb21pc2U8TW9uZ29DbGllbnQ+XHJcblxyXG5pZiAoIXByb2Nlc3MuZW52Lk1PTkdPREJfVVJJKSB7XHJcbiAgdGhyb3cgbmV3IEVycm9yKFwiUGxlYXNlIGFkZCB5b3VyIE1vbmdvREIgVVJJIHRvIC5lbnZcIilcclxufVxyXG5cclxuY2xpZW50ID0gbmV3IE1vbmdvQ2xpZW50KHVyaSwgb3B0aW9ucylcclxuY2xpZW50UHJvbWlzZSA9IGNsaWVudC5jb25uZWN0KClcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGNsaWVudFByb21pc2UiXSwibmFtZXMiOlsiTW9uZ29DbGllbnQiLCJ1cmkiLCJwcm9jZXNzIiwiZW52IiwiTU9OR09EQl9VUkkiLCJvcHRpb25zIiwiY2xpZW50IiwiY2xpZW50UHJvbWlzZSIsIkVycm9yIiwiY29ubmVjdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstudents%2Froute&page=%2Fapi%2Fstudents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstudents%2Froute.ts&appDir=D%3A%5Cidcard%5Csmartidcard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Csmartidcard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstudents%2Froute&page=%2Fapi%2Fstudents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstudents%2Froute.ts&appDir=D%3A%5Cidcard%5Csmartidcard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Csmartidcard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_idcard_smartidcard_app_api_students_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/students/route.ts */ \"(rsc)/./app/api/students/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/students/route\",\n        pathname: \"/api/students\",\n        filename: \"route\",\n        bundlePath: \"app/api/students/route\"\n    },\n    resolvedPagePath: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\api\\\\students\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_idcard_smartidcard_app_api_students_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZzdHVkZW50cyUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGc3R1ZGVudHMlMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZzdHVkZW50cyUyRnJvdXRlLnRzJmFwcERpcj1EJTNBJTVDaWRjYXJkJTVDc21hcnRpZGNhcmQlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUQlM0ElNUNpZGNhcmQlNUNzbWFydGlkY2FyZCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDSztBQUNsRjtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IseUdBQW1CO0FBQzNDO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHNEQUFzRDtBQUM5RDtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUMwRjs7QUFFMUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiRDpcXFxcaWRjYXJkXFxcXHNtYXJ0aWRjYXJkXFxcXGFwcFxcXFxhcGlcXFxcc3R1ZGVudHNcXFxccm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL3N0dWRlbnRzL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvc3R1ZGVudHNcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL3N0dWRlbnRzL3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiRDpcXFxcaWRjYXJkXFxcXHNtYXJ0aWRjYXJkXFxcXGFwcFxcXFxhcGlcXFxcc3R1ZGVudHNcXFxccm91dGUudHNcIixcbiAgICBuZXh0Q29uZmlnT3V0cHV0LFxuICAgIHVzZXJsYW5kXG59KTtcbi8vIFB1bGwgb3V0IHRoZSBleHBvcnRzIHRoYXQgd2UgbmVlZCB0byBleHBvc2UgZnJvbSB0aGUgbW9kdWxlLiBUaGlzIHNob3VsZFxuLy8gYmUgZWxpbWluYXRlZCB3aGVuIHdlJ3ZlIG1vdmVkIHRoZSBvdGhlciByb3V0ZXMgdG8gdGhlIG5ldyBmb3JtYXQuIFRoZXNlXG4vLyBhcmUgdXNlZCB0byBob29rIGludG8gdGhlIHJvdXRlLlxuY29uc3QgeyB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICB3b3JrQXN5bmNTdG9yYWdlLFxuICAgICAgICB3b3JrVW5pdEFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstudents%2Froute&page=%2Fapi%2Fstudents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstudents%2Froute.ts&appDir=D%3A%5Cidcard%5Csmartidcard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Csmartidcard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "mongodb":
/*!**************************!*\
  !*** external "mongodb" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongodb");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstudents%2Froute&page=%2Fapi%2Fstudents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstudents%2Froute.ts&appDir=D%3A%5Cidcard%5Csmartidcard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Csmartidcard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();