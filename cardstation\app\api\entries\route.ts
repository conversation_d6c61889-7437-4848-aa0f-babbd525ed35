import { NextRequest, NextResponse } from "next/server"
import clientPromise from "@/lib/mongodb"
import { ObjectId } from "mongodb"

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url)
    const studentId = url.searchParams.get("student_id")

    if (!clientPromise) {
      // MongoDB not available, return empty array
      console.log("MongoDB not available, returning empty entries array")
      return NextResponse.json([])
    }

    const client = await clientPromise
    if (!client) {
      return NextResponse.json([])
    }

    const db = client.db("idcard")
    const entries = db.collection("entry_logs")

    const query: any = {}
    if (studentId) query.student_id = studentId

    const results = await entries.find(query).sort({ entry_time: -1 }).toArray()
    const data = results.map((e) => ({
      ...e,
      id: e._id.toString(),
    }))
    return NextResponse.json(data)
  } catch (error) {
    console.error("GET /api/entries error:", error)
    // Return empty array instead of error for better UX
    return NextResponse.json([])
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    console.log("Received entry body:", body)

    if (!clientPromise) {
      return NextResponse.json({ error: "Database not available" }, { status: 503 })
    }

    const client = await clientPromise
    if (!client) {
      return NextResponse.json({ error: "Database connection failed" }, { status: 503 })
    }

    const db = client.db("idcard")
    const entries = db.collection("entry_logs")

    // Check if student is already inside
    const existingEntry = await entries.findOne({
      student_id: body.student_id,
      exit_time: null
    })

    if (existingEntry) {
      // Student is inside, mark exit
      const result = await entries.findOneAndUpdate(
        { _id: existingEntry._id },
        { 
          $set: { 
            exit_time: new Date(),
            status: "exit",
            updated_at: new Date()
          } 
        },
        { returnDocument: "after" }
      )
      if (!result || !result.value) {
        return NextResponse.json({ error: "Failed to update entry" }, { status: 500 })
      }
      return NextResponse.json({ ...result.value, id: result.value._id.toString() })
    } else {
      // New entry
      const newEntry = {
        ...body,
        entry_time: new Date(),
        status: "entry",
        verified: true,
        created_at: new Date(),
        updated_at: new Date(),
      }

      const result = await entries.insertOne(newEntry)
      return NextResponse.json({ ...newEntry, id: result.insertedId.toString() }, { status: 201 })
    }
  } catch (error) {
    console.error("POST /api/entries error:", error)
    return NextResponse.json({ error: "Failed to add entry" }, { status: 500 })
  }
} 