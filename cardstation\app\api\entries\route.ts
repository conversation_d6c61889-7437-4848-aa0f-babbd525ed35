import { NextRequest, NextResponse } from "next/server"
import clientPromise from "@/lib/mongodb"
import { ObjectId } from "mongodb"

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url)
    const studentId = url.searchParams.get("student_id")

    if (!clientPromise) {
      // MongoDB not available, return sample entries for testing
      console.log("MongoDB not available, returning sample entries for testing")

      const sampleEntries = [
        {
          id: "entry_001",
          student_id: "STU_001",
          application_number: "APP20254105",
          student_name: "Test Student",
          entry_time: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
          exit_time: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), // 1 hour ago
          status: "exit",
          verified: true,
          verification_method: "qr_and_face",
          face_match_score: 85,
          created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()
        },
        {
          id: "entry_002",
          student_id: "STU_001",
          application_number: "APP20254105",
          student_name: "Test Student",
          entry_time: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
          exit_time: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // 3 hours ago
          status: "exit",
          verified: true,
          verification_method: "qr_and_face",
          face_match_score: 92,
          created_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString()
        }
      ]

      // Filter by student_id if provided
      let filteredEntries = sampleEntries
      if (studentId) {
        filteredEntries = sampleEntries.filter(e => e.student_id === studentId)
      }

      return NextResponse.json(filteredEntries)
    }

    const client = await clientPromise
    if (!client) {
      return NextResponse.json([])
    }

    const db = client.db("idcard")
    const entries = db.collection("entry_logs")

    const query: any = {}
    if (studentId) query.student_id = studentId

    const results = await entries.find(query).sort({ entry_time: -1 }).toArray()
    const data = results.map((e) => ({
      ...e,
      id: e._id.toString(),
    }))
    return NextResponse.json(data)
  } catch (error) {
    console.error("GET /api/entries error:", error)
    // Return sample entries for testing instead of empty array
    const sampleEntries = [
      {
        id: "entry_001",
        student_id: "STU_001",
        application_number: "APP20254105",
        student_name: "Test Student",
        entry_time: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        exit_time: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
        status: "exit",
        verified: true,
        verification_method: "qr_and_face",
        face_match_score: 85,
        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()
      }
    ]
    return NextResponse.json(sampleEntries)
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    console.log("Received entry body:", body)

    if (!clientPromise) {
      return NextResponse.json({ error: "Database not available" }, { status: 503 })
    }

    const client = await clientPromise
    if (!client) {
      return NextResponse.json({ error: "Database connection failed" }, { status: 503 })
    }

    const db = client.db("idcard")
    const entries = db.collection("entry_logs")

    // Check if student is already inside
    const existingEntry = await entries.findOne({
      student_id: body.student_id,
      exit_time: null
    })

    if (existingEntry) {
      // Student is inside, mark exit
      const result = await entries.findOneAndUpdate(
        { _id: existingEntry._id },
        { 
          $set: { 
            exit_time: new Date(),
            status: "exit",
            updated_at: new Date()
          } 
        },
        { returnDocument: "after" }
      )
      if (!result || !result.value) {
        return NextResponse.json({ error: "Failed to update entry" }, { status: 500 })
      }
      return NextResponse.json({ ...result.value, id: result.value._id.toString() })
    } else {
      // New entry with enhanced verification data
      const newEntry = {
        ...body,
        entry_time: new Date(),
        status: "entry",
        verified: true,
        verification_method: body.verification_method || "qr_and_face",
        face_match_score: body.face_match_score || null,
        qr_validated: body.qr_validated !== undefined ? body.qr_validated : true,
        verification_timestamp: body.verification_timestamp || new Date().toISOString(),
        station_id: body.station_id || "main_entrance",
        created_at: new Date(),
        updated_at: new Date(),
      }

      const result = await entries.insertOne(newEntry)
      return NextResponse.json({ ...newEntry, id: result.insertedId.toString() }, { status: 201 })
    }
  } catch (error) {
    console.error("POST /api/entries error:", error)
    return NextResponse.json({ error: "Failed to add entry" }, { status: 500 })
  }
} 