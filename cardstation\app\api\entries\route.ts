import { NextRequest, NextResponse } from "next/server"
import clientPromise from "@/lib/mongodb"
import { ObjectId } from "mongodb"

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url)
    const studentId = url.searchParams.get("student_id")

    // Always return sample entries for testing to ensure data is visible
    console.log("Returning sample entries for testing")

    // Generate current time for reference
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

    // Create sample entries with a mix of entry/exit records
    const sampleEntries = [
      // Today's entries (for today's count)
      {
        id: "entry_001",
        student_id: "STU_001",
        application_number: "APP20254105",
        student_name: "Test Student",
        entry_time: new Date(today.getTime() + 9 * 60 * 60 * 1000).toISOString(), // 9 AM today
        exit_time: new Date(today.getTime() + 10 * 60 * 60 * 1000).toISOString(), // 10 AM today
        status: "exit",
        verified: true,
        verification_method: "qr_and_face",
        face_match_score: 85,
        created_at: new Date(today.getTime() + 9 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(today.getTime() + 10 * 60 * 60 * 1000).toISOString()
      },
      {
        id: "entry_002",
        student_id: "STU_001",
        application_number: "APP20254105",
        student_name: "Test Student",
        entry_time: new Date(today.getTime() + 13 * 60 * 60 * 1000).toISOString(), // 1 PM today
        exit_time: null, // Still inside
        status: "entry",
        verified: true,
        verification_method: "qr_and_face",
        face_match_score: 92,
        created_at: new Date(today.getTime() + 13 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(today.getTime() + 13 * 60 * 60 * 1000).toISOString()
      },
      // Yesterday's entries
      {
        id: "entry_003",
        student_id: "STU_002",
        application_number: "APP20254106",
        student_name: "Another Student",
        entry_time: new Date(today.getTime() - 15 * 60 * 60 * 1000).toISOString(), // Yesterday
        exit_time: new Date(today.getTime() - 14 * 60 * 60 * 1000).toISOString(), // Yesterday
        status: "exit",
        verified: true,
        verification_method: "qr_and_face",
        face_match_score: 88,
        created_at: new Date(today.getTime() - 15 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(today.getTime() - 14 * 60 * 60 * 1000).toISOString()
      }
    ]

    // Filter by student_id if provided
    let filteredEntries = sampleEntries
    if (studentId) {
      filteredEntries = sampleEntries.filter(e => e.student_id === studentId)
    }

    return NextResponse.json(filteredEntries)

    // MongoDB code commented out for testing - uncomment when MongoDB is available
    /*
    const client = await clientPromise
    if (!client) {
      return NextResponse.json([])
    }

    const db = client.db("idcard")
    const entries = db.collection("entry_logs")

    const query: any = {}
    if (studentId) query.student_id = studentId

    const results = await entries.find(query).sort({ entry_time: -1 }).toArray()
    const data = results.map((e) => ({
      ...e,
      id: e._id.toString(),
    }))
    return NextResponse.json(data)
    */
  } catch (error) {
    console.error("GET /api/entries error:", error)
    // Return sample entries for testing instead of empty array
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

    const sampleEntries = [
      {
        id: "entry_001",
        student_id: "STU_001",
        application_number: "APP20254105",
        student_name: "Test Student",
        entry_time: new Date(today.getTime() + 9 * 60 * 60 * 1000).toISOString(),
        exit_time: new Date(today.getTime() + 10 * 60 * 60 * 1000).toISOString(),
        status: "exit",
        verified: true,
        verification_method: "qr_and_face",
        face_match_score: 85,
        created_at: new Date(today.getTime() + 9 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(today.getTime() + 10 * 60 * 60 * 1000).toISOString()
      }
    ]
    return NextResponse.json(sampleEntries)
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    console.log("Received entry body:", body)

    // Enhanced entry/exit logic with fallback support
    if (!clientPromise) {
      console.log("MongoDB not available, using localStorage simulation")

      // Simulate entry/exit logic for testing
      const now = new Date()
      const entryId = `entry_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      // For testing, we'll simulate checking if student was already inside today
      // In real implementation, this would check localStorage or other storage
      const isFirstScanToday = true // You can modify this logic

      const newEntry = {
        id: entryId,
        student_id: body.student_id,
        application_number: body.application_number,
        student_name: body.student_name,
        entry_time: now.toISOString(),
        exit_time: isFirstScanToday ? null : now.toISOString(),
        status: isFirstScanToday ? "entry" : "exit",
        verified: true,
        verification_method: body.verification_method || "qr_and_face",
        face_match_score: body.face_match_score || null,
        qr_validated: body.qr_validated !== undefined ? body.qr_validated : true,
        verification_timestamp: body.verification_timestamp || now.toISOString(),
        station_id: body.station_id || "main_entrance",
        created_at: now.toISOString(),
        updated_at: now.toISOString(),
      }

      console.log("Simulated entry created:", newEntry)
      return NextResponse.json(newEntry, { status: 201 })
    }

    const client = await clientPromise
    if (!client) {
      return NextResponse.json({ error: "Database connection failed" }, { status: 503 })
    }

    const db = client.db("idcard")
    const entries = db.collection("entry_logs")

    // Check if student is already inside (has entry today without exit)
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    const existingEntry = await entries.findOne({
      student_id: body.student_id,
      entry_time: {
        $gte: today,
        $lt: tomorrow
      },
      exit_time: null
    })

    if (existingEntry) {
      // Student is inside, mark exit
      const result = await entries.findOneAndUpdate(
        { _id: existingEntry._id },
        {
          $set: {
            exit_time: new Date(),
            status: "exit",
            updated_at: new Date()
          }
        },
        { returnDocument: "after" }
      )
      if (!result || !result.value) {
        return NextResponse.json({ error: "Failed to update entry" }, { status: 500 })
      }
      console.log("Exit recorded for student:", body.student_name)
      return NextResponse.json({ ...result.value, id: result.value._id.toString() })
    } else {
      // New entry with enhanced verification data
      const newEntry = {
        ...body,
        entry_time: new Date(),
        status: "entry",
        verified: true,
        verification_method: body.verification_method || "qr_and_face",
        face_match_score: body.face_match_score || null,
        qr_validated: body.qr_validated !== undefined ? body.qr_validated : true,
        verification_timestamp: body.verification_timestamp || new Date().toISOString(),
        station_id: body.station_id || "main_entrance",
        created_at: new Date(),
        updated_at: new Date(),
      }

      const result = await entries.insertOne(newEntry)
      console.log("Entry recorded for student:", body.student_name)
      return NextResponse.json({ ...newEntry, id: result.insertedId.toString() }, { status: 201 })
    }
  } catch (error) {
    console.error("POST /api/entries error:", error)
    return NextResponse.json({ error: "Failed to add entry" }, { status: 500 })
  }
}