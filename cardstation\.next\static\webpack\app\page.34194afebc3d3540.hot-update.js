"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/database-store.ts":
/*!*******************************!*\
  !*** ./lib/database-store.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dbStore: () => (/* binding */ dbStore)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ dbStore auto */ \n// Local storage keys\nconst STUDENTS_KEY = \"smart_id_students\";\nconst ENTRIES_KEY = \"smart_id_entries\";\nclass DatabaseStore {\n    isSupabaseAvailable() {\n        return _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase !== null && \"object\" !== \"undefined\";\n    }\n    isLocalStorageAvailable() {\n        return  true && typeof window.localStorage !== \"undefined\";\n    }\n    // Local Storage Methods\n    saveStudentsToLocal(students) {\n        if (this.isLocalStorageAvailable()) {\n            localStorage.setItem(STUDENTS_KEY, JSON.stringify(students));\n        }\n    }\n    loadStudentsFromLocal() {\n        if (!this.isLocalStorageAvailable()) return [];\n        try {\n            const data = localStorage.getItem(STUDENTS_KEY);\n            if (!data) return [];\n            const students = JSON.parse(data);\n            return students.map((s)=>({\n                    ...s,\n                    createdAt: new Date(s.createdAt),\n                    updatedAt: new Date(s.updatedAt)\n                }));\n        } catch (error) {\n            console.error(\"Error loading students from localStorage:\", error);\n            return [];\n        }\n    }\n    saveEntriesToLocal(entries) {\n        if (this.isLocalStorageAvailable()) {\n            localStorage.setItem(ENTRIES_KEY, JSON.stringify(entries));\n        }\n    }\n    loadEntriesFromLocal() {\n        if (!this.isLocalStorageAvailable()) return [];\n        try {\n            const data = localStorage.getItem(ENTRIES_KEY);\n            if (!data) return [];\n            const entries = JSON.parse(data);\n            return entries.map((e)=>({\n                    ...e,\n                    entryTime: new Date(e.entryTime),\n                    exitTime: e.exitTime ? new Date(e.exitTime) : undefined,\n                    createdAt: new Date(e.createdAt)\n                }));\n        } catch (error) {\n            console.error(\"Error loading entries from localStorage:\", error);\n            return [];\n        }\n    }\n    // Student Management\n    async addStudent(student) {\n        const res = await fetch(\"/api/students\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(student)\n        });\n        if (!res.ok) throw new Error(\"Failed to add student\");\n        const data = await res.json();\n        return {\n            ...data,\n            createdAt: new Date(data.createdAt),\n            updatedAt: new Date(data.updatedAt)\n        };\n    }\n    async getStudents() {\n        try {\n            // Try API first\n            const res = await fetch(\"/api/students\");\n            if (res.ok) {\n                const data = await res.json();\n                console.log(\"✅ Students loaded from API:\", data.length);\n                return data.map((s)=>({\n                        ...s,\n                        createdAt: new Date(s.createdAt || s.created_at || new Date()),\n                        updatedAt: new Date(s.updatedAt || s.updated_at || new Date())\n                    }));\n            } else {\n                throw new Error(\"API failed\");\n            }\n        } catch (error) {\n            console.warn(\"⚠️ Students API not available, using localStorage fallback\");\n            // Fallback to localStorage\n            const localStudents = this.loadStudentsFromLocal();\n            console.log(\"✅ Students loaded from localStorage:\", localStudents.length);\n            return localStudents;\n        }\n    }\n    async getStudentByAppNumber(appNumber) {\n        try {\n            // Try API first\n            const res = await fetch(\"/api/students?application_number=\".concat(encodeURIComponent(appNumber)));\n            if (res.ok) {\n                const data = await res.json();\n                if (!data || data.length === 0) return null;\n                const s = data[0];\n                console.log(\"✅ Student found via API:\", s.name);\n                return {\n                    ...s,\n                    createdAt: new Date(s.createdAt || s.created_at || new Date()),\n                    updatedAt: new Date(s.updatedAt || s.updated_at || new Date())\n                };\n            } else {\n                throw new Error(\"API failed\");\n            }\n        } catch (error) {\n            console.warn(\"⚠️ Student API not available, using localStorage fallback\");\n            // Fallback to localStorage\n            const localStudents = this.loadStudentsFromLocal();\n            const student = localStudents.find((s)=>s.application_number === appNumber);\n            if (student) {\n                console.log(\"✅ Student found via localStorage:\", student.name);\n            } else {\n                console.log(\"❌ Student not found in localStorage\");\n            }\n            return student || null;\n        }\n    }\n    async getStudentByAppAndPhone(appNumber, phone) {\n        const url = \"/api/students?application_number=\".concat(encodeURIComponent(appNumber), \"&phone=\").concat(encodeURIComponent(phone));\n        const res = await fetch(url);\n        if (!res.ok) return null;\n        const data = await res.json();\n        if (!data || data.length === 0) return null;\n        const s = data[0];\n        return {\n            ...s,\n            createdAt: new Date(s.createdAt),\n            updatedAt: new Date(s.updatedAt)\n        };\n    }\n    async updateStudent(id, updates) {\n        const res = await fetch(\"/api/students\", {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                id,\n                ...updates\n            })\n        });\n        if (!res.ok) throw new Error(\"Failed to update student\");\n        const data = await res.json();\n        return {\n            ...data,\n            createdAt: new Date(data.createdAt),\n            updatedAt: new Date(data.updatedAt)\n        };\n    }\n    async deleteStudent(id) {\n        const res = await fetch(\"/api/students\", {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                id\n            })\n        });\n        if (!res.ok) throw new Error(\"Failed to delete student\");\n        return true;\n    }\n    // Entry Log Management - Using API route for better reliability\n    async addEntry(studentId, applicationNumber, studentName) {\n        try {\n            const entryData = {\n                student_id: studentId,\n                application_number: applicationNumber,\n                student_name: studentName,\n                verification_method: \"qr_and_face\",\n                qr_validated: true,\n                verification_timestamp: new Date().toISOString(),\n                station_id: \"main_entrance\"\n            };\n            console.log(\"Sending entry data to API:\", entryData);\n            try {\n                // Try API first\n                const res = await fetch('/api/entries', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(entryData)\n                });\n                if (res.ok) {\n                    const data = await res.json();\n                    console.log(\"✅ Entry recorded via API:\", data);\n                    return {\n                        ...data,\n                        entryTime: new Date(data.entry_time),\n                        exitTime: data.exit_time ? new Date(data.exit_time) : undefined,\n                        createdAt: new Date(data.created_at || data.entry_time),\n                        updatedAt: new Date(data.updated_at || data.entry_time)\n                    };\n                } else {\n                    throw new Error(\"API failed\");\n                }\n            } catch (apiError) {\n                console.warn(\"⚠️ API not available, using localStorage fallback\");\n                // Fallback to localStorage\n                const existingEntries = this.loadEntriesFromLocal();\n                // Check if student already has entry today without exit\n                const today = new Date();\n                today.setHours(0, 0, 0, 0);\n                const tomorrow = new Date(today);\n                tomorrow.setDate(tomorrow.getDate() + 1);\n                const todayEntry = existingEntries.find((entry)=>entry.student_id === studentId && entry.entryTime >= today && entry.entryTime < tomorrow && !entry.exitTime);\n                const now = new Date();\n                const entryId = \"entry_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 9));\n                let newEntry;\n                if (todayEntry) {\n                    // This is an exit\n                    todayEntry.exitTime = now;\n                    todayEntry.status = \"exit\";\n                    todayEntry.updatedAt = now;\n                    // Update existing entry\n                    const updatedEntries = existingEntries.map((entry)=>entry.id === todayEntry.id ? todayEntry : entry);\n                    this.saveEntriesToLocal(updatedEntries);\n                    newEntry = todayEntry;\n                    console.log(\"✅ EXIT recorded via localStorage:\", newEntry);\n                } else {\n                    // This is an entry\n                    newEntry = {\n                        id: entryId,\n                        student_id: studentId,\n                        application_number: applicationNumber,\n                        student_name: studentName,\n                        entryTime: now,\n                        exitTime: undefined,\n                        status: \"entry\",\n                        verified: true,\n                        createdAt: now,\n                        updatedAt: now\n                    };\n                    existingEntries.push(newEntry);\n                    this.saveEntriesToLocal(existingEntries);\n                    console.log(\"✅ ENTRY recorded via localStorage:\", newEntry);\n                }\n                return newEntry;\n            }\n        } catch (error) {\n            console.error(\"❌ Error adding entry:\", error);\n            throw error;\n        }\n    }\n    async getStudentEntries(studentId) {\n        try {\n            // Use API route which handles both MongoDB and fallback\n            const res = await fetch(\"/api/entries?student_id=\".concat(encodeURIComponent(studentId)));\n            if (!res.ok) {\n                console.error(\"Failed to fetch entries from API\");\n                return [];\n            }\n            const data = await res.json();\n            return data.map((e)=>({\n                    ...e,\n                    entryTime: new Date(e.entry_time),\n                    exitTime: e.exit_time ? new Date(e.exit_time) : undefined,\n                    createdAt: new Date(e.created_at || e.entry_time),\n                    updatedAt: new Date(e.updated_at || e.entry_time)\n                }));\n        } catch (error) {\n            console.error(\"Error fetching student entries:\", error);\n            return [];\n        }\n    }\n    async getAllEntries() {\n        try {\n            // Try API first\n            const res = await fetch('/api/entries');\n            if (res.ok) {\n                const data = await res.json();\n                console.log(\"✅ Entries loaded from API:\", data.length);\n                return data.map((e)=>({\n                        ...e,\n                        entryTime: new Date(e.entry_time),\n                        exitTime: e.exit_time ? new Date(e.exit_time) : undefined,\n                        createdAt: new Date(e.created_at || e.entry_time),\n                        updatedAt: new Date(e.updated_at || e.entry_time)\n                    }));\n            } else {\n                throw new Error(\"API failed\");\n            }\n        } catch (error) {\n            console.warn(\"⚠️ API not available, using localStorage fallback\");\n            // Fallback to localStorage\n            const localEntries = this.loadEntriesFromLocal();\n            console.log(\"✅ Entries loaded from localStorage:\", localEntries.length);\n            return localEntries;\n        }\n    }\n    async getTodayEntries() {\n        try {\n            // Get all entries and filter for today\n            const allEntries = await this.getAllEntries();\n            const today = new Date().toDateString();\n            return allEntries.filter((e)=>e.entryTime.toDateString() === today);\n        } catch (error) {\n            console.error(\"Error fetching today entries:\", error);\n            return [];\n        }\n    }\n    // Admin Authentication\n    async authenticateAdmin(username, password) {\n        if (this.isSupabaseAvailable() && _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase) {\n            // Use Supabase\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"admin_users\").select(\"*\").eq(\"username\", username).single();\n            if (error || !data) {\n                return false;\n            }\n            // Simple password check (in production, use proper hashing)\n            return password === \"admin123\";\n        } else {\n            // Fallback authentication for demo\n            return username === \"admin\" && password === \"admin123\";\n        }\n    }\n    // Utility functions\n    generateApplicationNumber() {\n        const year = new Date().getFullYear();\n        const random = Math.floor(Math.random() * 10000).toString().padStart(4, \"0\");\n        return \"APP\".concat(year).concat(random);\n    }\n    convertStudentDates(student) {\n        return {\n            ...student,\n            createdAt: new Date(student.created_at),\n            updatedAt: new Date(student.updated_at)\n        };\n    }\n    convertEntryLogDates(entry) {\n        return {\n            ...entry,\n            entryTime: new Date(entry.entry_time),\n            exitTime: entry.exit_time ? new Date(entry.exit_time) : undefined,\n            createdAt: new Date(entry.created_at)\n        };\n    }\n    // Clear all local data (for testing)\n    clearLocalData() {\n        if (this.isLocalStorageAvailable()) {\n            localStorage.removeItem(STUDENTS_KEY);\n            localStorage.removeItem(ENTRIES_KEY);\n        }\n    }\n    // Get storage info\n    getStorageInfo() {\n        return {\n            mode: \"Cloud\",\n            studentsCount: 0,\n            entriesCount: 0\n        };\n    }\n}\nconst dbStore = new DatabaseStore();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi9kYXRhYmFzZS1zdG9yZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs2REFFcUM7QUFxQnJDLHFCQUFxQjtBQUNyQixNQUFNQyxlQUFlO0FBQ3JCLE1BQU1DLGNBQWM7QUFFcEIsTUFBTUM7SUFDSUMsc0JBQStCO1FBQ3JDLE9BQU9KLCtDQUFRQSxLQUFLLFFBQVEsYUFBa0I7SUFDaEQ7SUFFUUssMEJBQW1DO1FBQ3pDLE9BQU8sS0FBNkIsSUFBSSxPQUFPQyxPQUFPQyxZQUFZLEtBQUs7SUFDekU7SUFFQSx3QkFBd0I7SUFDaEJDLG9CQUFvQkMsUUFBNEIsRUFBUTtRQUM5RCxJQUFJLElBQUksQ0FBQ0osdUJBQXVCLElBQUk7WUFDbENFLGFBQWFHLE9BQU8sQ0FBQ1QsY0FBY1UsS0FBS0MsU0FBUyxDQUFDSDtRQUNwRDtJQUNGO0lBRVFJLHdCQUE0QztRQUNsRCxJQUFJLENBQUMsSUFBSSxDQUFDUix1QkFBdUIsSUFBSSxPQUFPLEVBQUU7UUFFOUMsSUFBSTtZQUNGLE1BQU1TLE9BQU9QLGFBQWFRLE9BQU8sQ0FBQ2Q7WUFDbEMsSUFBSSxDQUFDYSxNQUFNLE9BQU8sRUFBRTtZQUVwQixNQUFNTCxXQUFXRSxLQUFLSyxLQUFLLENBQUNGO1lBQzVCLE9BQU9MLFNBQVNRLEdBQUcsQ0FBQyxDQUFDQyxJQUFZO29CQUMvQixHQUFHQSxDQUFDO29CQUNKQyxXQUFXLElBQUlDLEtBQUtGLEVBQUVDLFNBQVM7b0JBQy9CRSxXQUFXLElBQUlELEtBQUtGLEVBQUVHLFNBQVM7Z0JBQ2pDO1FBQ0YsRUFBRSxPQUFPQyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw2Q0FBNkNBO1lBQzNELE9BQU8sRUFBRTtRQUNYO0lBQ0Y7SUFFUUUsbUJBQW1CQyxPQUE0QixFQUFRO1FBQzdELElBQUksSUFBSSxDQUFDcEIsdUJBQXVCLElBQUk7WUFDbENFLGFBQWFHLE9BQU8sQ0FBQ1IsYUFBYVMsS0FBS0MsU0FBUyxDQUFDYTtRQUNuRDtJQUNGO0lBRVFDLHVCQUE0QztRQUNsRCxJQUFJLENBQUMsSUFBSSxDQUFDckIsdUJBQXVCLElBQUksT0FBTyxFQUFFO1FBRTlDLElBQUk7WUFDRixNQUFNUyxPQUFPUCxhQUFhUSxPQUFPLENBQUNiO1lBQ2xDLElBQUksQ0FBQ1ksTUFBTSxPQUFPLEVBQUU7WUFFcEIsTUFBTVcsVUFBVWQsS0FBS0ssS0FBSyxDQUFDRjtZQUMzQixPQUFPVyxRQUFRUixHQUFHLENBQUMsQ0FBQ1UsSUFBWTtvQkFDOUIsR0FBR0EsQ0FBQztvQkFDSkMsV0FBVyxJQUFJUixLQUFLTyxFQUFFQyxTQUFTO29CQUMvQkMsVUFBVUYsRUFBRUUsUUFBUSxHQUFHLElBQUlULEtBQUtPLEVBQUVFLFFBQVEsSUFBSUM7b0JBQzlDWCxXQUFXLElBQUlDLEtBQUtPLEVBQUVSLFNBQVM7Z0JBQ2pDO1FBQ0YsRUFBRSxPQUFPRyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw0Q0FBNENBO1lBQzFELE9BQU8sRUFBRTtRQUNYO0lBQ0Y7SUFFQSxxQkFBcUI7SUFDckIsTUFBTVMsV0FBV0MsT0FBZ0UsRUFBNkI7UUFDNUcsTUFBTUMsTUFBTSxNQUFNQyxNQUFNLGlCQUFpQjtZQUN2Q0MsUUFBUTtZQUNSQyxTQUFTO2dCQUFFLGdCQUFnQjtZQUFtQjtZQUM5Q0MsTUFBTTFCLEtBQUtDLFNBQVMsQ0FBQ29CO1FBQ3ZCO1FBQ0EsSUFBSSxDQUFDQyxJQUFJSyxFQUFFLEVBQUUsTUFBTSxJQUFJQyxNQUFNO1FBQzdCLE1BQU16QixPQUFPLE1BQU1tQixJQUFJTyxJQUFJO1FBQzNCLE9BQU87WUFDTCxHQUFHMUIsSUFBSTtZQUNQSyxXQUFXLElBQUlDLEtBQUtOLEtBQUtLLFNBQVM7WUFDbENFLFdBQVcsSUFBSUQsS0FBS04sS0FBS08sU0FBUztRQUNwQztJQUNGO0lBRUEsTUFBTW9CLGNBQTJDO1FBQy9DLElBQUk7WUFDRixnQkFBZ0I7WUFDaEIsTUFBTVIsTUFBTSxNQUFNQyxNQUFNO1lBQ3hCLElBQUlELElBQUlLLEVBQUUsRUFBRTtnQkFDVixNQUFNeEIsT0FBTyxNQUFNbUIsSUFBSU8sSUFBSTtnQkFDM0JqQixRQUFRbUIsR0FBRyxDQUFDLCtCQUErQjVCLEtBQUs2QixNQUFNO2dCQUN0RCxPQUFPN0IsS0FBS0csR0FBRyxDQUFDLENBQUNDLElBQVk7d0JBQzNCLEdBQUdBLENBQUM7d0JBQ0pDLFdBQVcsSUFBSUMsS0FBS0YsRUFBRUMsU0FBUyxJQUFJRCxFQUFFMEIsVUFBVSxJQUFJLElBQUl4Qjt3QkFDdkRDLFdBQVcsSUFBSUQsS0FBS0YsRUFBRUcsU0FBUyxJQUFJSCxFQUFFMkIsVUFBVSxJQUFJLElBQUl6QjtvQkFDekQ7WUFDRixPQUFPO2dCQUNMLE1BQU0sSUFBSW1CLE1BQU07WUFDbEI7UUFDRixFQUFFLE9BQU9qQixPQUFPO1lBQ2RDLFFBQVF1QixJQUFJLENBQUM7WUFDYiwyQkFBMkI7WUFDM0IsTUFBTUMsZ0JBQWdCLElBQUksQ0FBQ2xDLHFCQUFxQjtZQUNoRFUsUUFBUW1CLEdBQUcsQ0FBQyx3Q0FBd0NLLGNBQWNKLE1BQU07WUFDeEUsT0FBT0k7UUFDVDtJQUNGO0lBRUEsTUFBTUMsc0JBQXNCQyxTQUFpQixFQUFvQztRQUMvRSxJQUFJO1lBQ0YsZ0JBQWdCO1lBQ2hCLE1BQU1oQixNQUFNLE1BQU1DLE1BQU0sb0NBQWtFLE9BQTlCZ0IsbUJBQW1CRDtZQUMvRSxJQUFJaEIsSUFBSUssRUFBRSxFQUFFO2dCQUNWLE1BQU14QixPQUFPLE1BQU1tQixJQUFJTyxJQUFJO2dCQUMzQixJQUFJLENBQUMxQixRQUFRQSxLQUFLNkIsTUFBTSxLQUFLLEdBQUcsT0FBTztnQkFDdkMsTUFBTXpCLElBQUlKLElBQUksQ0FBQyxFQUFFO2dCQUNqQlMsUUFBUW1CLEdBQUcsQ0FBQyw0QkFBNEJ4QixFQUFFaUMsSUFBSTtnQkFDOUMsT0FBTztvQkFDTCxHQUFHakMsQ0FBQztvQkFDSkMsV0FBVyxJQUFJQyxLQUFLRixFQUFFQyxTQUFTLElBQUlELEVBQUUwQixVQUFVLElBQUksSUFBSXhCO29CQUN2REMsV0FBVyxJQUFJRCxLQUFLRixFQUFFRyxTQUFTLElBQUlILEVBQUUyQixVQUFVLElBQUksSUFBSXpCO2dCQUN6RDtZQUNGLE9BQU87Z0JBQ0wsTUFBTSxJQUFJbUIsTUFBTTtZQUNsQjtRQUNGLEVBQUUsT0FBT2pCLE9BQU87WUFDZEMsUUFBUXVCLElBQUksQ0FBQztZQUNiLDJCQUEyQjtZQUMzQixNQUFNQyxnQkFBZ0IsSUFBSSxDQUFDbEMscUJBQXFCO1lBQ2hELE1BQU1tQixVQUFVZSxjQUFjSyxJQUFJLENBQUNsQyxDQUFBQSxJQUFLQSxFQUFFbUMsa0JBQWtCLEtBQUtKO1lBQ2pFLElBQUlqQixTQUFTO2dCQUNYVCxRQUFRbUIsR0FBRyxDQUFDLHFDQUFxQ1YsUUFBUW1CLElBQUk7WUFDL0QsT0FBTztnQkFDTDVCLFFBQVFtQixHQUFHLENBQUM7WUFDZDtZQUNBLE9BQU9WLFdBQVc7UUFDcEI7SUFDRjtJQUVBLE1BQU1zQix3QkFBd0JMLFNBQWlCLEVBQUVNLEtBQWEsRUFBb0M7UUFDaEcsTUFBTUMsTUFBTSxvQ0FBMkVOLE9BQXZDQSxtQkFBbUJELFlBQVcsV0FBbUMsT0FBMUJDLG1CQUFtQks7UUFDMUcsTUFBTXRCLE1BQU0sTUFBTUMsTUFBTXNCO1FBQ3hCLElBQUksQ0FBQ3ZCLElBQUlLLEVBQUUsRUFBRSxPQUFPO1FBQ3BCLE1BQU14QixPQUFPLE1BQU1tQixJQUFJTyxJQUFJO1FBQzNCLElBQUksQ0FBQzFCLFFBQVFBLEtBQUs2QixNQUFNLEtBQUssR0FBRyxPQUFPO1FBQ3ZDLE1BQU16QixJQUFJSixJQUFJLENBQUMsRUFBRTtRQUNqQixPQUFPO1lBQ0wsR0FBR0ksQ0FBQztZQUNKQyxXQUFXLElBQUlDLEtBQUtGLEVBQUVDLFNBQVM7WUFDL0JFLFdBQVcsSUFBSUQsS0FBS0YsRUFBRUcsU0FBUztRQUNqQztJQUNGO0lBRUEsTUFBTW9DLGNBQWNDLEVBQVUsRUFBRUMsT0FBc0IsRUFBb0M7UUFDeEYsTUFBTTFCLE1BQU0sTUFBTUMsTUFBTSxpQkFBaUI7WUFDdkNDLFFBQVE7WUFDUkMsU0FBUztnQkFBRSxnQkFBZ0I7WUFBbUI7WUFDOUNDLE1BQU0xQixLQUFLQyxTQUFTLENBQUM7Z0JBQUU4QztnQkFBSSxHQUFHQyxPQUFPO1lBQUM7UUFDeEM7UUFDQSxJQUFJLENBQUMxQixJQUFJSyxFQUFFLEVBQUUsTUFBTSxJQUFJQyxNQUFNO1FBQzdCLE1BQU16QixPQUFPLE1BQU1tQixJQUFJTyxJQUFJO1FBQzNCLE9BQU87WUFDTCxHQUFHMUIsSUFBSTtZQUNQSyxXQUFXLElBQUlDLEtBQUtOLEtBQUtLLFNBQVM7WUFDbENFLFdBQVcsSUFBSUQsS0FBS04sS0FBS08sU0FBUztRQUNwQztJQUNGO0lBRUEsTUFBTXVDLGNBQWNGLEVBQVUsRUFBb0I7UUFDaEQsTUFBTXpCLE1BQU0sTUFBTUMsTUFBTSxpQkFBaUI7WUFDdkNDLFFBQVE7WUFDUkMsU0FBUztnQkFBRSxnQkFBZ0I7WUFBbUI7WUFDOUNDLE1BQU0xQixLQUFLQyxTQUFTLENBQUM7Z0JBQUU4QztZQUFHO1FBQzVCO1FBQ0EsSUFBSSxDQUFDekIsSUFBSUssRUFBRSxFQUFFLE1BQU0sSUFBSUMsTUFBTTtRQUM3QixPQUFPO0lBQ1Q7SUFFQSxnRUFBZ0U7SUFDaEUsTUFBTXNCLFNBQVNDLFNBQWlCLEVBQUVDLGlCQUF5QixFQUFFQyxXQUFtQixFQUE4QjtRQUM1RyxJQUFJO1lBQ0YsTUFBTUMsWUFBWTtnQkFDaEJDLFlBQVlKO2dCQUNaVCxvQkFBb0JVO2dCQUNwQkksY0FBY0g7Z0JBQ2RJLHFCQUFxQjtnQkFDckJDLGNBQWM7Z0JBQ2RDLHdCQUF3QixJQUFJbEQsT0FBT21ELFdBQVc7Z0JBQzlDQyxZQUFZO1lBQ2Q7WUFFQWpELFFBQVFtQixHQUFHLENBQUMsOEJBQThCdUI7WUFFMUMsSUFBSTtnQkFDRixnQkFBZ0I7Z0JBQ2hCLE1BQU1oQyxNQUFNLE1BQU1DLE1BQU0sZ0JBQWdCO29CQUN0Q0MsUUFBUTtvQkFDUkMsU0FBUzt3QkFDUCxnQkFBZ0I7b0JBQ2xCO29CQUNBQyxNQUFNMUIsS0FBS0MsU0FBUyxDQUFDcUQ7Z0JBQ3ZCO2dCQUVBLElBQUloQyxJQUFJSyxFQUFFLEVBQUU7b0JBQ1YsTUFBTXhCLE9BQU8sTUFBTW1CLElBQUlPLElBQUk7b0JBQzNCakIsUUFBUW1CLEdBQUcsQ0FBQyw2QkFBNkI1QjtvQkFDekMsT0FBTzt3QkFDTCxHQUFHQSxJQUFJO3dCQUNQYyxXQUFXLElBQUlSLEtBQUtOLEtBQUsyRCxVQUFVO3dCQUNuQzVDLFVBQVVmLEtBQUs0RCxTQUFTLEdBQUcsSUFBSXRELEtBQUtOLEtBQUs0RCxTQUFTLElBQUk1Qzt3QkFDdERYLFdBQVcsSUFBSUMsS0FBS04sS0FBSzhCLFVBQVUsSUFBSTlCLEtBQUsyRCxVQUFVO3dCQUN0RHBELFdBQVcsSUFBSUQsS0FBS04sS0FBSytCLFVBQVUsSUFBSS9CLEtBQUsyRCxVQUFVO29CQUN4RDtnQkFDRixPQUFPO29CQUNMLE1BQU0sSUFBSWxDLE1BQU07Z0JBQ2xCO1lBQ0YsRUFBRSxPQUFPb0MsVUFBVTtnQkFDakJwRCxRQUFRdUIsSUFBSSxDQUFDO2dCQUViLDJCQUEyQjtnQkFDM0IsTUFBTThCLGtCQUFrQixJQUFJLENBQUNsRCxvQkFBb0I7Z0JBRWpELHdEQUF3RDtnQkFDeEQsTUFBTW1ELFFBQVEsSUFBSXpEO2dCQUNsQnlELE1BQU1DLFFBQVEsQ0FBQyxHQUFHLEdBQUcsR0FBRztnQkFDeEIsTUFBTUMsV0FBVyxJQUFJM0QsS0FBS3lEO2dCQUMxQkUsU0FBU0MsT0FBTyxDQUFDRCxTQUFTRSxPQUFPLEtBQUs7Z0JBRXRDLE1BQU1DLGFBQWFOLGdCQUFnQnhCLElBQUksQ0FBQytCLENBQUFBLFFBQ3RDQSxNQUFNakIsVUFBVSxLQUFLSixhQUNyQnFCLE1BQU12RCxTQUFTLElBQUlpRCxTQUNuQk0sTUFBTXZELFNBQVMsR0FBR21ELFlBQ2xCLENBQUNJLE1BQU10RCxRQUFRO2dCQUdqQixNQUFNdUQsTUFBTSxJQUFJaEU7Z0JBQ2hCLE1BQU1pRSxVQUFVLFNBQXVCQyxPQUFkbEUsS0FBS2dFLEdBQUcsSUFBRyxLQUE4QyxPQUEzQ0UsS0FBS0MsTUFBTSxHQUFHQyxRQUFRLENBQUMsSUFBSUMsU0FBUyxDQUFDLEdBQUc7Z0JBRS9FLElBQUlDO2dCQUVKLElBQUlSLFlBQVk7b0JBQ2Qsa0JBQWtCO29CQUNsQkEsV0FBV3JELFFBQVEsR0FBR3VEO29CQUN0QkYsV0FBV1MsTUFBTSxHQUFHO29CQUNwQlQsV0FBVzdELFNBQVMsR0FBRytEO29CQUV2Qix3QkFBd0I7b0JBQ3hCLE1BQU1RLGlCQUFpQmhCLGdCQUFnQjNELEdBQUcsQ0FBQ2tFLENBQUFBLFFBQ3pDQSxNQUFNekIsRUFBRSxLQUFLd0IsV0FBV3hCLEVBQUUsR0FBR3dCLGFBQWFDO29CQUU1QyxJQUFJLENBQUMzRCxrQkFBa0IsQ0FBQ29FO29CQUV4QkYsV0FBV1I7b0JBQ1gzRCxRQUFRbUIsR0FBRyxDQUFDLHFDQUFxQ2dEO2dCQUNuRCxPQUFPO29CQUNMLG1CQUFtQjtvQkFDbkJBLFdBQVc7d0JBQ1RoQyxJQUFJMkI7d0JBQ0puQixZQUFZSjt3QkFDWlQsb0JBQW9CVTt3QkFDcEJJLGNBQWNIO3dCQUNkcEMsV0FBV3dEO3dCQUNYdkQsVUFBVUM7d0JBQ1Y2RCxRQUFRO3dCQUNSRSxVQUFVO3dCQUNWMUUsV0FBV2lFO3dCQUNYL0QsV0FBVytEO29CQUNiO29CQUVBUixnQkFBZ0JrQixJQUFJLENBQUNKO29CQUNyQixJQUFJLENBQUNsRSxrQkFBa0IsQ0FBQ29EO29CQUN4QnJELFFBQVFtQixHQUFHLENBQUMsc0NBQXNDZ0Q7Z0JBQ3BEO2dCQUVBLE9BQU9BO1lBQ1Q7UUFDRixFQUFFLE9BQU9wRSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyx5QkFBeUJBO1lBQ3ZDLE1BQU1BO1FBQ1I7SUFDRjtJQUdBLE1BQU15RSxrQkFBa0JqQyxTQUFpQixFQUFnQztRQUN2RSxJQUFJO1lBQ0Ysd0RBQXdEO1lBQ3hELE1BQU03QixNQUFNLE1BQU1DLE1BQU0sMkJBQXlELE9BQTlCZ0IsbUJBQW1CWTtZQUN0RSxJQUFJLENBQUM3QixJQUFJSyxFQUFFLEVBQUU7Z0JBQ1hmLFFBQVFELEtBQUssQ0FBQztnQkFDZCxPQUFPLEVBQUU7WUFDWDtZQUVBLE1BQU1SLE9BQU8sTUFBTW1CLElBQUlPLElBQUk7WUFDM0IsT0FBTzFCLEtBQUtHLEdBQUcsQ0FBQyxDQUFDVSxJQUFZO29CQUMzQixHQUFHQSxDQUFDO29CQUNKQyxXQUFXLElBQUlSLEtBQUtPLEVBQUU4QyxVQUFVO29CQUNoQzVDLFVBQVVGLEVBQUUrQyxTQUFTLEdBQUcsSUFBSXRELEtBQUtPLEVBQUUrQyxTQUFTLElBQUk1QztvQkFDaERYLFdBQVcsSUFBSUMsS0FBS08sRUFBRWlCLFVBQVUsSUFBSWpCLEVBQUU4QyxVQUFVO29CQUNoRHBELFdBQVcsSUFBSUQsS0FBS08sRUFBRWtCLFVBQVUsSUFBSWxCLEVBQUU4QyxVQUFVO2dCQUNsRDtRQUNGLEVBQUUsT0FBT25ELE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLG1DQUFtQ0E7WUFDakQsT0FBTyxFQUFFO1FBQ1g7SUFDRjtJQUVBLE1BQU0wRSxnQkFBOEM7UUFDbEQsSUFBSTtZQUNGLGdCQUFnQjtZQUNoQixNQUFNL0QsTUFBTSxNQUFNQyxNQUFNO1lBQ3hCLElBQUlELElBQUlLLEVBQUUsRUFBRTtnQkFDVixNQUFNeEIsT0FBTyxNQUFNbUIsSUFBSU8sSUFBSTtnQkFDM0JqQixRQUFRbUIsR0FBRyxDQUFDLDhCQUE4QjVCLEtBQUs2QixNQUFNO2dCQUNyRCxPQUFPN0IsS0FBS0csR0FBRyxDQUFDLENBQUNVLElBQVk7d0JBQzNCLEdBQUdBLENBQUM7d0JBQ0pDLFdBQVcsSUFBSVIsS0FBS08sRUFBRThDLFVBQVU7d0JBQ2hDNUMsVUFBVUYsRUFBRStDLFNBQVMsR0FBRyxJQUFJdEQsS0FBS08sRUFBRStDLFNBQVMsSUFBSTVDO3dCQUNoRFgsV0FBVyxJQUFJQyxLQUFLTyxFQUFFaUIsVUFBVSxJQUFJakIsRUFBRThDLFVBQVU7d0JBQ2hEcEQsV0FBVyxJQUFJRCxLQUFLTyxFQUFFa0IsVUFBVSxJQUFJbEIsRUFBRThDLFVBQVU7b0JBQ2xEO1lBQ0YsT0FBTztnQkFDTCxNQUFNLElBQUlsQyxNQUFNO1lBQ2xCO1FBQ0YsRUFBRSxPQUFPakIsT0FBTztZQUNkQyxRQUFRdUIsSUFBSSxDQUFDO1lBQ2IsMkJBQTJCO1lBQzNCLE1BQU1tRCxlQUFlLElBQUksQ0FBQ3ZFLG9CQUFvQjtZQUM5Q0gsUUFBUW1CLEdBQUcsQ0FBQyx1Q0FBdUN1RCxhQUFhdEQsTUFBTTtZQUN0RSxPQUFPc0Q7UUFDVDtJQUNGO0lBRUEsTUFBTUMsa0JBQWdEO1FBQ3BELElBQUk7WUFDRix1Q0FBdUM7WUFDdkMsTUFBTUMsYUFBYSxNQUFNLElBQUksQ0FBQ0gsYUFBYTtZQUMzQyxNQUFNbkIsUUFBUSxJQUFJekQsT0FBT2dGLFlBQVk7WUFDckMsT0FBT0QsV0FBV0UsTUFBTSxDQUFDLENBQUMxRSxJQUFNQSxFQUFFQyxTQUFTLENBQUN3RSxZQUFZLE9BQU92QjtRQUNqRSxFQUFFLE9BQU92RCxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxpQ0FBaUNBO1lBQy9DLE9BQU8sRUFBRTtRQUNYO0lBQ0Y7SUFFQSx1QkFBdUI7SUFDdkIsTUFBTWdGLGtCQUFrQkMsUUFBZ0IsRUFBRUMsUUFBZ0IsRUFBb0I7UUFDNUUsSUFBSSxJQUFJLENBQUNwRyxtQkFBbUIsTUFBTUosK0NBQVFBLEVBQUU7WUFDMUMsZUFBZTtZQUNmLE1BQU0sRUFBRWMsSUFBSSxFQUFFUSxLQUFLLEVBQUUsR0FBRyxNQUFNdEIsK0NBQVFBLENBQUN5RyxJQUFJLENBQUMsZUFBZUMsTUFBTSxDQUFDLEtBQUtDLEVBQUUsQ0FBQyxZQUFZSixVQUFVSyxNQUFNO1lBRXRHLElBQUl0RixTQUFTLENBQUNSLE1BQU07Z0JBQ2xCLE9BQU87WUFDVDtZQUVBLDREQUE0RDtZQUM1RCxPQUFPMEYsYUFBYTtRQUN0QixPQUFPO1lBQ0wsbUNBQW1DO1lBQ25DLE9BQU9ELGFBQWEsV0FBV0MsYUFBYTtRQUM5QztJQUNGO0lBRUEsb0JBQW9CO0lBQ3BCSyw0QkFBb0M7UUFDbEMsTUFBTUMsT0FBTyxJQUFJMUYsT0FBTzJGLFdBQVc7UUFDbkMsTUFBTXhCLFNBQVNELEtBQUswQixLQUFLLENBQUMxQixLQUFLQyxNQUFNLEtBQUssT0FDdkNDLFFBQVEsR0FDUnlCLFFBQVEsQ0FBQyxHQUFHO1FBQ2YsT0FBTyxNQUFhMUIsT0FBUHVCLE1BQWMsT0FBUHZCO0lBQ3RCO0lBRVEyQixvQkFBb0JsRixPQUFnQixFQUFvQjtRQUM5RCxPQUFPO1lBQ0wsR0FBR0EsT0FBTztZQUNWYixXQUFXLElBQUlDLEtBQUtZLFFBQVFZLFVBQVU7WUFDdEN2QixXQUFXLElBQUlELEtBQUtZLFFBQVFhLFVBQVU7UUFDeEM7SUFDRjtJQUVRc0UscUJBQXFCaEMsS0FBZSxFQUFxQjtRQUMvRCxPQUFPO1lBQ0wsR0FBR0EsS0FBSztZQUNSdkQsV0FBVyxJQUFJUixLQUFLK0QsTUFBTVYsVUFBVTtZQUNwQzVDLFVBQVVzRCxNQUFNVCxTQUFTLEdBQUcsSUFBSXRELEtBQUsrRCxNQUFNVCxTQUFTLElBQUk1QztZQUN4RFgsV0FBVyxJQUFJQyxLQUFLK0QsTUFBTXZDLFVBQVU7UUFDdEM7SUFDRjtJQUVBLHFDQUFxQztJQUNyQ3dFLGlCQUF1QjtRQUNyQixJQUFJLElBQUksQ0FBQy9HLHVCQUF1QixJQUFJO1lBQ2xDRSxhQUFhOEcsVUFBVSxDQUFDcEg7WUFDeEJNLGFBQWE4RyxVQUFVLENBQUNuSDtRQUMxQjtJQUNGO0lBRUEsbUJBQW1CO0lBQ25Cb0gsaUJBQWdGO1FBQ2hGLE9BQU87WUFDTEMsTUFBTTtZQUNOQyxlQUFlO1lBQ2ZDLGNBQWM7UUFDaEI7SUFDRjtBQUNBO0FBRU8sTUFBTUMsVUFBVSxJQUFJdkgsZ0JBQWUiLCJzb3VyY2VzIjpbIkQ6XFxpZGNhcmRcXGNhcmRzdGF0aW9uXFxsaWJcXGRhdGFiYXNlLXN0b3JlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCB7IHN1cGFiYXNlIH0gZnJvbSBcIi4vc3VwYWJhc2VcIlxuaW1wb3J0IHR5cGUgeyBEYXRhYmFzZSB9IGZyb20gXCIuL3N1cGFiYXNlXCJcbmltcG9ydCBjbGllbnRQcm9taXNlIGZyb20gXCIuL21vbmdvZGJcIlxuXG50eXBlIFN0dWRlbnQgPSBEYXRhYmFzZVtcInB1YmxpY1wiXVtcIlRhYmxlc1wiXVtcInN0dWRlbnRzXCJdW1wiUm93XCJdXG50eXBlIFN0dWRlbnRJbnNlcnQgPSBEYXRhYmFzZVtcInB1YmxpY1wiXVtcIlRhYmxlc1wiXVtcInN0dWRlbnRzXCJdW1wiSW5zZXJ0XCJdXG50eXBlIFN0dWRlbnRVcGRhdGUgPSBEYXRhYmFzZVtcInB1YmxpY1wiXVtcIlRhYmxlc1wiXVtcInN0dWRlbnRzXCJdW1wiVXBkYXRlXCJdXG50eXBlIEVudHJ5TG9nID0gRGF0YWJhc2VbXCJwdWJsaWNcIl1bXCJUYWJsZXNcIl1bXCJlbnRyeV9sb2dzXCJdW1wiUm93XCJdXG50eXBlIEVudHJ5TG9nSW5zZXJ0ID0gRGF0YWJhc2VbXCJwdWJsaWNcIl1bXCJUYWJsZXNcIl1bXCJlbnRyeV9sb2dzXCJdW1wiSW5zZXJ0XCJdXG5cbmV4cG9ydCBpbnRlcmZhY2UgU3R1ZGVudFdpdGhEYXRlcyBleHRlbmRzIE9taXQ8U3R1ZGVudCwgXCJjcmVhdGVkX2F0XCIgfCBcInVwZGF0ZWRfYXRcIj4ge1xuICBjcmVhdGVkQXQ6IERhdGVcbiAgdXBkYXRlZEF0OiBEYXRlXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgRW50cnlMb2dXaXRoRGF0ZXMgZXh0ZW5kcyBPbWl0PEVudHJ5TG9nLCBcImVudHJ5X3RpbWVcIiB8IFwiZXhpdF90aW1lXCIgfCBcImNyZWF0ZWRfYXRcIj4ge1xuICBlbnRyeVRpbWU6IERhdGVcbiAgZXhpdFRpbWU/OiBEYXRlXG4gIGNyZWF0ZWRBdDogRGF0ZVxufVxuXG4vLyBMb2NhbCBzdG9yYWdlIGtleXNcbmNvbnN0IFNUVURFTlRTX0tFWSA9IFwic21hcnRfaWRfc3R1ZGVudHNcIlxuY29uc3QgRU5UUklFU19LRVkgPSBcInNtYXJ0X2lkX2VudHJpZXNcIlxuXG5jbGFzcyBEYXRhYmFzZVN0b3JlIHtcbiAgcHJpdmF0ZSBpc1N1cGFiYXNlQXZhaWxhYmxlKCk6IGJvb2xlYW4ge1xuICAgIHJldHVybiBzdXBhYmFzZSAhPT0gbnVsbCAmJiB0eXBlb2Ygd2luZG93ICE9PSBcInVuZGVmaW5lZFwiXG4gIH1cblxuICBwcml2YXRlIGlzTG9jYWxTdG9yYWdlQXZhaWxhYmxlKCk6IGJvb2xlYW4ge1xuICAgIHJldHVybiB0eXBlb2Ygd2luZG93ICE9PSBcInVuZGVmaW5lZFwiICYmIHR5cGVvZiB3aW5kb3cubG9jYWxTdG9yYWdlICE9PSBcInVuZGVmaW5lZFwiXG4gIH1cblxuICAvLyBMb2NhbCBTdG9yYWdlIE1ldGhvZHNcbiAgcHJpdmF0ZSBzYXZlU3R1ZGVudHNUb0xvY2FsKHN0dWRlbnRzOiBTdHVkZW50V2l0aERhdGVzW10pOiB2b2lkIHtcbiAgICBpZiAodGhpcy5pc0xvY2FsU3RvcmFnZUF2YWlsYWJsZSgpKSB7XG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShTVFVERU5UU19LRVksIEpTT04uc3RyaW5naWZ5KHN0dWRlbnRzKSlcbiAgICB9XG4gIH1cblxuICBwcml2YXRlIGxvYWRTdHVkZW50c0Zyb21Mb2NhbCgpOiBTdHVkZW50V2l0aERhdGVzW10ge1xuICAgIGlmICghdGhpcy5pc0xvY2FsU3RvcmFnZUF2YWlsYWJsZSgpKSByZXR1cm4gW11cblxuICAgIHRyeSB7XG4gICAgICBjb25zdCBkYXRhID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oU1RVREVOVFNfS0VZKVxuICAgICAgaWYgKCFkYXRhKSByZXR1cm4gW11cblxuICAgICAgY29uc3Qgc3R1ZGVudHMgPSBKU09OLnBhcnNlKGRhdGEpXG4gICAgICByZXR1cm4gc3R1ZGVudHMubWFwKChzOiBhbnkpID0+ICh7XG4gICAgICAgIC4uLnMsXG4gICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUocy5jcmVhdGVkQXQpLFxuICAgICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKHMudXBkYXRlZEF0KSxcbiAgICAgIH0pKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgbG9hZGluZyBzdHVkZW50cyBmcm9tIGxvY2FsU3RvcmFnZTpcIiwgZXJyb3IpXG4gICAgICByZXR1cm4gW11cbiAgICB9XG4gIH1cblxuICBwcml2YXRlIHNhdmVFbnRyaWVzVG9Mb2NhbChlbnRyaWVzOiBFbnRyeUxvZ1dpdGhEYXRlc1tdKTogdm9pZCB7XG4gICAgaWYgKHRoaXMuaXNMb2NhbFN0b3JhZ2VBdmFpbGFibGUoKSkge1xuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oRU5UUklFU19LRVksIEpTT04uc3RyaW5naWZ5KGVudHJpZXMpKVxuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgbG9hZEVudHJpZXNGcm9tTG9jYWwoKTogRW50cnlMb2dXaXRoRGF0ZXNbXSB7XG4gICAgaWYgKCF0aGlzLmlzTG9jYWxTdG9yYWdlQXZhaWxhYmxlKCkpIHJldHVybiBbXVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGRhdGEgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShFTlRSSUVTX0tFWSlcbiAgICAgIGlmICghZGF0YSkgcmV0dXJuIFtdXG5cbiAgICAgIGNvbnN0IGVudHJpZXMgPSBKU09OLnBhcnNlKGRhdGEpXG4gICAgICByZXR1cm4gZW50cmllcy5tYXAoKGU6IGFueSkgPT4gKHtcbiAgICAgICAgLi4uZSxcbiAgICAgICAgZW50cnlUaW1lOiBuZXcgRGF0ZShlLmVudHJ5VGltZSksXG4gICAgICAgIGV4aXRUaW1lOiBlLmV4aXRUaW1lID8gbmV3IERhdGUoZS5leGl0VGltZSkgOiB1bmRlZmluZWQsXG4gICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoZS5jcmVhdGVkQXQpLFxuICAgICAgfSkpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBsb2FkaW5nIGVudHJpZXMgZnJvbSBsb2NhbFN0b3JhZ2U6XCIsIGVycm9yKVxuICAgICAgcmV0dXJuIFtdXG4gICAgfVxuICB9XG5cbiAgLy8gU3R1ZGVudCBNYW5hZ2VtZW50XG4gIGFzeW5jIGFkZFN0dWRlbnQoc3R1ZGVudDogT21pdDxTdHVkZW50SW5zZXJ0LCBcImlkXCIgfCBcImNyZWF0ZWRfYXRcIiB8IFwidXBkYXRlZF9hdFwiPik6IFByb21pc2U8U3R1ZGVudFdpdGhEYXRlcz4ge1xuICAgIGNvbnN0IHJlcyA9IGF3YWl0IGZldGNoKFwiL2FwaS9zdHVkZW50c1wiLCB7XG4gICAgICBtZXRob2Q6IFwiUE9TVFwiLFxuICAgICAgaGVhZGVyczogeyBcIkNvbnRlbnQtVHlwZVwiOiBcImFwcGxpY2F0aW9uL2pzb25cIiB9LFxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoc3R1ZGVudCksXG4gICAgfSk7XG4gICAgaWYgKCFyZXMub2spIHRocm93IG5ldyBFcnJvcihcIkZhaWxlZCB0byBhZGQgc3R1ZGVudFwiKTtcbiAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzLmpzb24oKTtcbiAgICByZXR1cm4ge1xuICAgICAgLi4uZGF0YSxcbiAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoZGF0YS5jcmVhdGVkQXQpLFxuICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZShkYXRhLnVwZGF0ZWRBdCksXG4gICAgfTtcbiAgfVxuXG4gIGFzeW5jIGdldFN0dWRlbnRzKCk6IFByb21pc2U8U3R1ZGVudFdpdGhEYXRlc1tdPiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIFRyeSBBUEkgZmlyc3RcbiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGZldGNoKFwiL2FwaS9zdHVkZW50c1wiKTtcbiAgICAgIGlmIChyZXMub2spIHtcbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlcy5qc29uKCk7XG4gICAgICAgIGNvbnNvbGUubG9nKFwi4pyFIFN0dWRlbnRzIGxvYWRlZCBmcm9tIEFQSTpcIiwgZGF0YS5sZW5ndGgpO1xuICAgICAgICByZXR1cm4gZGF0YS5tYXAoKHM6IGFueSkgPT4gKHtcbiAgICAgICAgICAuLi5zLFxuICAgICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUocy5jcmVhdGVkQXQgfHwgcy5jcmVhdGVkX2F0IHx8IG5ldyBEYXRlKCkpLFxuICAgICAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUocy51cGRhdGVkQXQgfHwgcy51cGRhdGVkX2F0IHx8IG5ldyBEYXRlKCkpLFxuICAgICAgICB9KSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJBUEkgZmFpbGVkXCIpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLndhcm4oXCLimqDvuI8gU3R1ZGVudHMgQVBJIG5vdCBhdmFpbGFibGUsIHVzaW5nIGxvY2FsU3RvcmFnZSBmYWxsYmFja1wiKTtcbiAgICAgIC8vIEZhbGxiYWNrIHRvIGxvY2FsU3RvcmFnZVxuICAgICAgY29uc3QgbG9jYWxTdHVkZW50cyA9IHRoaXMubG9hZFN0dWRlbnRzRnJvbUxvY2FsKCk7XG4gICAgICBjb25zb2xlLmxvZyhcIuKchSBTdHVkZW50cyBsb2FkZWQgZnJvbSBsb2NhbFN0b3JhZ2U6XCIsIGxvY2FsU3R1ZGVudHMubGVuZ3RoKTtcbiAgICAgIHJldHVybiBsb2NhbFN0dWRlbnRzO1xuICAgIH1cbiAgfVxuXG4gIGFzeW5jIGdldFN0dWRlbnRCeUFwcE51bWJlcihhcHBOdW1iZXI6IHN0cmluZyk6IFByb21pc2U8U3R1ZGVudFdpdGhEYXRlcyB8IG51bGw+IHtcbiAgICB0cnkge1xuICAgICAgLy8gVHJ5IEFQSSBmaXJzdFxuICAgICAgY29uc3QgcmVzID0gYXdhaXQgZmV0Y2goYC9hcGkvc3R1ZGVudHM/YXBwbGljYXRpb25fbnVtYmVyPSR7ZW5jb2RlVVJJQ29tcG9uZW50KGFwcE51bWJlcil9YCk7XG4gICAgICBpZiAocmVzLm9rKSB7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXMuanNvbigpO1xuICAgICAgICBpZiAoIWRhdGEgfHwgZGF0YS5sZW5ndGggPT09IDApIHJldHVybiBudWxsO1xuICAgICAgICBjb25zdCBzID0gZGF0YVswXTtcbiAgICAgICAgY29uc29sZS5sb2coXCLinIUgU3R1ZGVudCBmb3VuZCB2aWEgQVBJOlwiLCBzLm5hbWUpO1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIC4uLnMsXG4gICAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZShzLmNyZWF0ZWRBdCB8fCBzLmNyZWF0ZWRfYXQgfHwgbmV3IERhdGUoKSksXG4gICAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZShzLnVwZGF0ZWRBdCB8fCBzLnVwZGF0ZWRfYXQgfHwgbmV3IERhdGUoKSksXG4gICAgICAgIH07XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJBUEkgZmFpbGVkXCIpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLndhcm4oXCLimqDvuI8gU3R1ZGVudCBBUEkgbm90IGF2YWlsYWJsZSwgdXNpbmcgbG9jYWxTdG9yYWdlIGZhbGxiYWNrXCIpO1xuICAgICAgLy8gRmFsbGJhY2sgdG8gbG9jYWxTdG9yYWdlXG4gICAgICBjb25zdCBsb2NhbFN0dWRlbnRzID0gdGhpcy5sb2FkU3R1ZGVudHNGcm9tTG9jYWwoKTtcbiAgICAgIGNvbnN0IHN0dWRlbnQgPSBsb2NhbFN0dWRlbnRzLmZpbmQocyA9PiBzLmFwcGxpY2F0aW9uX251bWJlciA9PT0gYXBwTnVtYmVyKTtcbiAgICAgIGlmIChzdHVkZW50KSB7XG4gICAgICAgIGNvbnNvbGUubG9nKFwi4pyFIFN0dWRlbnQgZm91bmQgdmlhIGxvY2FsU3RvcmFnZTpcIiwgc3R1ZGVudC5uYW1lKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUubG9nKFwi4p2MIFN0dWRlbnQgbm90IGZvdW5kIGluIGxvY2FsU3RvcmFnZVwiKTtcbiAgICAgIH1cbiAgICAgIHJldHVybiBzdHVkZW50IHx8IG51bGw7XG4gICAgfVxuICB9XG5cbiAgYXN5bmMgZ2V0U3R1ZGVudEJ5QXBwQW5kUGhvbmUoYXBwTnVtYmVyOiBzdHJpbmcsIHBob25lOiBzdHJpbmcpOiBQcm9taXNlPFN0dWRlbnRXaXRoRGF0ZXMgfCBudWxsPiB7XG4gICAgY29uc3QgdXJsID0gYC9hcGkvc3R1ZGVudHM/YXBwbGljYXRpb25fbnVtYmVyPSR7ZW5jb2RlVVJJQ29tcG9uZW50KGFwcE51bWJlcil9JnBob25lPSR7ZW5jb2RlVVJJQ29tcG9uZW50KHBob25lKX1gO1xuICAgIGNvbnN0IHJlcyA9IGF3YWl0IGZldGNoKHVybCk7XG4gICAgaWYgKCFyZXMub2spIHJldHVybiBudWxsO1xuICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXMuanNvbigpO1xuICAgIGlmICghZGF0YSB8fCBkYXRhLmxlbmd0aCA9PT0gMCkgcmV0dXJuIG51bGw7XG4gICAgY29uc3QgcyA9IGRhdGFbMF07XG4gICAgcmV0dXJuIHtcbiAgICAgIC4uLnMsXG4gICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKHMuY3JlYXRlZEF0KSxcbiAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUocy51cGRhdGVkQXQpLFxuICAgIH07XG4gIH1cblxuICBhc3luYyB1cGRhdGVTdHVkZW50KGlkOiBzdHJpbmcsIHVwZGF0ZXM6IFN0dWRlbnRVcGRhdGUpOiBQcm9taXNlPFN0dWRlbnRXaXRoRGF0ZXMgfCBudWxsPiB7XG4gICAgY29uc3QgcmVzID0gYXdhaXQgZmV0Y2goXCIvYXBpL3N0dWRlbnRzXCIsIHtcbiAgICAgIG1ldGhvZDogXCJQVVRcIixcbiAgICAgIGhlYWRlcnM6IHsgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIgfSxcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgaWQsIC4uLnVwZGF0ZXMgfSksXG4gICAgfSk7XG4gICAgaWYgKCFyZXMub2spIHRocm93IG5ldyBFcnJvcihcIkZhaWxlZCB0byB1cGRhdGUgc3R1ZGVudFwiKTtcbiAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzLmpzb24oKTtcbiAgICByZXR1cm4ge1xuICAgICAgLi4uZGF0YSxcbiAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoZGF0YS5jcmVhdGVkQXQpLFxuICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZShkYXRhLnVwZGF0ZWRBdCksXG4gICAgfTtcbiAgfVxuXG4gIGFzeW5jIGRlbGV0ZVN0dWRlbnQoaWQ6IHN0cmluZyk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICAgIGNvbnN0IHJlcyA9IGF3YWl0IGZldGNoKFwiL2FwaS9zdHVkZW50c1wiLCB7XG4gICAgICBtZXRob2Q6IFwiREVMRVRFXCIsXG4gICAgICBoZWFkZXJzOiB7IFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiIH0sXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7IGlkIH0pLFxuICAgIH0pO1xuICAgIGlmICghcmVzLm9rKSB0aHJvdyBuZXcgRXJyb3IoXCJGYWlsZWQgdG8gZGVsZXRlIHN0dWRlbnRcIik7XG4gICAgcmV0dXJuIHRydWU7XG4gIH1cblxuICAvLyBFbnRyeSBMb2cgTWFuYWdlbWVudCAtIFVzaW5nIEFQSSByb3V0ZSBmb3IgYmV0dGVyIHJlbGlhYmlsaXR5XG4gIGFzeW5jIGFkZEVudHJ5KHN0dWRlbnRJZDogc3RyaW5nLCBhcHBsaWNhdGlvbk51bWJlcjogc3RyaW5nLCBzdHVkZW50TmFtZTogc3RyaW5nKTogUHJvbWlzZTxFbnRyeUxvZ1dpdGhEYXRlcz4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBlbnRyeURhdGEgPSB7XG4gICAgICAgIHN0dWRlbnRfaWQ6IHN0dWRlbnRJZCxcbiAgICAgICAgYXBwbGljYXRpb25fbnVtYmVyOiBhcHBsaWNhdGlvbk51bWJlcixcbiAgICAgICAgc3R1ZGVudF9uYW1lOiBzdHVkZW50TmFtZSxcbiAgICAgICAgdmVyaWZpY2F0aW9uX21ldGhvZDogXCJxcl9hbmRfZmFjZVwiLFxuICAgICAgICBxcl92YWxpZGF0ZWQ6IHRydWUsXG4gICAgICAgIHZlcmlmaWNhdGlvbl90aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgc3RhdGlvbl9pZDogXCJtYWluX2VudHJhbmNlXCJcbiAgICAgIH07XG5cbiAgICAgIGNvbnNvbGUubG9nKFwiU2VuZGluZyBlbnRyeSBkYXRhIHRvIEFQSTpcIiwgZW50cnlEYXRhKTtcblxuICAgICAgdHJ5IHtcbiAgICAgICAgLy8gVHJ5IEFQSSBmaXJzdFxuICAgICAgICBjb25zdCByZXMgPSBhd2FpdCBmZXRjaCgnL2FwaS9lbnRyaWVzJywge1xuICAgICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgICAgfSxcbiAgICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShlbnRyeURhdGEpLFxuICAgICAgICB9KTtcblxuICAgICAgICBpZiAocmVzLm9rKSB7XG4gICAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlcy5qc29uKCk7XG4gICAgICAgICAgY29uc29sZS5sb2coXCLinIUgRW50cnkgcmVjb3JkZWQgdmlhIEFQSTpcIiwgZGF0YSk7XG4gICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIC4uLmRhdGEsXG4gICAgICAgICAgICBlbnRyeVRpbWU6IG5ldyBEYXRlKGRhdGEuZW50cnlfdGltZSksXG4gICAgICAgICAgICBleGl0VGltZTogZGF0YS5leGl0X3RpbWUgPyBuZXcgRGF0ZShkYXRhLmV4aXRfdGltZSkgOiB1bmRlZmluZWQsXG4gICAgICAgICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKGRhdGEuY3JlYXRlZF9hdCB8fCBkYXRhLmVudHJ5X3RpbWUpLFxuICAgICAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZShkYXRhLnVwZGF0ZWRfYXQgfHwgZGF0YS5lbnRyeV90aW1lKVxuICAgICAgICAgIH07XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiQVBJIGZhaWxlZFwiKTtcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoYXBpRXJyb3IpIHtcbiAgICAgICAgY29uc29sZS53YXJuKFwi4pqg77iPIEFQSSBub3QgYXZhaWxhYmxlLCB1c2luZyBsb2NhbFN0b3JhZ2UgZmFsbGJhY2tcIik7XG5cbiAgICAgICAgLy8gRmFsbGJhY2sgdG8gbG9jYWxTdG9yYWdlXG4gICAgICAgIGNvbnN0IGV4aXN0aW5nRW50cmllcyA9IHRoaXMubG9hZEVudHJpZXNGcm9tTG9jYWwoKTtcblxuICAgICAgICAvLyBDaGVjayBpZiBzdHVkZW50IGFscmVhZHkgaGFzIGVudHJ5IHRvZGF5IHdpdGhvdXQgZXhpdFxuICAgICAgICBjb25zdCB0b2RheSA9IG5ldyBEYXRlKCk7XG4gICAgICAgIHRvZGF5LnNldEhvdXJzKDAsIDAsIDAsIDApO1xuICAgICAgICBjb25zdCB0b21vcnJvdyA9IG5ldyBEYXRlKHRvZGF5KTtcbiAgICAgICAgdG9tb3Jyb3cuc2V0RGF0ZSh0b21vcnJvdy5nZXREYXRlKCkgKyAxKTtcblxuICAgICAgICBjb25zdCB0b2RheUVudHJ5ID0gZXhpc3RpbmdFbnRyaWVzLmZpbmQoZW50cnkgPT5cbiAgICAgICAgICBlbnRyeS5zdHVkZW50X2lkID09PSBzdHVkZW50SWQgJiZcbiAgICAgICAgICBlbnRyeS5lbnRyeVRpbWUgPj0gdG9kYXkgJiZcbiAgICAgICAgICBlbnRyeS5lbnRyeVRpbWUgPCB0b21vcnJvdyAmJlxuICAgICAgICAgICFlbnRyeS5leGl0VGltZVxuICAgICAgICApO1xuXG4gICAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7XG4gICAgICAgIGNvbnN0IGVudHJ5SWQgPSBgZW50cnlfJHtEYXRlLm5vdygpfV8ke01hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cmluZygyLCA5KX1gO1xuXG4gICAgICAgIGxldCBuZXdFbnRyeTogRW50cnlMb2dXaXRoRGF0ZXM7XG5cbiAgICAgICAgaWYgKHRvZGF5RW50cnkpIHtcbiAgICAgICAgICAvLyBUaGlzIGlzIGFuIGV4aXRcbiAgICAgICAgICB0b2RheUVudHJ5LmV4aXRUaW1lID0gbm93O1xuICAgICAgICAgIHRvZGF5RW50cnkuc3RhdHVzID0gXCJleGl0XCI7XG4gICAgICAgICAgdG9kYXlFbnRyeS51cGRhdGVkQXQgPSBub3c7XG5cbiAgICAgICAgICAvLyBVcGRhdGUgZXhpc3RpbmcgZW50cnlcbiAgICAgICAgICBjb25zdCB1cGRhdGVkRW50cmllcyA9IGV4aXN0aW5nRW50cmllcy5tYXAoZW50cnkgPT5cbiAgICAgICAgICAgIGVudHJ5LmlkID09PSB0b2RheUVudHJ5LmlkID8gdG9kYXlFbnRyeSA6IGVudHJ5XG4gICAgICAgICAgKTtcbiAgICAgICAgICB0aGlzLnNhdmVFbnRyaWVzVG9Mb2NhbCh1cGRhdGVkRW50cmllcyk7XG5cbiAgICAgICAgICBuZXdFbnRyeSA9IHRvZGF5RW50cnk7XG4gICAgICAgICAgY29uc29sZS5sb2coXCLinIUgRVhJVCByZWNvcmRlZCB2aWEgbG9jYWxTdG9yYWdlOlwiLCBuZXdFbnRyeSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgLy8gVGhpcyBpcyBhbiBlbnRyeVxuICAgICAgICAgIG5ld0VudHJ5ID0ge1xuICAgICAgICAgICAgaWQ6IGVudHJ5SWQsXG4gICAgICAgICAgICBzdHVkZW50X2lkOiBzdHVkZW50SWQsXG4gICAgICAgICAgICBhcHBsaWNhdGlvbl9udW1iZXI6IGFwcGxpY2F0aW9uTnVtYmVyLFxuICAgICAgICAgICAgc3R1ZGVudF9uYW1lOiBzdHVkZW50TmFtZSxcbiAgICAgICAgICAgIGVudHJ5VGltZTogbm93LFxuICAgICAgICAgICAgZXhpdFRpbWU6IHVuZGVmaW5lZCxcbiAgICAgICAgICAgIHN0YXR1czogXCJlbnRyeVwiLFxuICAgICAgICAgICAgdmVyaWZpZWQ6IHRydWUsXG4gICAgICAgICAgICBjcmVhdGVkQXQ6IG5vdyxcbiAgICAgICAgICAgIHVwZGF0ZWRBdDogbm93XG4gICAgICAgICAgfSBhcyBFbnRyeUxvZ1dpdGhEYXRlcztcblxuICAgICAgICAgIGV4aXN0aW5nRW50cmllcy5wdXNoKG5ld0VudHJ5KTtcbiAgICAgICAgICB0aGlzLnNhdmVFbnRyaWVzVG9Mb2NhbChleGlzdGluZ0VudHJpZXMpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKFwi4pyFIEVOVFJZIHJlY29yZGVkIHZpYSBsb2NhbFN0b3JhZ2U6XCIsIG5ld0VudHJ5KTtcbiAgICAgICAgfVxuXG4gICAgICAgIHJldHVybiBuZXdFbnRyeTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihcIuKdjCBFcnJvciBhZGRpbmcgZW50cnk6XCIsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfVxuXG5cbiAgYXN5bmMgZ2V0U3R1ZGVudEVudHJpZXMoc3R1ZGVudElkOiBzdHJpbmcpOiBQcm9taXNlPEVudHJ5TG9nV2l0aERhdGVzW10+IHtcbiAgICB0cnkge1xuICAgICAgLy8gVXNlIEFQSSByb3V0ZSB3aGljaCBoYW5kbGVzIGJvdGggTW9uZ29EQiBhbmQgZmFsbGJhY2tcbiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGZldGNoKGAvYXBpL2VudHJpZXM/c3R1ZGVudF9pZD0ke2VuY29kZVVSSUNvbXBvbmVudChzdHVkZW50SWQpfWApO1xuICAgICAgaWYgKCFyZXMub2spIHtcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkZhaWxlZCB0byBmZXRjaCBlbnRyaWVzIGZyb20gQVBJXCIpO1xuICAgICAgICByZXR1cm4gW107XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXMuanNvbigpO1xuICAgICAgcmV0dXJuIGRhdGEubWFwKChlOiBhbnkpID0+ICh7XG4gICAgICAgIC4uLmUsXG4gICAgICAgIGVudHJ5VGltZTogbmV3IERhdGUoZS5lbnRyeV90aW1lKSxcbiAgICAgICAgZXhpdFRpbWU6IGUuZXhpdF90aW1lID8gbmV3IERhdGUoZS5leGl0X3RpbWUpIDogdW5kZWZpbmVkLFxuICAgICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKGUuY3JlYXRlZF9hdCB8fCBlLmVudHJ5X3RpbWUpLFxuICAgICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKGUudXBkYXRlZF9hdCB8fCBlLmVudHJ5X3RpbWUpXG4gICAgICB9KSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBmZXRjaGluZyBzdHVkZW50IGVudHJpZXM6XCIsIGVycm9yKTtcbiAgICAgIHJldHVybiBbXTtcbiAgICB9XG4gIH1cblxuICBhc3luYyBnZXRBbGxFbnRyaWVzKCk6IFByb21pc2U8RW50cnlMb2dXaXRoRGF0ZXNbXT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBUcnkgQVBJIGZpcnN0XG4gICAgICBjb25zdCByZXMgPSBhd2FpdCBmZXRjaCgnL2FwaS9lbnRyaWVzJyk7XG4gICAgICBpZiAocmVzLm9rKSB7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXMuanNvbigpO1xuICAgICAgICBjb25zb2xlLmxvZyhcIuKchSBFbnRyaWVzIGxvYWRlZCBmcm9tIEFQSTpcIiwgZGF0YS5sZW5ndGgpO1xuICAgICAgICByZXR1cm4gZGF0YS5tYXAoKGU6IGFueSkgPT4gKHtcbiAgICAgICAgICAuLi5lLFxuICAgICAgICAgIGVudHJ5VGltZTogbmV3IERhdGUoZS5lbnRyeV90aW1lKSxcbiAgICAgICAgICBleGl0VGltZTogZS5leGl0X3RpbWUgPyBuZXcgRGF0ZShlLmV4aXRfdGltZSkgOiB1bmRlZmluZWQsXG4gICAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZShlLmNyZWF0ZWRfYXQgfHwgZS5lbnRyeV90aW1lKSxcbiAgICAgICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKGUudXBkYXRlZF9hdCB8fCBlLmVudHJ5X3RpbWUpXG4gICAgICAgIH0pKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIkFQSSBmYWlsZWRcIik7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUud2FybihcIuKaoO+4jyBBUEkgbm90IGF2YWlsYWJsZSwgdXNpbmcgbG9jYWxTdG9yYWdlIGZhbGxiYWNrXCIpO1xuICAgICAgLy8gRmFsbGJhY2sgdG8gbG9jYWxTdG9yYWdlXG4gICAgICBjb25zdCBsb2NhbEVudHJpZXMgPSB0aGlzLmxvYWRFbnRyaWVzRnJvbUxvY2FsKCk7XG4gICAgICBjb25zb2xlLmxvZyhcIuKchSBFbnRyaWVzIGxvYWRlZCBmcm9tIGxvY2FsU3RvcmFnZTpcIiwgbG9jYWxFbnRyaWVzLmxlbmd0aCk7XG4gICAgICByZXR1cm4gbG9jYWxFbnRyaWVzO1xuICAgIH1cbiAgfVxuXG4gIGFzeW5jIGdldFRvZGF5RW50cmllcygpOiBQcm9taXNlPEVudHJ5TG9nV2l0aERhdGVzW10+IHtcbiAgICB0cnkge1xuICAgICAgLy8gR2V0IGFsbCBlbnRyaWVzIGFuZCBmaWx0ZXIgZm9yIHRvZGF5XG4gICAgICBjb25zdCBhbGxFbnRyaWVzID0gYXdhaXQgdGhpcy5nZXRBbGxFbnRyaWVzKCk7XG4gICAgICBjb25zdCB0b2RheSA9IG5ldyBEYXRlKCkudG9EYXRlU3RyaW5nKCk7XG4gICAgICByZXR1cm4gYWxsRW50cmllcy5maWx0ZXIoKGUpID0+IGUuZW50cnlUaW1lLnRvRGF0ZVN0cmluZygpID09PSB0b2RheSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBmZXRjaGluZyB0b2RheSBlbnRyaWVzOlwiLCBlcnJvcik7XG4gICAgICByZXR1cm4gW107XG4gICAgfVxuICB9XG5cbiAgLy8gQWRtaW4gQXV0aGVudGljYXRpb25cbiAgYXN5bmMgYXV0aGVudGljYXRlQWRtaW4odXNlcm5hbWU6IHN0cmluZywgcGFzc3dvcmQ6IHN0cmluZyk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICAgIGlmICh0aGlzLmlzU3VwYWJhc2VBdmFpbGFibGUoKSAmJiBzdXBhYmFzZSkge1xuICAgICAgLy8gVXNlIFN1cGFiYXNlXG4gICAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5mcm9tKFwiYWRtaW5fdXNlcnNcIikuc2VsZWN0KFwiKlwiKS5lcShcInVzZXJuYW1lXCIsIHVzZXJuYW1lKS5zaW5nbGUoKVxuXG4gICAgICBpZiAoZXJyb3IgfHwgIWRhdGEpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlXG4gICAgICB9XG5cbiAgICAgIC8vIFNpbXBsZSBwYXNzd29yZCBjaGVjayAoaW4gcHJvZHVjdGlvbiwgdXNlIHByb3BlciBoYXNoaW5nKVxuICAgICAgcmV0dXJuIHBhc3N3b3JkID09PSBcImFkbWluMTIzXCJcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gRmFsbGJhY2sgYXV0aGVudGljYXRpb24gZm9yIGRlbW9cbiAgICAgIHJldHVybiB1c2VybmFtZSA9PT0gXCJhZG1pblwiICYmIHBhc3N3b3JkID09PSBcImFkbWluMTIzXCJcbiAgICB9XG4gIH1cblxuICAvLyBVdGlsaXR5IGZ1bmN0aW9uc1xuICBnZW5lcmF0ZUFwcGxpY2F0aW9uTnVtYmVyKCk6IHN0cmluZyB7XG4gICAgY29uc3QgeWVhciA9IG5ldyBEYXRlKCkuZ2V0RnVsbFllYXIoKVxuICAgIGNvbnN0IHJhbmRvbSA9IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDEwMDAwKVxuICAgICAgLnRvU3RyaW5nKClcbiAgICAgIC5wYWRTdGFydCg0LCBcIjBcIilcbiAgICByZXR1cm4gYEFQUCR7eWVhcn0ke3JhbmRvbX1gXG4gIH1cblxuICBwcml2YXRlIGNvbnZlcnRTdHVkZW50RGF0ZXMoc3R1ZGVudDogU3R1ZGVudCk6IFN0dWRlbnRXaXRoRGF0ZXMge1xuICAgIHJldHVybiB7XG4gICAgICAuLi5zdHVkZW50LFxuICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZShzdHVkZW50LmNyZWF0ZWRfYXQpLFxuICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZShzdHVkZW50LnVwZGF0ZWRfYXQpLFxuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgY29udmVydEVudHJ5TG9nRGF0ZXMoZW50cnk6IEVudHJ5TG9nKTogRW50cnlMb2dXaXRoRGF0ZXMge1xuICAgIHJldHVybiB7XG4gICAgICAuLi5lbnRyeSxcbiAgICAgIGVudHJ5VGltZTogbmV3IERhdGUoZW50cnkuZW50cnlfdGltZSksXG4gICAgICBleGl0VGltZTogZW50cnkuZXhpdF90aW1lID8gbmV3IERhdGUoZW50cnkuZXhpdF90aW1lKSA6IHVuZGVmaW5lZCxcbiAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoZW50cnkuY3JlYXRlZF9hdCksXG4gICAgfVxuICB9XG5cbiAgLy8gQ2xlYXIgYWxsIGxvY2FsIGRhdGEgKGZvciB0ZXN0aW5nKVxuICBjbGVhckxvY2FsRGF0YSgpOiB2b2lkIHtcbiAgICBpZiAodGhpcy5pc0xvY2FsU3RvcmFnZUF2YWlsYWJsZSgpKSB7XG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShTVFVERU5UU19LRVkpXG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShFTlRSSUVTX0tFWSlcbiAgICB9XG4gIH1cblxuICAvLyBHZXQgc3RvcmFnZSBpbmZvXG4gIGdldFN0b3JhZ2VJbmZvKCk6IHsgbW9kZTogc3RyaW5nOyBzdHVkZW50c0NvdW50OiBudW1iZXI7IGVudHJpZXNDb3VudDogbnVtYmVyIH0ge1xuICByZXR1cm4ge1xuICAgIG1vZGU6IFwiQ2xvdWRcIixcbiAgICBzdHVkZW50c0NvdW50OiAwLFxuICAgIGVudHJpZXNDb3VudDogMCxcbiAgfVxufVxufVxuXG5leHBvcnQgY29uc3QgZGJTdG9yZSA9IG5ldyBEYXRhYmFzZVN0b3JlKClcbmV4cG9ydCB0eXBlIHsgU3R1ZGVudFdpdGhEYXRlcyBhcyBTdHVkZW50LCBFbnRyeUxvZ1dpdGhEYXRlcyBhcyBFbnRyeUxvZyB9XG4gIl0sIm5hbWVzIjpbInN1cGFiYXNlIiwiU1RVREVOVFNfS0VZIiwiRU5UUklFU19LRVkiLCJEYXRhYmFzZVN0b3JlIiwiaXNTdXBhYmFzZUF2YWlsYWJsZSIsImlzTG9jYWxTdG9yYWdlQXZhaWxhYmxlIiwid2luZG93IiwibG9jYWxTdG9yYWdlIiwic2F2ZVN0dWRlbnRzVG9Mb2NhbCIsInN0dWRlbnRzIiwic2V0SXRlbSIsIkpTT04iLCJzdHJpbmdpZnkiLCJsb2FkU3R1ZGVudHNGcm9tTG9jYWwiLCJkYXRhIiwiZ2V0SXRlbSIsInBhcnNlIiwibWFwIiwicyIsImNyZWF0ZWRBdCIsIkRhdGUiLCJ1cGRhdGVkQXQiLCJlcnJvciIsImNvbnNvbGUiLCJzYXZlRW50cmllc1RvTG9jYWwiLCJlbnRyaWVzIiwibG9hZEVudHJpZXNGcm9tTG9jYWwiLCJlIiwiZW50cnlUaW1lIiwiZXhpdFRpbWUiLCJ1bmRlZmluZWQiLCJhZGRTdHVkZW50Iiwic3R1ZGVudCIsInJlcyIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJvayIsIkVycm9yIiwianNvbiIsImdldFN0dWRlbnRzIiwibG9nIiwibGVuZ3RoIiwiY3JlYXRlZF9hdCIsInVwZGF0ZWRfYXQiLCJ3YXJuIiwibG9jYWxTdHVkZW50cyIsImdldFN0dWRlbnRCeUFwcE51bWJlciIsImFwcE51bWJlciIsImVuY29kZVVSSUNvbXBvbmVudCIsIm5hbWUiLCJmaW5kIiwiYXBwbGljYXRpb25fbnVtYmVyIiwiZ2V0U3R1ZGVudEJ5QXBwQW5kUGhvbmUiLCJwaG9uZSIsInVybCIsInVwZGF0ZVN0dWRlbnQiLCJpZCIsInVwZGF0ZXMiLCJkZWxldGVTdHVkZW50IiwiYWRkRW50cnkiLCJzdHVkZW50SWQiLCJhcHBsaWNhdGlvbk51bWJlciIsInN0dWRlbnROYW1lIiwiZW50cnlEYXRhIiwic3R1ZGVudF9pZCIsInN0dWRlbnRfbmFtZSIsInZlcmlmaWNhdGlvbl9tZXRob2QiLCJxcl92YWxpZGF0ZWQiLCJ2ZXJpZmljYXRpb25fdGltZXN0YW1wIiwidG9JU09TdHJpbmciLCJzdGF0aW9uX2lkIiwiZW50cnlfdGltZSIsImV4aXRfdGltZSIsImFwaUVycm9yIiwiZXhpc3RpbmdFbnRyaWVzIiwidG9kYXkiLCJzZXRIb3VycyIsInRvbW9ycm93Iiwic2V0RGF0ZSIsImdldERhdGUiLCJ0b2RheUVudHJ5IiwiZW50cnkiLCJub3ciLCJlbnRyeUlkIiwiTWF0aCIsInJhbmRvbSIsInRvU3RyaW5nIiwic3Vic3RyaW5nIiwibmV3RW50cnkiLCJzdGF0dXMiLCJ1cGRhdGVkRW50cmllcyIsInZlcmlmaWVkIiwicHVzaCIsImdldFN0dWRlbnRFbnRyaWVzIiwiZ2V0QWxsRW50cmllcyIsImxvY2FsRW50cmllcyIsImdldFRvZGF5RW50cmllcyIsImFsbEVudHJpZXMiLCJ0b0RhdGVTdHJpbmciLCJmaWx0ZXIiLCJhdXRoZW50aWNhdGVBZG1pbiIsInVzZXJuYW1lIiwicGFzc3dvcmQiLCJmcm9tIiwic2VsZWN0IiwiZXEiLCJzaW5nbGUiLCJnZW5lcmF0ZUFwcGxpY2F0aW9uTnVtYmVyIiwieWVhciIsImdldEZ1bGxZZWFyIiwiZmxvb3IiLCJwYWRTdGFydCIsImNvbnZlcnRTdHVkZW50RGF0ZXMiLCJjb252ZXJ0RW50cnlMb2dEYXRlcyIsImNsZWFyTG9jYWxEYXRhIiwicmVtb3ZlSXRlbSIsImdldFN0b3JhZ2VJbmZvIiwibW9kZSIsInN0dWRlbnRzQ291bnQiLCJlbnRyaWVzQ291bnQiLCJkYlN0b3JlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/database-store.ts\n"));

/***/ })

});