<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test - Entry Data</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .success {
            background: #28a745;
        }
        .error {
            background: #dc3545;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .entry-item {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .entry-header {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .entry-details {
            font-size: 14px;
            color: #666;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🧪 API Test - Entry Data Verification</h1>
    
    <div class="card">
        <h2>📡 API Endpoints Test</h2>
        <button class="button" onclick="testEntriesAPI()">Test /api/entries</button>
        <button class="button" onclick="testStudentsAPI()">Test /api/students</button>
        <button class="button" onclick="testSpecificStudent()">Test Specific Student</button>
        <div id="api-status" class="status">Ready to test APIs</div>
    </div>

    <div class="card">
        <h2>📊 Entries Data</h2>
        <div id="entries-count" class="status">No data loaded</div>
        <div id="entries-list"></div>
    </div>

    <div class="card">
        <h2>👥 Students Data</h2>
        <div id="students-count" class="status">No data loaded</div>
        <div id="students-list"></div>
    </div>

    <div class="card">
        <h2>🔍 Raw API Response</h2>
        <pre id="raw-response">No data loaded</pre>
    </div>

    <script>
        async function testEntriesAPI() {
            updateStatus('api-status', '🔄 Testing /api/entries...', '');
            
            try {
                const response = await fetch('/api/entries');
                const data = await response.json();
                
                console.log('Entries API Response:', data);
                
                if (response.ok) {
                    updateStatus('api-status', `✅ /api/entries working! Found ${data.length} entries`, 'success');
                    updateStatus('entries-count', `📊 Total Entries: ${data.length}`, 'success');
                    
                    // Display entries
                    displayEntries(data);
                    
                    // Show raw response
                    document.getElementById('raw-response').textContent = JSON.stringify(data, null, 2);
                } else {
                    updateStatus('api-status', `❌ /api/entries failed! Status: ${response.status}`, 'error');
                }
            } catch (error) {
                console.error('API Error:', error);
                updateStatus('api-status', `❌ /api/entries error: ${error.message}`, 'error');
            }
        }

        async function testStudentsAPI() {
            updateStatus('api-status', '🔄 Testing /api/students...', '');
            
            try {
                const response = await fetch('/api/students');
                const data = await response.json();
                
                console.log('Students API Response:', data);
                
                if (response.ok) {
                    updateStatus('api-status', `✅ /api/students working! Found ${data.length} students`, 'success');
                    updateStatus('students-count', `👥 Total Students: ${data.length}`, 'success');
                    
                    // Display students
                    displayStudents(data);
                } else {
                    updateStatus('api-status', `❌ /api/students failed! Status: ${response.status}`, 'error');
                }
            } catch (error) {
                console.error('API Error:', error);
                updateStatus('api-status', `❌ /api/students error: ${error.message}`, 'error');
            }
        }

        async function testSpecificStudent() {
            updateStatus('api-status', '🔄 Testing specific student entries...', '');
            
            try {
                const response = await fetch('/api/entries?student_id=STU_001');
                const data = await response.json();
                
                console.log('Specific Student Entries:', data);
                
                if (response.ok) {
                    updateStatus('api-status', `✅ Specific student entries! Found ${data.length} entries for STU_001`, 'success');
                    displayEntries(data);
                } else {
                    updateStatus('api-status', `❌ Specific student query failed! Status: ${response.status}`, 'error');
                }
            } catch (error) {
                console.error('API Error:', error);
                updateStatus('api-status', `❌ Specific student query error: ${error.message}`, 'error');
            }
        }

        function displayEntries(entries) {
            const container = document.getElementById('entries-list');
            if (entries.length === 0) {
                container.innerHTML = '<p>No entries found</p>';
                return;
            }
            
            let html = '';
            entries.forEach(entry => {
                html += `
                    <div class="entry-item">
                        <div class="entry-header">
                            ${entry.status === 'entry' ? '🟢 ENTRY' : '🔴 EXIT'} - ${entry.student_name}
                        </div>
                        <div class="entry-details">
                            <strong>App Number:</strong> ${entry.application_number}<br>
                            <strong>Entry Time:</strong> ${new Date(entry.entry_time).toLocaleString()}<br>
                            ${entry.exit_time ? `<strong>Exit Time:</strong> ${new Date(entry.exit_time).toLocaleString()}<br>` : ''}
                            <strong>Status:</strong> ${entry.status}<br>
                            <strong>Verified:</strong> ${entry.verified ? '✅ Yes' : '❌ No'}<br>
                            <strong>Method:</strong> ${entry.verification_method || 'N/A'}<br>
                            ${entry.face_match_score ? `<strong>Face Score:</strong> ${entry.face_match_score}%<br>` : ''}
                        </div>
                    </div>
                `;
            });
            container.innerHTML = html;
        }

        function displayStudents(students) {
            const container = document.getElementById('students-list');
            if (students.length === 0) {
                container.innerHTML = '<p>No students found</p>';
                return;
            }
            
            let html = '';
            students.forEach(student => {
                html += `
                    <div class="entry-item">
                        <div class="entry-header">
                            👤 ${student.name}
                        </div>
                        <div class="entry-details">
                            <strong>App Number:</strong> ${student.application_number}<br>
                            <strong>Phone:</strong> ${student.phone}<br>
                            <strong>Class:</strong> ${student.class}<br>
                            <strong>Department:</strong> ${student.department}<br>
                        </div>
                    </div>
                `;
            });
            container.innerHTML = html;
        }

        function updateStatus(elementId, message, type = '') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = 'status ' + type;
        }

        // Auto-test on page load
        window.onload = function() {
            setTimeout(() => {
                testEntriesAPI();
            }, 1000);
        };
    </script>
</body>
</html>
