"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/database-store.ts":
/*!*******************************!*\
  !*** ./lib/database-store.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dbStore: () => (/* binding */ dbStore)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ dbStore auto */ \n// Local storage keys\nconst STUDENTS_KEY = \"smart_id_students\";\nconst ENTRIES_KEY = \"smart_id_entries\";\nclass DatabaseStore {\n    isSupabaseAvailable() {\n        return _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase !== null && \"object\" !== \"undefined\";\n    }\n    isLocalStorageAvailable() {\n        return  true && typeof window.localStorage !== \"undefined\";\n    }\n    // Local Storage Methods\n    saveStudentsToLocal(students) {\n        if (this.isLocalStorageAvailable()) {\n            localStorage.setItem(STUDENTS_KEY, JSON.stringify(students));\n        }\n    }\n    loadStudentsFromLocal() {\n        if (!this.isLocalStorageAvailable()) return [];\n        try {\n            const data = localStorage.getItem(STUDENTS_KEY);\n            if (!data) return [];\n            const students = JSON.parse(data);\n            return students.map((s)=>({\n                    ...s,\n                    createdAt: new Date(s.createdAt),\n                    updatedAt: new Date(s.updatedAt)\n                }));\n        } catch (error) {\n            console.error(\"Error loading students from localStorage:\", error);\n            return [];\n        }\n    }\n    saveEntriesToLocal(entries) {\n        if (this.isLocalStorageAvailable()) {\n            localStorage.setItem(ENTRIES_KEY, JSON.stringify(entries));\n        }\n    }\n    loadEntriesFromLocal() {\n        if (!this.isLocalStorageAvailable()) return [];\n        try {\n            const data = localStorage.getItem(ENTRIES_KEY);\n            if (!data) return [];\n            const entries = JSON.parse(data);\n            return entries.map((e)=>({\n                    ...e,\n                    entryTime: new Date(e.entryTime),\n                    exitTime: e.exitTime ? new Date(e.exitTime) : undefined,\n                    createdAt: new Date(e.createdAt)\n                }));\n        } catch (error) {\n            console.error(\"Error loading entries from localStorage:\", error);\n            return [];\n        }\n    }\n    // Student Management\n    async addStudent(student) {\n        const res = await fetch(\"/api/students\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(student)\n        });\n        if (!res.ok) throw new Error(\"Failed to add student\");\n        const data = await res.json();\n        return {\n            ...data,\n            createdAt: new Date(data.createdAt),\n            updatedAt: new Date(data.updatedAt)\n        };\n    }\n    async getStudents() {\n        try {\n            // Try API first\n            const res = await fetch(\"/api/students\");\n            if (res.ok) {\n                const data = await res.json();\n                console.log(\"✅ Students loaded from API:\", data.length);\n                return data.map((s)=>({\n                        ...s,\n                        createdAt: new Date(s.createdAt || s.created_at || new Date()),\n                        updatedAt: new Date(s.updatedAt || s.updated_at || new Date())\n                    }));\n            } else {\n                throw new Error(\"API failed\");\n            }\n        } catch (error) {\n            console.warn(\"⚠️ Students API not available, using localStorage fallback\");\n            // Fallback to localStorage\n            const localStudents = this.loadStudentsFromLocal();\n            console.log(\"✅ Students loaded from localStorage:\", localStudents.length);\n            return localStudents;\n        }\n    }\n    async getStudentByAppNumber(appNumber) {\n        const res = await fetch(\"/api/students?application_number=\".concat(encodeURIComponent(appNumber)));\n        if (!res.ok) return null;\n        const data = await res.json();\n        if (!data || data.length === 0) return null;\n        const s = data[0];\n        return {\n            ...s,\n            createdAt: new Date(s.createdAt),\n            updatedAt: new Date(s.updatedAt)\n        };\n    }\n    async getStudentByAppAndPhone(appNumber, phone) {\n        const url = \"/api/students?application_number=\".concat(encodeURIComponent(appNumber), \"&phone=\").concat(encodeURIComponent(phone));\n        const res = await fetch(url);\n        if (!res.ok) return null;\n        const data = await res.json();\n        if (!data || data.length === 0) return null;\n        const s = data[0];\n        return {\n            ...s,\n            createdAt: new Date(s.createdAt),\n            updatedAt: new Date(s.updatedAt)\n        };\n    }\n    async updateStudent(id, updates) {\n        const res = await fetch(\"/api/students\", {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                id,\n                ...updates\n            })\n        });\n        if (!res.ok) throw new Error(\"Failed to update student\");\n        const data = await res.json();\n        return {\n            ...data,\n            createdAt: new Date(data.createdAt),\n            updatedAt: new Date(data.updatedAt)\n        };\n    }\n    async deleteStudent(id) {\n        const res = await fetch(\"/api/students\", {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                id\n            })\n        });\n        if (!res.ok) throw new Error(\"Failed to delete student\");\n        return true;\n    }\n    // Entry Log Management - Using API route for better reliability\n    async addEntry(studentId, applicationNumber, studentName) {\n        try {\n            const entryData = {\n                student_id: studentId,\n                application_number: applicationNumber,\n                student_name: studentName,\n                verification_method: \"qr_and_face\",\n                qr_validated: true,\n                verification_timestamp: new Date().toISOString(),\n                station_id: \"main_entrance\"\n            };\n            console.log(\"Sending entry data to API:\", entryData);\n            try {\n                // Try API first\n                const res = await fetch('/api/entries', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(entryData)\n                });\n                if (res.ok) {\n                    const data = await res.json();\n                    console.log(\"✅ Entry recorded via API:\", data);\n                    return {\n                        ...data,\n                        entryTime: new Date(data.entry_time),\n                        exitTime: data.exit_time ? new Date(data.exit_time) : undefined,\n                        createdAt: new Date(data.created_at || data.entry_time),\n                        updatedAt: new Date(data.updated_at || data.entry_time)\n                    };\n                } else {\n                    throw new Error(\"API failed\");\n                }\n            } catch (apiError) {\n                console.warn(\"⚠️ API not available, using localStorage fallback\");\n                // Fallback to localStorage\n                const existingEntries = this.loadEntriesFromLocal();\n                // Check if student already has entry today without exit\n                const today = new Date();\n                today.setHours(0, 0, 0, 0);\n                const tomorrow = new Date(today);\n                tomorrow.setDate(tomorrow.getDate() + 1);\n                const todayEntry = existingEntries.find((entry)=>entry.student_id === studentId && entry.entryTime >= today && entry.entryTime < tomorrow && !entry.exitTime);\n                const now = new Date();\n                const entryId = \"entry_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 9));\n                let newEntry;\n                if (todayEntry) {\n                    // This is an exit\n                    todayEntry.exitTime = now;\n                    todayEntry.status = \"exit\";\n                    todayEntry.updatedAt = now;\n                    // Update existing entry\n                    const updatedEntries = existingEntries.map((entry)=>entry.id === todayEntry.id ? todayEntry : entry);\n                    this.saveEntriesToLocal(updatedEntries);\n                    newEntry = todayEntry;\n                    console.log(\"✅ EXIT recorded via localStorage:\", newEntry);\n                } else {\n                    // This is an entry\n                    newEntry = {\n                        id: entryId,\n                        student_id: studentId,\n                        application_number: applicationNumber,\n                        student_name: studentName,\n                        entryTime: now,\n                        exitTime: undefined,\n                        status: \"entry\",\n                        verified: true,\n                        createdAt: now,\n                        updatedAt: now\n                    };\n                    existingEntries.push(newEntry);\n                    this.saveEntriesToLocal(existingEntries);\n                    console.log(\"✅ ENTRY recorded via localStorage:\", newEntry);\n                }\n                return newEntry;\n            }\n        } catch (error) {\n            console.error(\"❌ Error adding entry:\", error);\n            throw error;\n        }\n    }\n    async getStudentEntries(studentId) {\n        try {\n            // Use API route which handles both MongoDB and fallback\n            const res = await fetch(\"/api/entries?student_id=\".concat(encodeURIComponent(studentId)));\n            if (!res.ok) {\n                console.error(\"Failed to fetch entries from API\");\n                return [];\n            }\n            const data = await res.json();\n            return data.map((e)=>({\n                    ...e,\n                    entryTime: new Date(e.entry_time),\n                    exitTime: e.exit_time ? new Date(e.exit_time) : undefined,\n                    createdAt: new Date(e.created_at || e.entry_time),\n                    updatedAt: new Date(e.updated_at || e.entry_time)\n                }));\n        } catch (error) {\n            console.error(\"Error fetching student entries:\", error);\n            return [];\n        }\n    }\n    async getAllEntries() {\n        try {\n            // Try API first\n            const res = await fetch('/api/entries');\n            if (res.ok) {\n                const data = await res.json();\n                console.log(\"✅ Entries loaded from API:\", data.length);\n                return data.map((e)=>({\n                        ...e,\n                        entryTime: new Date(e.entry_time),\n                        exitTime: e.exit_time ? new Date(e.exit_time) : undefined,\n                        createdAt: new Date(e.created_at || e.entry_time),\n                        updatedAt: new Date(e.updated_at || e.entry_time)\n                    }));\n            } else {\n                throw new Error(\"API failed\");\n            }\n        } catch (error) {\n            console.warn(\"⚠️ API not available, using localStorage fallback\");\n            // Fallback to localStorage\n            const localEntries = this.loadEntriesFromLocal();\n            console.log(\"✅ Entries loaded from localStorage:\", localEntries.length);\n            return localEntries;\n        }\n    }\n    async getTodayEntries() {\n        try {\n            // Get all entries and filter for today\n            const allEntries = await this.getAllEntries();\n            const today = new Date().toDateString();\n            return allEntries.filter((e)=>e.entryTime.toDateString() === today);\n        } catch (error) {\n            console.error(\"Error fetching today entries:\", error);\n            return [];\n        }\n    }\n    // Admin Authentication\n    async authenticateAdmin(username, password) {\n        if (this.isSupabaseAvailable() && _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase) {\n            // Use Supabase\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"admin_users\").select(\"*\").eq(\"username\", username).single();\n            if (error || !data) {\n                return false;\n            }\n            // Simple password check (in production, use proper hashing)\n            return password === \"admin123\";\n        } else {\n            // Fallback authentication for demo\n            return username === \"admin\" && password === \"admin123\";\n        }\n    }\n    // Utility functions\n    generateApplicationNumber() {\n        const year = new Date().getFullYear();\n        const random = Math.floor(Math.random() * 10000).toString().padStart(4, \"0\");\n        return \"APP\".concat(year).concat(random);\n    }\n    convertStudentDates(student) {\n        return {\n            ...student,\n            createdAt: new Date(student.created_at),\n            updatedAt: new Date(student.updated_at)\n        };\n    }\n    convertEntryLogDates(entry) {\n        return {\n            ...entry,\n            entryTime: new Date(entry.entry_time),\n            exitTime: entry.exit_time ? new Date(entry.exit_time) : undefined,\n            createdAt: new Date(entry.created_at)\n        };\n    }\n    // Clear all local data (for testing)\n    clearLocalData() {\n        if (this.isLocalStorageAvailable()) {\n            localStorage.removeItem(STUDENTS_KEY);\n            localStorage.removeItem(ENTRIES_KEY);\n        }\n    }\n    // Get storage info\n    getStorageInfo() {\n        return {\n            mode: \"Cloud\",\n            studentsCount: 0,\n            entriesCount: 0\n        };\n    }\n}\nconst dbStore = new DatabaseStore();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/database-store.ts\n"));

/***/ })

});