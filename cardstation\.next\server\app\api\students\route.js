/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/students/route";
exports.ids = ["app/api/students/route"];
exports.modules = {

/***/ "(rsc)/./app/api/students/route.ts":
/*!***********************************!*\
  !*** ./app/api/students/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./lib/mongodb.ts\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nasync function GET(req) {\n    try {\n        const url = new URL(req.url);\n        const appNumber = url.searchParams.get(\"application_number\");\n        const phone = url.searchParams.get(\"phone\");\n        if (!_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) {\n            // MongoDB not available, use sample data for testing\n            console.log(\"MongoDB not available, using sample data for testing\");\n            const sampleStudents = [\n                {\n                    id: \"STU_001\",\n                    application_number: \"APP20254105\",\n                    name: \"Test Student\",\n                    phone: \"9772348371\",\n                    email: \"<EMAIL>\",\n                    class: \"12th\",\n                    department: \"Science\",\n                    schedule: \"Morning\",\n                    image_url: \"/placeholder-user.jpg\",\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                }\n            ];\n            // Filter based on query parameters\n            let filteredStudents = sampleStudents;\n            if (appNumber && phone) {\n                filteredStudents = sampleStudents.filter((s)=>s.application_number === appNumber && s.phone === phone);\n            } else if (appNumber) {\n                filteredStudents = sampleStudents.filter((s)=>s.application_number === appNumber);\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(filteredStudents);\n        }\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        const db = client.db(\"idcard\");\n        const students = db.collection(\"students\");\n        const query = {};\n        if (appNumber) query.application_number = appNumber;\n        if (phone) query.phone = phone;\n        const results = await students.find(query).sort({\n            createdAt: -1\n        }).toArray();\n        const data = results.map((s)=>({\n                ...s,\n                id: s._id.toString()\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"GET /api/students error:\", error);\n        // Return sample data for testing instead of empty array\n        const sampleStudents = [\n            {\n                id: \"STU_001\",\n                application_number: \"APP20254105\",\n                name: \"Test Student\",\n                phone: \"9772348371\",\n                email: \"<EMAIL>\",\n                class: \"12th\",\n                department: \"Science\",\n                schedule: \"Morning\",\n                image_url: \"/placeholder-user.jpg\",\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            }\n        ];\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(sampleStudents);\n    }\n}\nasync function POST(req) {\n    try {\n        const body = await req.json();\n        console.log(\"Received body:\", body);\n        if (!_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Database not available\"\n            }, {\n                status: 503\n            });\n        }\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        if (!client) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Database connection failed\"\n            }, {\n                status: 503\n            });\n        }\n        const db = client.db(\"idcard\");\n        const students = db.collection(\"students\");\n        const newStudent = {\n            ...body,\n            createdAt: new Date(),\n            updatedAt: new Date()\n        };\n        const result = await students.insertOne(newStudent);\n        console.log(\"Insert result:\", result);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            ...newStudent,\n            id: result.insertedId.toString()\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"POST /api/students error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to add student\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PUT(req) {\n    try {\n        const { id, ...updates } = await req.json();\n        if (!_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Database not available\"\n            }, {\n                status: 503\n            });\n        }\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        if (!client) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Database connection failed\"\n            }, {\n                status: 503\n            });\n        }\n        const db = client.db(\"idcard\");\n        const students = db.collection(\"students\");\n        const result = await students.findOneAndUpdate({\n            _id: new mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId(id)\n        }, {\n            $set: {\n                ...updates,\n                updatedAt: new Date()\n            }\n        }, {\n            returnDocument: \"after\"\n        });\n        if (!result || !result.value) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Student not found\"\n            }, {\n                status: 404\n            });\n        }\n        const updated = result.value;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            ...updated,\n            id: updated._id.toString()\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to update student\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(req) {\n    try {\n        const { id } = await req.json();\n        if (!id) return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Missing id\"\n        }, {\n            status: 400\n        });\n        if (!_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Database not available\"\n            }, {\n                status: 503\n            });\n        }\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        if (!client) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Database connection failed\"\n            }, {\n                status: 503\n            });\n        }\n        const db = client.db(\"idcard\");\n        const students = db.collection(\"students\");\n        const result = await students.deleteOne({\n            _id: new mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId(id)\n        });\n        if (result.deletedCount === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Student not found\"\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to delete student\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/students/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/mongodb.ts":
/*!************************!*\
  !*** ./lib/mongodb.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_0__);\n\nconst uri = process.env.MONGODB_URI || \"mongodb://localhost:27017/idcard\";\nconst options = {};\nlet client = null;\nlet clientPromise = null;\ntry {\n    if (process.env.MONGODB_URI) {\n        client = new mongodb__WEBPACK_IMPORTED_MODULE_0__.MongoClient(uri, options);\n        clientPromise = client.connect();\n    }\n} catch (error) {\n    console.warn(\"MongoDB connection failed, will use local storage:\", error);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (clientPromise);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvbW9uZ29kYi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBcUM7QUFFckMsTUFBTUMsTUFBTUMsUUFBUUMsR0FBRyxDQUFDQyxXQUFXLElBQUk7QUFDdkMsTUFBTUMsVUFBVSxDQUFDO0FBRWpCLElBQUlDLFNBQTZCO0FBQ2pDLElBQUlDLGdCQUE2QztBQUVqRCxJQUFJO0lBQ0YsSUFBSUwsUUFBUUMsR0FBRyxDQUFDQyxXQUFXLEVBQUU7UUFDM0JFLFNBQVMsSUFBSU4sZ0RBQVdBLENBQUNDLEtBQUtJO1FBQzlCRSxnQkFBZ0JELE9BQU9FLE9BQU87SUFDaEM7QUFDRixFQUFFLE9BQU9DLE9BQU87SUFDZEMsUUFBUUMsSUFBSSxDQUFDLHNEQUFzREY7QUFDckU7QUFFQSxpRUFBZUYsYUFBYUEsRUFBQSIsInNvdXJjZXMiOlsiRDpcXGlkY2FyZFxcY2FyZHN0YXRpb25cXGxpYlxcbW9uZ29kYi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBNb25nb0NsaWVudCB9IGZyb20gXCJtb25nb2RiXCJcclxuXHJcbmNvbnN0IHVyaSA9IHByb2Nlc3MuZW52Lk1PTkdPREJfVVJJIHx8IFwibW9uZ29kYjovL2xvY2FsaG9zdDoyNzAxNy9pZGNhcmRcIlxyXG5jb25zdCBvcHRpb25zID0ge31cclxuXHJcbmxldCBjbGllbnQ6IE1vbmdvQ2xpZW50IHwgbnVsbCA9IG51bGxcclxubGV0IGNsaWVudFByb21pc2U6IFByb21pc2U8TW9uZ29DbGllbnQ+IHwgbnVsbCA9IG51bGxcclxuXHJcbnRyeSB7XHJcbiAgaWYgKHByb2Nlc3MuZW52Lk1PTkdPREJfVVJJKSB7XHJcbiAgICBjbGllbnQgPSBuZXcgTW9uZ29DbGllbnQodXJpLCBvcHRpb25zKVxyXG4gICAgY2xpZW50UHJvbWlzZSA9IGNsaWVudC5jb25uZWN0KClcclxuICB9XHJcbn0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgY29uc29sZS53YXJuKFwiTW9uZ29EQiBjb25uZWN0aW9uIGZhaWxlZCwgd2lsbCB1c2UgbG9jYWwgc3RvcmFnZTpcIiwgZXJyb3IpXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGNsaWVudFByb21pc2UiXSwibmFtZXMiOlsiTW9uZ29DbGllbnQiLCJ1cmkiLCJwcm9jZXNzIiwiZW52IiwiTU9OR09EQl9VUkkiLCJvcHRpb25zIiwiY2xpZW50IiwiY2xpZW50UHJvbWlzZSIsImNvbm5lY3QiLCJlcnJvciIsImNvbnNvbGUiLCJ3YXJuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstudents%2Froute&page=%2Fapi%2Fstudents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstudents%2Froute.ts&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstudents%2Froute&page=%2Fapi%2Fstudents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstudents%2Froute.ts&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_idcard_cardstation_app_api_students_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/students/route.ts */ \"(rsc)/./app/api/students/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/students/route\",\n        pathname: \"/api/students\",\n        filename: \"route\",\n        bundlePath: \"app/api/students/route\"\n    },\n    resolvedPagePath: \"D:\\\\idcard\\\\cardstation\\\\app\\\\api\\\\students\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_idcard_cardstation_app_api_students_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstudents%2Froute&page=%2Fapi%2Fstudents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstudents%2Froute.ts&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "mongodb":
/*!**************************!*\
  !*** external "mongodb" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongodb");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstudents%2Froute&page=%2Fapi%2Fstudents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstudents%2Froute.ts&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();