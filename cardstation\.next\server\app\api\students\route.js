/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/students/route";
exports.ids = ["app/api/students/route"];
exports.modules = {

/***/ "(rsc)/./app/api/students/route.ts":
/*!***********************************!*\
  !*** ./app/api/students/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./lib/mongodb.ts\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nasync function GET(req) {\n    try {\n        const url = new URL(req.url);\n        const appNumber = url.searchParams.get(\"application_number\");\n        const phone = url.searchParams.get(\"phone\");\n        if (!_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) {\n            // MongoDB not available, return empty array\n            console.log(\"MongoDB not available, returning empty students array\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json([]);\n        }\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        const db = client.db(\"idcard\");\n        const students = db.collection(\"students\");\n        const query = {};\n        if (appNumber) query.application_number = appNumber;\n        if (phone) query.phone = phone;\n        const results = await students.find(query).sort({\n            createdAt: -1\n        }).toArray();\n        const data = results.map((s)=>({\n                ...s,\n                id: s._id.toString()\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"GET /api/students error:\", error);\n        // Return empty array instead of error for better UX\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json([]);\n    }\n}\nasync function POST(req) {\n    try {\n        const body = await req.json();\n        console.log(\"Received body:\", body);\n        if (!_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Database not available\"\n            }, {\n                status: 503\n            });\n        }\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        if (!client) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Database connection failed\"\n            }, {\n                status: 503\n            });\n        }\n        const db = client.db(\"idcard\");\n        const students = db.collection(\"students\");\n        const newStudent = {\n            ...body,\n            createdAt: new Date(),\n            updatedAt: new Date()\n        };\n        const result = await students.insertOne(newStudent);\n        console.log(\"Insert result:\", result);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            ...newStudent,\n            id: result.insertedId.toString()\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"POST /api/students error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to add student\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PUT(req) {\n    try {\n        const { id, ...updates } = await req.json();\n        if (!_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Database not available\"\n            }, {\n                status: 503\n            });\n        }\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        if (!client) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Database connection failed\"\n            }, {\n                status: 503\n            });\n        }\n        const db = client.db(\"idcard\");\n        const students = db.collection(\"students\");\n        const result = await students.findOneAndUpdate({\n            _id: new mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId(id)\n        }, {\n            $set: {\n                ...updates,\n                updatedAt: new Date()\n            }\n        }, {\n            returnDocument: \"after\"\n        });\n        if (!result || !result.value) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Student not found\"\n            }, {\n                status: 404\n            });\n        }\n        const updated = result.value;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            ...updated,\n            id: updated._id.toString()\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to update student\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(req) {\n    try {\n        const { id } = await req.json();\n        if (!id) return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Missing id\"\n        }, {\n            status: 400\n        });\n        if (!_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Database not available\"\n            }, {\n                status: 503\n            });\n        }\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        if (!client) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Database connection failed\"\n            }, {\n                status: 503\n            });\n        }\n        const db = client.db(\"idcard\");\n        const students = db.collection(\"students\");\n        const result = await students.deleteOne({\n            _id: new mongodb__WEBPACK_IMPORTED_MODULE_2__.ObjectId(id)\n        });\n        if (result.deletedCount === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Student not found\"\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to delete student\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/students/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/mongodb.ts":
/*!************************!*\
  !*** ./lib/mongodb.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_0__);\n\nconst uri = process.env.MONGODB_URI || \"mongodb://localhost:27017/idcard\";\nconst options = {};\nlet client = null;\nlet clientPromise = null;\ntry {\n    if (process.env.MONGODB_URI) {\n        client = new mongodb__WEBPACK_IMPORTED_MODULE_0__.MongoClient(uri, options);\n        clientPromise = client.connect();\n    }\n} catch (error) {\n    console.warn(\"MongoDB connection failed, will use local storage:\", error);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (clientPromise);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvbW9uZ29kYi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBcUM7QUFFckMsTUFBTUMsTUFBTUMsUUFBUUMsR0FBRyxDQUFDQyxXQUFXLElBQUk7QUFDdkMsTUFBTUMsVUFBVSxDQUFDO0FBRWpCLElBQUlDLFNBQTZCO0FBQ2pDLElBQUlDLGdCQUE2QztBQUVqRCxJQUFJO0lBQ0YsSUFBSUwsUUFBUUMsR0FBRyxDQUFDQyxXQUFXLEVBQUU7UUFDM0JFLFNBQVMsSUFBSU4sZ0RBQVdBLENBQUNDLEtBQUtJO1FBQzlCRSxnQkFBZ0JELE9BQU9FLE9BQU87SUFDaEM7QUFDRixFQUFFLE9BQU9DLE9BQU87SUFDZEMsUUFBUUMsSUFBSSxDQUFDLHNEQUFzREY7QUFDckU7QUFFQSxpRUFBZUYsYUFBYUEsRUFBQSIsInNvdXJjZXMiOlsiRDpcXGlkY2FyZFxcY2FyZHN0YXRpb25cXGxpYlxcbW9uZ29kYi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBNb25nb0NsaWVudCB9IGZyb20gXCJtb25nb2RiXCJcclxuXHJcbmNvbnN0IHVyaSA9IHByb2Nlc3MuZW52Lk1PTkdPREJfVVJJIHx8IFwibW9uZ29kYjovL2xvY2FsaG9zdDoyNzAxNy9pZGNhcmRcIlxyXG5jb25zdCBvcHRpb25zID0ge31cclxuXHJcbmxldCBjbGllbnQ6IE1vbmdvQ2xpZW50IHwgbnVsbCA9IG51bGxcclxubGV0IGNsaWVudFByb21pc2U6IFByb21pc2U8TW9uZ29DbGllbnQ+IHwgbnVsbCA9IG51bGxcclxuXHJcbnRyeSB7XHJcbiAgaWYgKHByb2Nlc3MuZW52Lk1PTkdPREJfVVJJKSB7XHJcbiAgICBjbGllbnQgPSBuZXcgTW9uZ29DbGllbnQodXJpLCBvcHRpb25zKVxyXG4gICAgY2xpZW50UHJvbWlzZSA9IGNsaWVudC5jb25uZWN0KClcclxuICB9XHJcbn0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgY29uc29sZS53YXJuKFwiTW9uZ29EQiBjb25uZWN0aW9uIGZhaWxlZCwgd2lsbCB1c2UgbG9jYWwgc3RvcmFnZTpcIiwgZXJyb3IpXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGNsaWVudFByb21pc2UiXSwibmFtZXMiOlsiTW9uZ29DbGllbnQiLCJ1cmkiLCJwcm9jZXNzIiwiZW52IiwiTU9OR09EQl9VUkkiLCJvcHRpb25zIiwiY2xpZW50IiwiY2xpZW50UHJvbWlzZSIsImNvbm5lY3QiLCJlcnJvciIsImNvbnNvbGUiLCJ3YXJuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstudents%2Froute&page=%2Fapi%2Fstudents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstudents%2Froute.ts&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstudents%2Froute&page=%2Fapi%2Fstudents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstudents%2Froute.ts&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_idcard_cardstation_app_api_students_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/students/route.ts */ \"(rsc)/./app/api/students/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/students/route\",\n        pathname: \"/api/students\",\n        filename: \"route\",\n        bundlePath: \"app/api/students/route\"\n    },\n    resolvedPagePath: \"D:\\\\idcard\\\\cardstation\\\\app\\\\api\\\\students\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_idcard_cardstation_app_api_students_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstudents%2Froute&page=%2Fapi%2Fstudents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstudents%2Froute.ts&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "mongodb":
/*!**************************!*\
  !*** external "mongodb" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongodb");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstudents%2Froute&page=%2Fapi%2Fstudents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstudents%2Froute.ts&appDir=D%3A%5Cidcard%5Ccardstation%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Ccardstation&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();