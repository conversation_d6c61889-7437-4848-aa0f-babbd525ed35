"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IDCardStation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scan.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _lib_database_store__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/database-store */ \"(app-pages-browser)/./lib/database-store.ts\");\n/* harmony import */ var jsqr__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! jsqr */ \"(app-pages-browser)/./node_modules/jsqr/dist/jsQR.js\");\n/* harmony import */ var jsqr__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(jsqr__WEBPACK_IMPORTED_MODULE_10__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction IDCardStation() {\n    _s();\n    const [currentStudent, setCurrentStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [qrValidated, setQrValidated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScanning, setIsScanning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cameraActive, setCameraActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [qrScannerActive, setQrScannerActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [verificationStatus, setVerificationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"idle\");\n    const [recentEntries, setRecentEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showTryAgain, setShowTryAgain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [availableStudents, setAvailableStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [manualQRData, setManualQRData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showTodayHistory, setShowTodayHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [todayEntries, setTodayEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [faceMatchScore, setFaceMatchScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [scanningForQR, setScanningForQR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [qrScanStatus, setQrScanStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isConnected: false,\n        mode: \"Local Storage\",\n        studentsCount: 0,\n        entriesCount: 0\n    });\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const qrVideoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const qrCanvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scanIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IDCardStation.useEffect\": ()=>{\n            loadData();\n            checkConnection();\n        }\n    }[\"IDCardStation.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IDCardStation.useEffect\": ()=>{\n            // Cleanup scan interval on unmount\n            return ({\n                \"IDCardStation.useEffect\": ()=>{\n                    if (scanIntervalRef.current) {\n                        clearInterval(scanIntervalRef.current);\n                    }\n                }\n            })[\"IDCardStation.useEffect\"];\n        }\n    }[\"IDCardStation.useEffect\"], []);\n    const checkConnection = async ()=>{\n        try {\n            const status = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getStorageInfo();\n            setConnectionStatus({\n                isConnected: status.mode === \"Cloud Database\",\n                mode: status.mode,\n                studentsCount: status.studentsCount,\n                entriesCount: status.entriesCount\n            });\n        } catch (error) {\n            console.error(\"Error checking connection:\", error);\n            setConnectionStatus({\n                isConnected: false,\n                mode: \"Local Storage (Error)\",\n                studentsCount: 0,\n                entriesCount: 0\n            });\n        }\n    };\n    const loadData = async ()=>{\n        try {\n            setLoading(true);\n            const students = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getStudents();\n            const entries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getAllEntries();\n            setAvailableStudents(students);\n            setRecentEntries(entries.slice(0, 5));\n            // Update connection status\n            checkConnection();\n            console.log(\"✅ Loaded \".concat(students.length, \" students from \").concat(connectionStatus.mode));\n        } catch (error) {\n            console.error(\"Error loading data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Enhanced Application Number validation with better error handling\n    const validateApplicationNumber = async (appNumber)=>{\n        try {\n            // Clean the application number\n            const cleanAppNumber = appNumber.trim().toUpperCase();\n            if (!cleanAppNumber) {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: \"Empty Application Number. Please scan a valid QR code.\",\n                    errorType: \"EMPTY_QR\"\n                };\n            }\n            // Validate application number format (should start with APP followed by year and 4 digits)\n            const appNumberPattern = /^APP\\d{8}$/;\n            if (!appNumberPattern.test(cleanAppNumber)) {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: 'Invalid QR Code Format: \"'.concat(cleanAppNumber, '\" is not a valid application number format. Expected format: APP followed by 8 digits.'),\n                    errorType: \"INVALID_FORMAT\"\n                };\n            }\n            // Ensure we have loaded student data from admin database\n            if (availableStudents.length === 0) {\n                setQrScanStatus(\"Loading student data from admin database...\");\n                await loadData();\n                if (availableStudents.length === 0) {\n                    return {\n                        isValid: false,\n                        student: null,\n                        error: \"No students found in admin database. Please check database connection or add students from Admin Panel.\",\n                        errorType: \"NO_DATABASE_CONNECTION\"\n                    };\n                }\n            }\n            // Find student by application number in admin database\n            setQrScanStatus(\"Checking application number against admin database...\");\n            const student = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getStudentByAppNumber(cleanAppNumber);\n            if (!student) {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: 'Application Number Not Found: \"'.concat(cleanAppNumber, '\" is not registered in the admin database. Please verify the QR code or contact admin for registration.'),\n                    errorType: \"NOT_FOUND_IN_DATABASE\"\n                };\n            }\n            // Verify student has required data for face verification\n            if (!student.image_url || student.image_url.trim() === '') {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: \"Student Photo Missing: \".concat(student.name, \" (\").concat(cleanAppNumber, \") does not have a photo in the admin database. Please contact admin to add a photo for face verification.\"),\n                    errorType: \"NO_PHOTO\"\n                };\n            }\n            // Success - Application number is valid and student found in admin database\n            console.log(\"✅ Application Number Validated: \".concat(student.name, \" (\").concat(cleanAppNumber, \")\"));\n            return {\n                isValid: true,\n                student,\n                errorType: \"SUCCESS\"\n            };\n        } catch (error) {\n            console.error(\"Application number validation error:\", error);\n            return {\n                isValid: false,\n                student: null,\n                error: \"Database Connection Error: Unable to validate application number against admin database. Please check connection and try again.\",\n                errorType: \"DATABASE_ERROR\"\n            };\n        }\n    };\n    // Real QR Code detection using jsQR library\n    const detectQRCode = ()=>{\n        if (!qrVideoRef.current || !qrCanvasRef.current) return null;\n        const video = qrVideoRef.current;\n        const canvas = qrCanvasRef.current;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx || video.videoWidth === 0 || video.videoHeight === 0) return null;\n        try {\n            // Set canvas size to match video\n            canvas.width = video.videoWidth;\n            canvas.height = video.videoHeight;\n            // Draw current video frame to canvas\n            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n            // Get image data for QR detection\n            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n            // Use jsQR library for actual QR code detection\n            const code = jsqr__WEBPACK_IMPORTED_MODULE_10___default()(imageData.data, imageData.width, imageData.height, {\n                inversionAttempts: \"dontInvert\"\n            });\n            if (code) {\n                console.log(\"QR Code detected:\", code.data);\n                return code.data;\n            }\n            return null;\n        } catch (error) {\n            console.error(\"QR detection error:\", error);\n            return null;\n        }\n    };\n    // Start QR Scanner with enhanced error handling\n    const startQRScanner = async ()=>{\n        try {\n            setQrScannerActive(true);\n            setScanningForQR(true);\n            setQrScanStatus(\"Starting camera...\");\n            // Ensure we have student data loaded\n            await loadData();\n            let stream;\n            try {\n                // Try back camera first (better for QR scanning)\n                stream = await navigator.mediaDevices.getUserMedia({\n                    video: {\n                        facingMode: \"environment\",\n                        width: {\n                            ideal: 1280,\n                            min: 640\n                        },\n                        height: {\n                            ideal: 720,\n                            min: 480\n                        }\n                    }\n                });\n                setQrScanStatus(\"Back camera active - Point at QR code (\".concat(availableStudents.length, \" students loaded)\"));\n            } catch (envError) {\n                try {\n                    // Fallback to front camera\n                    stream = await navigator.mediaDevices.getUserMedia({\n                        video: {\n                            facingMode: \"user\",\n                            width: {\n                                ideal: 1280,\n                                min: 640\n                            },\n                            height: {\n                                ideal: 720,\n                                min: 480\n                            }\n                        }\n                    });\n                    setQrScanStatus(\"Front camera active - Point at QR code (\".concat(availableStudents.length, \" students loaded)\"));\n                } catch (userError) {\n                    // Fallback to any camera\n                    stream = await navigator.mediaDevices.getUserMedia({\n                        video: {\n                            width: {\n                                ideal: 1280,\n                                min: 640\n                            },\n                            height: {\n                                ideal: 720,\n                                min: 480\n                            }\n                        }\n                    });\n                    setQrScanStatus(\"Camera active - Point at QR code (\".concat(availableStudents.length, \" students loaded)\"));\n                }\n            }\n            if (qrVideoRef.current && stream) {\n                qrVideoRef.current.srcObject = stream;\n                await qrVideoRef.current.play();\n                // Start continuous QR scanning\n                startContinuousScanning();\n                console.log(\"QR Scanner camera started successfully\");\n            }\n        } catch (error) {\n            console.error(\"QR Scanner access error:\", error);\n            setQrScannerActive(false);\n            setScanningForQR(false);\n            setQrScanStatus(\"\");\n            if (error instanceof Error) {\n                if (error.name === \"NotAllowedError\") {\n                    alert(\"Camera Permission Denied!\\n\\nTo fix this:\\n1. Click the camera icon in your browser's address bar\\n2. Allow camera access\\n3. Refresh the page and try again\\n\\nOr use Manual Application Number Input below.\");\n                } else if (error.name === \"NotFoundError\") {\n                    alert(\"No Camera Found!\\n\\nNo camera detected on this device.\\nYou can use Manual Application Number Input below.\");\n                } else {\n                    alert(\"Camera Access Failed!\\n\\nUnable to access camera.\\nYou can use Manual Application Number Input below.\");\n                }\n            } else {\n                alert(\"Camera Access Failed!\\n\\nUnable to access camera.\\nYou can use Manual Application Number Input below.\");\n            }\n        }\n    };\n    // Enhanced continuous scanning with better performance\n    const startContinuousScanning = ()=>{\n        if (scanIntervalRef.current) {\n            clearInterval(scanIntervalRef.current);\n        }\n        scanIntervalRef.current = setInterval(()=>{\n            if (!qrScannerActive || qrValidated) {\n                return;\n            }\n            // Try to detect QR code (Application Number)\n            const detectedAppNumber = detectQRCode();\n            if (detectedAppNumber) {\n                console.log(\"QR Code detected:\", detectedAppNumber);\n                setQrScanStatus(\"✅ QR Code detected! Validating Application Number...\");\n                processApplicationNumber(detectedAppNumber);\n            } else {\n                setQrScanStatus(\"\\uD83D\\uDD0D Scanning for QR code... (\".concat(availableStudents.length, \" students in database)\"));\n            }\n        }, 500) // Scan every 500ms for better responsiveness\n        ;\n    };\n    // Stop QR Scanner\n    const stopQRScanner = ()=>{\n        if (qrVideoRef.current && qrVideoRef.current.srcObject) {\n            const tracks = qrVideoRef.current.srcObject.getTracks();\n            tracks.forEach((track)=>track.stop());\n            qrVideoRef.current.srcObject = null;\n        }\n        if (scanIntervalRef.current) {\n            clearInterval(scanIntervalRef.current);\n            scanIntervalRef.current = null;\n        }\n        setQrScannerActive(false);\n        setScanningForQR(false);\n        setQrScanStatus(\"\");\n    };\n    // Process Manual Application Number Input\n    const handleManualQRInput = async ()=>{\n        if (!manualQRData.trim()) {\n            alert(\"Please enter Application Number\");\n            return;\n        }\n        setQrScanStatus(\"Processing Application Number...\");\n        // Ensure data is loaded\n        await loadData();\n        processApplicationNumber(manualQRData.trim());\n        setManualQRData(\"\");\n    };\n    // Enhanced Process Application Number with better error handling and try again\n    const processApplicationNumber = async (appNumber)=>{\n        console.log(\"Processing Application Number:\", appNumber);\n        setQrScanStatus(\"Validating Application Number against admin database...\");\n        // Ensure we have the latest student data from admin database\n        await loadData();\n        const validation = await validateApplicationNumber(appNumber);\n        if (!validation.isValid) {\n            setQrScanStatus(\"❌ Application Number validation failed!\");\n            // Show specific error message based on error type\n            let errorMessage = \"❌ QR Code Validation Failed!\\n\\n\".concat(validation.error, \"\\n\\n\");\n            let tryAgainMessage = \"\";\n            switch(validation.errorType){\n                case \"EMPTY_QR\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Scanning a valid QR code\\n• Ensuring QR code is clearly visible\\n• Using proper lighting\";\n                    break;\n                case \"INVALID_FORMAT\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Scanning the correct student QR code\\n• Ensuring QR code is not damaged\\n• Getting a new QR code from admin\";\n                    break;\n                case \"NOT_FOUND_IN_DATABASE\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Verifying the application number\\n• Contacting admin for registration\\n• Checking if student is registered in system\";\n                    break;\n                case \"NO_PHOTO\":\n                    tryAgainMessage = \"🔄 Please contact admin to:\\n• Add student photo to database\\n• Complete student registration\\n• Enable face verification\";\n                    break;\n                case \"NO_DATABASE_CONNECTION\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Checking internet connection\\n• Refreshing the page\\n• Contacting admin for database access\";\n                    break;\n                default:\n                    tryAgainMessage = \"🔄 Please try:\\n• Scanning QR code again\\n• Checking database connection\\n• Contacting admin for support\";\n            }\n            alert(errorMessage + tryAgainMessage);\n            // Show try again option for QR scanning\n            setShowTryAgain(true);\n            // Continue scanning if camera is active, otherwise show manual input option\n            if (qrScannerActive) {\n                setTimeout(()=>{\n                    setQrScanStatus(\"Ready to scan again... (\".concat(availableStudents.length, \" students in database)\"));\n                }, 2000);\n            } else {\n                setQrScanStatus(\"Ready to try again - Click 'Start QR Scanner' or enter manually\");\n            }\n            return;\n        }\n        if (validation.student) {\n            setCurrentStudent(validation.student);\n            setQrValidated(true);\n            setVerificationStatus(\"idle\");\n            setShowTryAgain(false);\n            setCameraActive(false);\n            setFaceMatchScore(null);\n            setQrScanStatus(\"✅ Application Number validated successfully! Ready for face verification.\");\n            stopQRScanner();\n            console.log(\"✅ Application Number Validated: \".concat(validation.student.name));\n            console.log(\"Student Details: \".concat(validation.student.class, \", \").concat(validation.student.department));\n            console.log(\"Student Image Available: \".concat(validation.student.image_url ? 'Yes' : 'No'));\n            // Auto-start face verification after successful QR validation\n            setTimeout(()=>{\n                if (validation.student) {\n                    alert(\"✅ QR Code Validated Successfully!\\n\\nStudent: \".concat(validation.student.name, \"\\nClass: \").concat(validation.student.class, \"\\nApplication Number: \").concat(validation.student.application_number, \"\\n\\n\\uD83C\\uDFAF Next Step: Face Verification\\nClick 'Start Face Verification' to proceed.\"));\n                }\n            }, 1000);\n        }\n    };\n    // Start camera for face scanning\n    const startCamera = async ()=>{\n        try {\n            setCameraActive(true);\n            setVerificationStatus(\"scanning\");\n            const stream = await navigator.mediaDevices.getUserMedia({\n                video: {\n                    width: {\n                        ideal: 640\n                    },\n                    height: {\n                        ideal: 480\n                    },\n                    facingMode: \"user\"\n                }\n            });\n            if (videoRef.current) {\n                videoRef.current.srcObject = stream;\n                await videoRef.current.play();\n            }\n        } catch (error) {\n            console.error(\"Camera access denied:\", error);\n            alert(\"Please allow camera access for face verification\");\n            setCameraActive(false);\n            setVerificationStatus(\"idle\");\n        }\n    };\n    // Stop camera\n    const stopCamera = ()=>{\n        if (videoRef.current && videoRef.current.srcObject) {\n            const tracks = videoRef.current.srcObject.getTracks();\n            tracks.forEach((track)=>track.stop());\n            videoRef.current.srcObject = null;\n        }\n        setCameraActive(false);\n        setVerificationStatus(\"idle\");\n    };\n    // Capture current frame from video for face comparison\n    const captureFrame = ()=>{\n        if (!videoRef.current || !canvasRef.current) return null;\n        const canvas = canvasRef.current;\n        const video = videoRef.current;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return null;\n        canvas.width = video.videoWidth;\n        canvas.height = video.videoHeight;\n        ctx.drawImage(video, 0, 0);\n        return canvas.toDataURL(\"image/jpeg\", 0.8);\n    };\n    // Enhanced face verification with better user guidance and error handling\n    const verifyFace = async ()=>{\n        if (!currentStudent || !qrValidated) {\n            alert(\"Please scan a valid Application Number first\");\n            return;\n        }\n        if (!currentStudent.image_url || currentStudent.image_url.trim() === '') {\n            alert(\"❌ Face Verification Error!\\n\\nStudent photo not found in admin database.\\nPlease contact admin to add a photo for this student.\");\n            return;\n        }\n        setIsScanning(true);\n        setFaceMatchScore(null);\n        setVerificationStatus(\"scanning\");\n        // Capture current frame\n        const currentFrame = captureFrame();\n        console.log(\"Starting face verification process...\");\n        console.log(\"Student:\", currentStudent.name);\n        console.log(\"Student stored image:\", currentStudent.image_url);\n        console.log(\"Current frame captured:\", currentFrame ? \"Yes\" : \"No\");\n        // Show progress to user\n        let progress = 0;\n        const progressInterval = setInterval(()=>{\n            progress += 10;\n            if (progress <= 100) {\n                setQrScanStatus(\"\\uD83D\\uDD0D Analyzing face... \".concat(progress, \"%\"));\n            }\n        }, 300);\n        // Simulate face recognition processing time (3 seconds)\n        setTimeout(()=>{\n            clearInterval(progressInterval);\n            // Simulate face matching algorithm with more realistic scoring\n            // In real implementation, this would use actual face recognition API\n            const baseScore = Math.random() * 40 + 60 // Score between 60-100\n            ;\n            const matchScore = Math.round(baseScore);\n            setFaceMatchScore(matchScore);\n            // Consider match successful if score > 75%\n            const isMatch = matchScore > 75;\n            if (isMatch) {\n                setVerificationStatus(\"success\");\n                setQrScanStatus(\"✅ Face verification successful! Match score: \".concat(matchScore, \"%\"));\n                // Show success message\n                setTimeout(()=>{\n                    alert(\"✅ Face Verification Successful!\\n\\nStudent: \".concat(currentStudent.name, \"\\nMatch Score: \").concat(matchScore, \"%\\n\\n\\uD83D\\uDCDD Recording entry...\"));\n                }, 500);\n                // Record entry and reset after showing success\n                recordEntry();\n                setTimeout(()=>{\n                    stopCamera();\n                    resetStation();\n                }, 4000);\n            } else {\n                setVerificationStatus(\"failed\");\n                setQrScanStatus(\"❌ Face verification failed. Match score: \".concat(matchScore, \"%\"));\n                setShowTryAgain(true);\n                // Show failure message with try again option\n                setTimeout(()=>{\n                    alert(\"❌ Face Verification Failed!\\n\\nMatch Score: \".concat(matchScore, \"% (Required: >75%)\\n\\n\\uD83D\\uDD04 Please try again:\\n• Ensure good lighting\\n• Look directly at camera\\n• Remove glasses if wearing\\n• Keep face centered in frame\"));\n                }, 500);\n            }\n            setIsScanning(false);\n        }, 3000);\n    };\n    // Enhanced entry recording with complete verification data\n    const recordEntry = async ()=>{\n        if (!currentStudent) return;\n        try {\n            console.log(\"\\uD83D\\uDCDD Recording entry for \".concat(currentStudent.name, \"...\"));\n            // Create enhanced entry data with verification details\n            const entryData = {\n                student_id: currentStudent.id,\n                application_number: currentStudent.application_number,\n                student_name: currentStudent.name,\n                student_class: currentStudent.class,\n                student_department: currentStudent.department,\n                verification_method: \"qr_and_face\",\n                face_match_score: faceMatchScore,\n                qr_validated: qrValidated,\n                verification_timestamp: new Date().toISOString(),\n                station_id: \"main_entrance\"\n            };\n            const newEntry = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.addEntry(currentStudent.id, currentStudent.application_number, currentStudent.name);\n            // Reload data to show updated entries\n            await loadData();\n            const entryType = newEntry.status === \"entry\" ? \"Entry\" : \"Exit\";\n            console.log(\"✅ \".concat(entryType, \" recorded for \").concat(currentStudent.name));\n            console.log(\"Entry ID: \".concat(newEntry.id));\n            console.log(\"Verification Score: \".concat(faceMatchScore, \"%\"));\n            console.log(\"Timestamp: \".concat(new Date().toLocaleString()));\n            // Show success notification\n            setQrScanStatus(\"✅ \".concat(entryType, \" recorded successfully for \").concat(currentStudent.name));\n        } catch (error) {\n            console.error(\"Error recording entry:\", error);\n            alert(\"❌ Error Recording Entry!\\n\\nFailed to save entry for \".concat(currentStudent.name, \".\\nPlease try again or contact admin.\"));\n            setQrScanStatus(\"❌ Failed to record entry - please try again\");\n        }\n    };\n    // Enhanced try again function with different options\n    const tryAgain = ()=>{\n        setShowTryAgain(false);\n        setVerificationStatus(\"idle\");\n        setFaceMatchScore(null);\n        setQrScanStatus(\"\");\n        stopCamera();\n    };\n    // Try again for QR scanning\n    const tryAgainQR = ()=>{\n        setShowTryAgain(false);\n        setQrValidated(false);\n        setCurrentStudent(null);\n        setVerificationStatus(\"idle\");\n        setFaceMatchScore(null);\n        setQrScanStatus(\"\");\n        stopCamera();\n        stopQRScanner();\n    };\n    // Try again for face verification only\n    const tryAgainFace = ()=>{\n        setShowTryAgain(false);\n        setVerificationStatus(\"idle\");\n        setFaceMatchScore(null);\n        setQrScanStatus(\"Ready for face verification - Click 'Start Face Verification'\");\n        stopCamera();\n    };\n    // Complete reset of the station\n    const resetStation = ()=>{\n        setCurrentStudent(null);\n        setQrValidated(false);\n        setVerificationStatus(\"idle\");\n        setShowTryAgain(false);\n        setFaceMatchScore(null);\n        setQrScanStatus(\"\");\n        setManualQRData(\"\");\n        stopCamera();\n        stopQRScanner();\n        console.log(\"🔄 Station reset - Ready for next student\");\n    };\n    // Load today's entries for history modal\n    const loadTodayHistory = async ()=>{\n        try {\n            const entries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getTodayEntries();\n            setTodayEntries(entries);\n            setShowTodayHistory(true);\n        } catch (error) {\n            console.error(\"Error loading today's history:\", error);\n        }\n    };\n    const formatDateTime = (date)=>{\n        return date.toLocaleString(\"en-IN\", {\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const generateSimpleQRCode = ()=>{\n        if (!currentStudent) return \"\";\n        return currentStudent.application_number;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-purple-50 to-pink-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                    ref: canvasRef,\n                    style: {\n                        display: \"none\"\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 692,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                    ref: qrCanvasRef,\n                    style: {\n                        display: \"none\"\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 693,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-purple-600 p-3 rounded-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-8 w-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 701,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 700,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-3xl\",\n                                                    children: \"Smart ID Card Station\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 704,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    className: \"text-lg\",\n                                                    children: \"Professional QR Scanner & Face Verification System\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 705,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 703,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 699,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: loadData,\n                                        variant: \"outline\",\n                                        disabled: loading,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 712,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Sync Data\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 711,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 710,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 698,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                        lineNumber: 697,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 696,\n                    columnNumber: 9\n                }, this),\n                availableStudents.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                    className: \"border-red-200 bg-red-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"h-4 w-4 text-red-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 725,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                            className: \"text-red-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"No Students Found!\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 727,\n                                    columnNumber: 15\n                                }, this),\n                                \" Please add students from Admin Panel first.\",\n                                connectionStatus.isConnected ? \" Make sure both systems are connected to the same database.\" : \" Check database connection or add students locally.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 726,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 724,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: qrValidated ? \"border-green-200 bg-green-50\" : \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 742,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Step 1: Application Number Scanner\",\n                                                    qrValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"ml-2\",\n                                                        children: \"✅ Validated\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 745,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 741,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 740,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: !qrValidated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    qrScannerActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                        ref: qrVideoRef,\n                                                                        className: \"w-full h-64 object-cover rounded border\",\n                                                                        autoPlay: true,\n                                                                        muted: true,\n                                                                        playsInline: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 758,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute top-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs\",\n                                                                        children: \"QR Scanner Active\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 765,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    scanningForQR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"border-4 border-green-500 border-dashed rounded-lg w-56 h-56 flex items-center justify-center bg-black/10\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-center text-white\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                        className: \"h-16 w-16 mx-auto mb-3 text-green-400\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 772,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-lg font-semibold\",\n                                                                                        children: \"Point Camera Here\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 773,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm\",\n                                                                                        children: \"QR Code with Application Number\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 774,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"mt-2 px-3 py-1 bg-green-500/80 rounded-full text-xs\",\n                                                                                        children: \"Auto-scanning active\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 775,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 771,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 770,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 769,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 757,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            qrScanStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                                className: \"border-blue-200 bg-blue-50\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 786,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                        className: \"text-blue-800\",\n                                                                        children: qrScanStatus\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 787,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 785,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    onClick: stopQRScanner,\n                                                                    variant: \"outline\",\n                                                                    className: \"w-full bg-transparent\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"mr-2 h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 793,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"Stop Scanner\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 792,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 791,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 756,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-64 flex items-center justify-center bg-gray-100 rounded border\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"h-16 w-16 mx-auto text-gray-400 mb-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 802,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-600\",\n                                                                            children: \"Application Number Scanner Ready\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 803,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Point camera at student's QR code\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 804,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400 mt-2\",\n                                                                            children: [\n                                                                                availableStudents.length,\n                                                                                \" students in database\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 805,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 801,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 800,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                onClick: startQRScanner,\n                                                                className: \"w-full\",\n                                                                disabled: loading || availableStudents.length === 0,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 815,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    availableStudents.length === 0 ? \"Add Students First\" : \"Start QR Code Scanner\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 810,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 799,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 821,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"manualQR\",\n                                                                children: \"Manual Application Number Input\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 825,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        id: \"manualQR\",\n                                                                        value: manualQRData,\n                                                                        onChange: (e)=>setManualQRData(e.target.value),\n                                                                        placeholder: \"Enter Application Number (e.g: APP20241234)\",\n                                                                        className: \"flex-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 827,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        onClick: handleManualQRInput,\n                                                                        variant: \"outline\",\n                                                                        disabled: availableStudents.length === 0,\n                                                                        children: \"Validate\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 834,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 826,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"Enter Application Number from Student App\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 842,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 824,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                        className: \"border-blue-200 bg-blue-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4 text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 847,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                className: \"text-blue-800\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Connected to Same Database:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 849,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"list-disc list-inside text-xs mt-1 space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"QR code contains student's Application Number\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 851,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Scanner reads Application Number from QR code\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 852,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"System finds student details from same admin database\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 853,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Face verification with stored student photo\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 854,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 850,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 848,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 846,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-16 w-16 mx-auto text-green-600 mb-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 861,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-green-700 font-semibold\",\n                                                        children: \"Application Number Successfully Validated!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 862,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-green-600\",\n                                                        children: \"Student found in database - Proceed to face verification\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 863,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        onClick: resetStation,\n                                                        variant: \"outline\",\n                                                        className: \"mt-4 bg-transparent\",\n                                                        children: \"Scan Different Application Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 864,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 860,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 751,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 739,\n                                    columnNumber: 13\n                                }, this),\n                                currentStudent && qrValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"border-2 border-blue-200 bg-blue-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 878,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Student Found in Database\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 877,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: resetStation,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 882,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 881,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 876,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 875,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: currentStudent.image_url || \"/placeholder.svg\",\n                                                                    alt: currentStudent.name,\n                                                                    className: \"w-24 h-24 rounded-full border-4 border-blue-300 object-cover\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 889,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"absolute -bottom-2 -right-2 text-xs\",\n                                                                    children: \"Reference Photo\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 894,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 888,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-blue-800\",\n                                                                    children: currentStudent.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 899,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-sm font-mono\",\n                                                                    children: currentStudent.application_number\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 900,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-700\",\n                                                                    children: [\n                                                                        currentStudent.class,\n                                                                        \" - \",\n                                                                        currentStudent.department\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 903,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: \"default\",\n                                                                    className: \"text-xs bg-green-600\",\n                                                                    children: \"✅ Found in Database\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 906,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 898,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 887,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 912,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-blue-700\",\n                                                                    children: \"Phone:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 916,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-600\",\n                                                                    children: currentStudent.phone\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 917,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 915,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-blue-700\",\n                                                                    children: \"Schedule:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 920,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-600 text-xs\",\n                                                                    children: currentStudent.schedule || \"Not assigned\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 921,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 919,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 914,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                    className: \"border-yellow-200 bg-yellow-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4 text-yellow-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 926,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                            className: \"text-yellow-800\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Next Step:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 928,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Face verification required to match with stored photo above\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 927,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 925,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 886,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 874,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 737,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: verificationStatus === \"success\" ? \"border-green-200 bg-green-50\" : \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 942,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Step 2: Face Verification\",\n                                                    verificationStatus === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"ml-2\",\n                                                        children: \"✅ Verified\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 945,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 941,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 940,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative bg-gray-100 rounded-lg overflow-hidden\",\n                                                    children: cameraActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                        ref: videoRef,\n                                                                        className: \"w-full h-64 object-cover rounded\",\n                                                                        autoPlay: true,\n                                                                        muted: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 956,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute top-2 right-2 bg-black/50 text-white px-2 py-1 rounded text-xs\",\n                                                                        children: \"Live Camera\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 957,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 955,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        onClick: verifyFace,\n                                                                        disabled: isScanning || verificationStatus !== \"scanning\" || !qrValidated,\n                                                                        className: \"flex-1\",\n                                                                        children: isScanning ? \"Analyzing Face...\" : \"Verify Face Match\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 963,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        onClick: stopCamera,\n                                                                        variant: \"outline\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 971,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 970,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 962,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            faceMatchScore !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: [\n                                                                            \"Face Match Score: \",\n                                                                            faceMatchScore,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 977,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-full bg-gray-200 rounded-full h-2 mt-1\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-2 rounded-full \".concat(faceMatchScore > 75 ? \"bg-green-500\" : \"bg-red-500\"),\n                                                                            style: {\n                                                                                width: \"\".concat(faceMatchScore, \"%\")\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 979,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 978,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 976,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 954,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-64 flex items-center justify-center text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-12 w-12 mx-auto mb-2 opacity-50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 990,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Face Camera Ready\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 991,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: qrValidated ? \"Click to start face verification\" : \"Scan Application Number first\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 992,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 989,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 988,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 952,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 space-y-3\",\n                                                    children: [\n                                                        verificationStatus === \"idle\" && qrValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            onClick: startCamera,\n                                                            className: \"w-full\",\n                                                            variant: \"default\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1004,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Start Face Verification\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1003,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        verificationStatus === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                            className: \"border-green-200 bg-green-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1011,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                    className: \"text-green-800\",\n                                                                    children: [\n                                                                        \"✅ Face Verification Successful! Entry Recorded.\",\n                                                                        faceMatchScore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"block text-sm\",\n                                                                            children: [\n                                                                                \"Match Score: \",\n                                                                                faceMatchScore,\n                                                                                \"%\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1014,\n                                                                            columnNumber: 44\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1012,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1010,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        verificationStatus === \"failed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                            className: \"border-red-200 bg-red-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-red-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1021,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                    className: \"text-red-800\",\n                                                                    children: [\n                                                                        \"❌ Face Verification Failed! Face doesn't match stored photo.\",\n                                                                        faceMatchScore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"block text-sm\",\n                                                                            children: [\n                                                                                \"Match Score: \",\n                                                                                faceMatchScore,\n                                                                                \"%\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1024,\n                                                                            columnNumber: 44\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1022,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1020,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        showTryAgain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                                    className: \"border-orange-200 bg-orange-50\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-orange-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1032,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                            className: \"text-orange-800\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Verification Failed!\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1034,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \" Choose an option below:\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1033,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1031,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 gap-2\",\n                                                                    children: [\n                                                                        verificationStatus === \"failed\" && qrValidated ? // Face verification failed, but QR is valid\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                    onClick: tryAgainFace,\n                                                                                    variant: \"outline\",\n                                                                                    className: \"w-full\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                            className: \"mr-2 h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 1043,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        \"Try Face Verification Again\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1042,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                    onClick: tryAgainQR,\n                                                                                    variant: \"outline\",\n                                                                                    className: \"w-full\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                            className: \"mr-2 h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 1047,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        \"Scan Different QR Code\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1046,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true) : // QR validation failed\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                onClick: tryAgainQR,\n                                                                                variant: \"outline\",\n                                                                                className: \"w-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                        className: \"mr-2 h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1055,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    \"Try QR Scan Again\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1054,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            onClick: resetStation,\n                                                                            variant: \"destructive\",\n                                                                            className: \"w-full\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"mr-2 h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1061,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Reset Station\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1060,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1038,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1030,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        !qrValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                            className: \"border-yellow-200 bg-yellow-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-yellow-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1070,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                    className: \"text-yellow-800\",\n                                                                    children: \"Please scan and validate an Application Number first before face verification.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1071,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1069,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1001,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 951,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 939,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1084,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Recent Entries\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1083,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1082,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 max-h-64 overflow-y-auto\",\n                                                children: recentEntries.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-center py-4\",\n                                                    children: \"No entries yet\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1091,\n                                                    columnNumber: 21\n                                                }, this) : recentEntries.map((log)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: log.student_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1096,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: formatDateTime(log.entryTime)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1097,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            \"App: \",\n                                                                            log.application_number\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1098,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1095,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                variant: log.status === \"entry\" ? \"default\" : \"secondary\",\n                                                                children: log.status === \"entry\" ? \"🟢 Entry\" : \"🔴 Exit\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1100,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, log.id, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1094,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1089,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1088,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 1081,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 937,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 735,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"Database Connection & System Integration\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 1115,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 1114,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-purple-700 mb-2\",\n                                                children: \"Same Database Connection:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1120,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                className: \"list-decimal list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Station connects to same database as Admin Panel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1122,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Students added in Admin are instantly available here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1123,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Entry logs are shared across both systems\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1124,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Real-time data synchronization\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1125,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Fallback to local storage if database unavailable\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1126,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Automatic data sync when connection restored\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1127,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1121,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1119,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-green-700 mb-2\",\n                                                children: \"Professional Station Features:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1131,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Dedicated website for security staff\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1133,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"No login required - direct access\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1134,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Real-time QR code scanning\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1135,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Live face verification system\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1136,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Automatic entry/exit logging\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1137,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Professional security interface\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1138,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1132,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1130,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 1118,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 1117,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 1113,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n            lineNumber: 690,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n        lineNumber: 689,\n        columnNumber: 5\n    }, this);\n}\n_s(IDCardStation, \"xnpj/6OFHfAd7O/TavDfQlDPni0=\");\n_c = IDCardStation;\nvar _c;\n$RefreshReg$(_c, \"IDCardStation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});