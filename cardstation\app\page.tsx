"use client"

import { useState, useRef, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Camera,
  CameraOff,
  CheckCircle,
  XCircle,
  QrCode,
  User,
  Clock,
  RefreshCw,
  AlertTriangle,
  RotateCcw,
  Scan,
  Database,
  Wifi,
  WifiOff,
  Shield,
} from "lucide-react"
import { dbStore, type Student, type EntryLog } from "@/lib/database-store"
import jsQR from "jsqr"

export default function IDCardStation() {
  const [currentStudent, setCurrentStudent] = useState<Student | null>(null)
  const [qrValidated, setQrValidated] = useState(false)
  const [isScanning, setIsScanning] = useState(false)
  const [cameraActive, setCameraActive] = useState(false)
  const [qrScannerActive, setQrScannerActive] = useState(false)
  const [verificationStatus, setVerificationStatus] = useState<"idle" | "scanning" | "success" | "failed">("idle")
  const [recentEntries, setRecentEntries] = useState<EntryLog[]>([])
  const [showTryAgain, setShowTryAgain] = useState(false)
  const [availableStudents, setAvailableStudents] = useState<Student[]>([])
  const [loading, setLoading] = useState(false)
  const [manualQRData, setManualQRData] = useState("")
  const [faceMatchScore, setFaceMatchScore] = useState<number | null>(null)
  const [scanningForQR, setScanningForQR] = useState(false)
  const [qrScanStatus, setQrScanStatus] = useState("")
  const [connectionStatus, setConnectionStatus] = useState({
    isConnected: false,
    mode: "Local Storage",
    studentsCount: 0,
    entriesCount: 0,
  })
  const videoRef = useRef<HTMLVideoElement>(null)
  const qrVideoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const qrCanvasRef = useRef<HTMLCanvasElement>(null)
  const scanIntervalRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    loadData()
    checkConnection()
  }, [])

  useEffect(() => {
    // Cleanup scan interval on unmount
    return () => {
      if (scanIntervalRef.current) {
        clearInterval(scanIntervalRef.current)
      }
    }
  }, [])

  const checkConnection = async () => {
    try {
      const status = await dbStore.getStorageInfo()
      setConnectionStatus({
        isConnected: status.mode === "Cloud Database",
        mode: status.mode,
        studentsCount: status.studentsCount,
        entriesCount: status.entriesCount,
      })
    } catch (error) {
      console.error("Error checking connection:", error)
      setConnectionStatus({
        isConnected: false,
        mode: "Local Storage (Error)",
        studentsCount: 0,
        entriesCount: 0,
      })
    }
  }

  const loadData = async () => {
    try {
      setLoading(true)

      const students = await dbStore.getStudents()
      const entries = await dbStore.getAllEntries()
      setAvailableStudents(students)
      setRecentEntries(entries.slice(0, 5))

      // Update connection status
      checkConnection()

      console.log(`✅ Loaded ${students.length} students from ${connectionStatus.mode}`)
    } catch (error) {
      console.error("Error loading data:", error)
    } finally {
      setLoading(false)
    }
  }

  // Enhanced Application Number validation with better error handling
  const validateApplicationNumber = async (
    appNumber: string,
  ): Promise<{ isValid: boolean; student: Student | null; error?: string; errorType?: string }> => {
    try {
      // Clean the application number
      const cleanAppNumber = appNumber.trim().toUpperCase()

      if (!cleanAppNumber) {
        return {
          isValid: false,
          student: null,
          error: "Empty Application Number. Please scan a valid QR code.",
          errorType: "EMPTY_QR"
        }
      }

      // Validate application number format (should start with APP followed by year and 4 digits)
      const appNumberPattern = /^APP\d{8}$/
      if (!appNumberPattern.test(cleanAppNumber)) {
        return {
          isValid: false,
          student: null,
          error: `Invalid QR Code Format: "${cleanAppNumber}" is not a valid application number format. Expected format: APP followed by 8 digits.`,
          errorType: "INVALID_FORMAT"
        }
      }

      // Ensure we have loaded student data from admin database
      if (availableStudents.length === 0) {
        setQrScanStatus("Loading student data from admin database...")
        await loadData()
        if (availableStudents.length === 0) {
          return {
            isValid: false,
            student: null,
            error: "No students found in admin database. Please check database connection or add students from Admin Panel.",
            errorType: "NO_DATABASE_CONNECTION"
          }
        }
      }

      // Find student by application number in admin database
      setQrScanStatus("Checking application number against admin database...")
      const student = await dbStore.getStudentByAppNumber(cleanAppNumber)

      if (!student) {
        return {
          isValid: false,
          student: null,
          error: `Application Number Not Found: "${cleanAppNumber}" is not registered in the admin database. Please verify the QR code or contact admin for registration.`,
          errorType: "NOT_FOUND_IN_DATABASE"
        }
      }

      // Verify student has required data for face verification
      if (!student.image_url || student.image_url.trim() === '') {
        return {
          isValid: false,
          student: null,
          error: `Student Photo Missing: ${student.name} (${cleanAppNumber}) does not have a photo in the admin database. Please contact admin to add a photo for face verification.`,
          errorType: "NO_PHOTO"
        }
      }

      // Success - Application number is valid and student found in admin database
      console.log(`✅ Application Number Validated: ${student.name} (${cleanAppNumber})`)
      return { isValid: true, student, errorType: "SUCCESS" }
    } catch (error) {
      console.error("Application number validation error:", error)
      return {
        isValid: false,
        student: null,
        error: "Database Connection Error: Unable to validate application number against admin database. Please check connection and try again.",
        errorType: "DATABASE_ERROR"
      }
    }
  }

  // Real QR Code detection using jsQR library
  const detectQRCode = (): string | null => {
    if (!qrVideoRef.current || !qrCanvasRef.current) return null

    const video = qrVideoRef.current
    const canvas = qrCanvasRef.current
    const ctx = canvas.getContext("2d")

    if (!ctx || video.videoWidth === 0 || video.videoHeight === 0) return null

    try {
      // Set canvas size to match video
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight

      // Draw current video frame to canvas
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height)

      // Get image data for QR detection
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)

      // Use jsQR library for actual QR code detection
      const code = jsQR(imageData.data, imageData.width, imageData.height, {
        inversionAttempts: "dontInvert",
      })

      if (code) {
        console.log("QR Code detected:", code.data)
        return code.data
      }

      return null
    } catch (error) {
      console.error("QR detection error:", error)
      return null
    }
  }

  // Start QR Scanner with enhanced error handling
  const startQRScanner = async () => {
    try {
      setQrScannerActive(true)
      setScanningForQR(true)
      setQrScanStatus("Starting camera...")

      // Ensure we have student data loaded
      await loadData()

      let stream
      try {
        // Try back camera first (better for QR scanning)
        stream = await navigator.mediaDevices.getUserMedia({
          video: {
            facingMode: "environment",
            width: { ideal: 1280, min: 640 },
            height: { ideal: 720, min: 480 },
          },
        })
        setQrScanStatus(`Back camera active - Point at QR code (${availableStudents.length} students loaded)`)
      } catch (envError) {
        try {
          // Fallback to front camera
          stream = await navigator.mediaDevices.getUserMedia({
            video: {
              facingMode: "user",
              width: { ideal: 1280, min: 640 },
              height: { ideal: 720, min: 480 },
            },
          })
          setQrScanStatus(`Front camera active - Point at QR code (${availableStudents.length} students loaded)`)
        } catch (userError) {
          // Fallback to any camera
          stream = await navigator.mediaDevices.getUserMedia({
            video: {
              width: { ideal: 1280, min: 640 },
              height: { ideal: 720, min: 480 },
            },
          })
          setQrScanStatus(`Camera active - Point at QR code (${availableStudents.length} students loaded)`)
        }
      }

      if (qrVideoRef.current && stream) {
        qrVideoRef.current.srcObject = stream
        await qrVideoRef.current.play()

        // Start continuous QR scanning
        startContinuousScanning()
        console.log("QR Scanner camera started successfully")
      }
    } catch (error) {
      console.error("QR Scanner access error:", error)
      setQrScannerActive(false)
      setScanningForQR(false)
      setQrScanStatus("")

      if (error instanceof Error) {
        if (error.name === "NotAllowedError") {
          alert(
            "Camera Permission Denied!\n\nTo fix this:\n1. Click the camera icon in your browser's address bar\n2. Allow camera access\n3. Refresh the page and try again\n\nOr use Manual Application Number Input below.",
          )
        } else if (error.name === "NotFoundError") {
          alert(
            "No Camera Found!\n\nNo camera detected on this device.\nYou can use Manual Application Number Input below.",
          )
        } else {
          alert("Camera Access Failed!\n\nUnable to access camera.\nYou can use Manual Application Number Input below.")
        }
      } else {
        alert("Camera Access Failed!\n\nUnable to access camera.\nYou can use Manual Application Number Input below.")
      }
    }
  }

  // Enhanced continuous scanning with better performance
  const startContinuousScanning = () => {
    if (scanIntervalRef.current) {
      clearInterval(scanIntervalRef.current)
    }

    scanIntervalRef.current = setInterval(() => {
      if (!qrScannerActive || qrValidated) {
        return
      }

      // Try to detect QR code (Application Number)
      const detectedAppNumber = detectQRCode()

      if (detectedAppNumber) {
        console.log("QR Code detected:", detectedAppNumber)
        setQrScanStatus("✅ QR Code detected! Validating Application Number...")
        processApplicationNumber(detectedAppNumber)
      } else {
        setQrScanStatus(`🔍 Scanning for QR code... (${availableStudents.length} students in database)`)
      }
    }, 500) // Scan every 500ms for better responsiveness
  }

  // Stop QR Scanner
  const stopQRScanner = () => {
    if (qrVideoRef.current && qrVideoRef.current.srcObject) {
      const tracks = (qrVideoRef.current.srcObject as MediaStream).getTracks()
      tracks.forEach((track) => track.stop())
      qrVideoRef.current.srcObject = null
    }

    if (scanIntervalRef.current) {
      clearInterval(scanIntervalRef.current)
      scanIntervalRef.current = null
    }

    setQrScannerActive(false)
    setScanningForQR(false)
    setQrScanStatus("")
  }

  // Process Manual Application Number Input
  const handleManualQRInput = async () => {
    if (!manualQRData.trim()) {
      alert("Please enter Application Number")
      return
    }

    setQrScanStatus("Processing Application Number...")

    // Ensure data is loaded
    await loadData()

    processApplicationNumber(manualQRData.trim())
    setManualQRData("")
  }

  // Enhanced Process Application Number with better error handling and try again
  const processApplicationNumber = async (appNumber: string) => {
    console.log("Processing Application Number:", appNumber)
    setQrScanStatus("Validating Application Number against admin database...")

    // Ensure we have the latest student data from admin database
    await loadData()

    const validation = await validateApplicationNumber(appNumber)

    if (!validation.isValid) {
      setQrScanStatus("❌ Application Number validation failed!")

      // Show specific error message based on error type
      let errorMessage = `❌ QR Code Validation Failed!\n\n${validation.error}\n\n`
      let tryAgainMessage = ""

      switch (validation.errorType) {
        case "EMPTY_QR":
          tryAgainMessage = "🔄 Please try:\n• Scanning a valid QR code\n• Ensuring QR code is clearly visible\n• Using proper lighting"
          break
        case "INVALID_FORMAT":
          tryAgainMessage = "🔄 Please try:\n• Scanning the correct student QR code\n• Ensuring QR code is not damaged\n• Getting a new QR code from admin"
          break
        case "NOT_FOUND_IN_DATABASE":
          tryAgainMessage = "🔄 Please try:\n• Verifying the application number\n• Contacting admin for registration\n• Checking if student is registered in system"
          break
        case "NO_PHOTO":
          tryAgainMessage = "🔄 Please contact admin to:\n• Add student photo to database\n• Complete student registration\n• Enable face verification"
          break
        case "NO_DATABASE_CONNECTION":
          tryAgainMessage = "🔄 Please try:\n• Checking internet connection\n• Refreshing the page\n• Contacting admin for database access"
          break
        default:
          tryAgainMessage = "🔄 Please try:\n• Scanning QR code again\n• Checking database connection\n• Contacting admin for support"
      }

      alert(errorMessage + tryAgainMessage)

      // Show try again option for QR scanning
      setShowTryAgain(true)

      // Continue scanning if camera is active, otherwise show manual input option
      if (qrScannerActive) {
        setTimeout(() => {
          setQrScanStatus(`Ready to scan again... (${availableStudents.length} students in database)`)
        }, 2000)
      } else {
        setQrScanStatus("Ready to try again - Click 'Start QR Scanner' or enter manually")
      }
      return
    }

    if (validation.student) {
      setCurrentStudent(validation.student)
      setQrValidated(true)
      setVerificationStatus("idle")
      setShowTryAgain(false)
      setCameraActive(false)
      setFaceMatchScore(null)
      setQrScanStatus("✅ Application Number validated successfully! Ready for face verification.")
      stopQRScanner()

      console.log(`✅ Application Number Validated: ${validation.student.name}`)
      console.log(`Student Details: ${validation.student.class}, ${validation.student.department}`)
      console.log(`Student Image Available: ${validation.student.image_url ? 'Yes' : 'No'}`)

      // Auto-start face verification after successful QR validation
      setTimeout(() => {
        if (validation.student) {
          alert(`✅ QR Code Validated Successfully!\n\nStudent: ${validation.student.name}\nClass: ${validation.student.class}\nApplication Number: ${validation.student.application_number}\n\n🎯 Next Step: Face Verification\nClick 'Start Face Verification' to proceed.`)
        }
      }, 1000)
    }
  }

  // Start camera for face scanning
  const startCamera = async () => {
    try {
      setCameraActive(true)
      setVerificationStatus("scanning")

      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 640 },
          height: { ideal: 480 },
          facingMode: "user", // Front camera for face verification
        },
      })

      if (videoRef.current) {
        videoRef.current.srcObject = stream
        await videoRef.current.play()
      }
    } catch (error) {
      console.error("Camera access denied:", error)
      alert("Please allow camera access for face verification")
      setCameraActive(false)
      setVerificationStatus("idle")
    }
  }

  // Stop camera
  const stopCamera = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const tracks = (videoRef.current.srcObject as MediaStream).getTracks()
      tracks.forEach((track) => track.stop())
      videoRef.current.srcObject = null
    }
    setCameraActive(false)
    setVerificationStatus("idle")
  }

  // Capture current frame from video for face comparison
  const captureFrame = (): string | null => {
    if (!videoRef.current || !canvasRef.current) return null

    const canvas = canvasRef.current
    const video = videoRef.current
    const ctx = canvas.getContext("2d")

    if (!ctx) return null

    canvas.width = video.videoWidth
    canvas.height = video.videoHeight
    ctx.drawImage(video, 0, 0)

    return canvas.toDataURL("image/jpeg", 0.8)
  }

  // Enhanced face verification with better user guidance and error handling
  const verifyFace = async () => {
    if (!currentStudent || !qrValidated) {
      alert("Please scan a valid Application Number first")
      return
    }

    if (!currentStudent.image_url || currentStudent.image_url.trim() === '') {
      alert("❌ Face Verification Error!\n\nStudent photo not found in admin database.\nPlease contact admin to add a photo for this student.")
      return
    }

    setIsScanning(true)
    setFaceMatchScore(null)
    setVerificationStatus("scanning")

    // Capture current frame
    const currentFrame = captureFrame()

    console.log("Starting face verification process...")
    console.log("Student:", currentStudent.name)
    console.log("Student stored image:", currentStudent.image_url)
    console.log("Current frame captured:", currentFrame ? "Yes" : "No")

    // Show progress to user
    let progress = 0
    const progressInterval = setInterval(() => {
      progress += 10
      if (progress <= 100) {
        setQrScanStatus(`🔍 Analyzing face... ${progress}%`)
      }
    }, 300)

    // Simulate face recognition processing time (3 seconds)
    setTimeout(() => {
      clearInterval(progressInterval)

      // Simulate face matching algorithm with more realistic scoring
      // In real implementation, this would use actual face recognition API
      const baseScore = Math.random() * 40 + 60 // Score between 60-100
      const matchScore = Math.round(baseScore)
      setFaceMatchScore(matchScore)

      // Consider match successful if score > 75%
      const isMatch = matchScore > 75

      if (isMatch) {
        setVerificationStatus("success")
        setQrScanStatus(`✅ Face verification successful! Match score: ${matchScore}%`)

        // Show success message
        setTimeout(() => {
          alert(`✅ Face Verification Successful!\n\nStudent: ${currentStudent.name}\nMatch Score: ${matchScore}%\n\n📝 Recording entry...`)
        }, 500)

        // Record entry and reset after showing success
        recordEntry()
        setTimeout(() => {
          stopCamera()
          resetStation()
        }, 4000)
      } else {
        setVerificationStatus("failed")
        setQrScanStatus(`❌ Face verification failed. Match score: ${matchScore}%`)
        setShowTryAgain(true)

        // Show failure message with try again option
        setTimeout(() => {
          alert(`❌ Face Verification Failed!\n\nMatch Score: ${matchScore}% (Required: >75%)\n\n🔄 Please try again:\n• Ensure good lighting\n• Look directly at camera\n• Remove glasses if wearing\n• Keep face centered in frame`)
        }, 500)
      }

      setIsScanning(false)
    }, 3000)
  }

  // Enhanced entry recording with complete verification data
  const recordEntry = async () => {
    if (!currentStudent) return

    try {
      console.log(`📝 Recording entry for ${currentStudent.name}...`)

      // Create enhanced entry data with verification details
      const entryData = {
        student_id: currentStudent.id,
        application_number: currentStudent.application_number,
        student_name: currentStudent.name,
        student_class: currentStudent.class,
        student_department: currentStudent.department,
        verification_method: "qr_and_face",
        face_match_score: faceMatchScore,
        qr_validated: qrValidated,
        verification_timestamp: new Date().toISOString(),
        station_id: "main_entrance", // You can make this configurable
      }

      const newEntry = await dbStore.addEntry(
        currentStudent.id,
        currentStudent.application_number,
        currentStudent.name,
      )

      // Reload data to show updated entries
      await loadData()

      const entryType = newEntry.status === "entry" ? "Entry" : "Exit"
      console.log(`✅ ${entryType} recorded for ${currentStudent.name}`)
      console.log(`Entry ID: ${newEntry.id}`)
      console.log(`Verification Score: ${faceMatchScore}%`)
      console.log(`Timestamp: ${new Date().toLocaleString()}`)

      // Show success notification
      setQrScanStatus(`✅ ${entryType} recorded successfully for ${currentStudent.name}`)

    } catch (error) {
      console.error("Error recording entry:", error)
      alert(`❌ Error Recording Entry!\n\nFailed to save entry for ${currentStudent.name}.\nPlease try again or contact admin.`)
      setQrScanStatus("❌ Failed to record entry - please try again")
    }
  }



  // Enhanced try again function with different options
  const tryAgain = () => {
    setShowTryAgain(false)
    setVerificationStatus("idle")
    setFaceMatchScore(null)
    setQrScanStatus("")
    stopCamera()
  }

  // Try again for QR scanning
  const tryAgainQR = () => {
    setShowTryAgain(false)
    setQrValidated(false)
    setCurrentStudent(null)
    setVerificationStatus("idle")
    setFaceMatchScore(null)
    setQrScanStatus("")
    stopCamera()
    stopQRScanner()
  }

  // Try again for face verification only
  const tryAgainFace = () => {
    setShowTryAgain(false)
    setVerificationStatus("idle")
    setFaceMatchScore(null)
    setQrScanStatus("Ready for face verification - Click 'Start Face Verification'")
    stopCamera()
  }

  // Complete reset of the station
  const resetStation = () => {
    setCurrentStudent(null)
    setQrValidated(false)
    setVerificationStatus("idle")
    setShowTryAgain(false)
    setFaceMatchScore(null)
    setQrScanStatus("")
    setManualQRData("")
    stopCamera()
    stopQRScanner()
    console.log("🔄 Station reset - Ready for next student")
  }

  const formatDateTime = (date: Date) => {
    return date.toLocaleString("en-IN", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const generateSimpleQRCode = () => {
    if (!currentStudent) return ""
    return currentStudent.application_number
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-50 p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Hidden canvases for image processing */}
        <canvas ref={canvasRef} style={{ display: "none" }} />
        <canvas ref={qrCanvasRef} style={{ display: "none" }} />

        {/* Header */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="bg-purple-600 p-3 rounded-full">
                  <Shield className="h-8 w-8 text-white" />
                </div>
                <div>
                  <CardTitle className="text-3xl">Smart ID Card Station</CardTitle>
                  <CardDescription className="text-lg">
                    Professional QR Scanner & Face Verification System
                  </CardDescription>
                </div>
              </div>
              <div className="flex gap-2">
                <Button onClick={loadData} variant="outline" disabled={loading}>
                  <RefreshCw className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`} />
                  Sync Data
                </Button>
              </div>
            </div>
          </CardHeader>
        </Card>



        {/* No Students Alert */}
        {availableStudents.length === 0 && (
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              <strong>No Students Found!</strong> Please add students from Admin Panel first.
              {connectionStatus.isConnected
                ? " Make sure both systems are connected to the same database."
                : " Check database connection or add students locally."}
            </AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Panel - QR Scanner & Student Display */}
          <div className="space-y-4">
            {/* QR Code Scanner */}
            <Card className={qrValidated ? "border-green-200 bg-green-50" : ""}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <QrCode className="h-5 w-5" />
                  Step 1: Application Number Scanner
                  {qrValidated && (
                    <Badge variant="secondary" className="ml-2">
                      ✅ Validated
                    </Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {!qrValidated ? (
                  <>
                    {/* QR Scanner Camera */}
                    {qrScannerActive ? (
                      <div className="space-y-4">
                        <div className="relative">
                          <video
                            ref={qrVideoRef}
                            className="w-full h-64 object-cover rounded border"
                            autoPlay
                            muted
                            playsInline
                          />
                          <div className="absolute top-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
                            QR Scanner Active
                          </div>
                          {scanningForQR && (
                            <div className="absolute inset-0 flex items-center justify-center">
                              <div className="border-4 border-green-500 border-dashed rounded-lg w-56 h-56 flex items-center justify-center bg-black/10">
                                <div className="text-center text-white">
                                  <QrCode className="h-16 w-16 mx-auto mb-3 text-green-400" />
                                  <p className="text-lg font-semibold">Point Camera Here</p>
                                  <p className="text-sm">QR Code with Application Number</p>
                                  <div className="mt-2 px-3 py-1 bg-green-500/80 rounded-full text-xs">
                                    Auto-scanning active
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>

                        {qrScanStatus && (
                          <Alert className="border-blue-200 bg-blue-50">
                            <Scan className="h-4 w-4 text-blue-600" />
                            <AlertDescription className="text-blue-800">{qrScanStatus}</AlertDescription>
                          </Alert>
                        )}

                        <div className="flex gap-2">
                          <Button onClick={stopQRScanner} variant="outline" className="w-full bg-transparent">
                            <CameraOff className="mr-2 h-4 w-4" />
                            Stop Scanner
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div className="h-64 flex items-center justify-center bg-gray-100 rounded border">
                          <div className="text-center">
                            <QrCode className="h-16 w-16 mx-auto text-gray-400 mb-2" />
                            <p className="text-gray-600">Application Number Scanner Ready</p>
                            <p className="text-sm text-gray-500">Point camera at student's QR code</p>
                            <p className="text-xs text-gray-400 mt-2">
                              {availableStudents.length} students in database
                            </p>
                          </div>
                        </div>
                        <Button
                          onClick={startQRScanner}
                          className="w-full"
                          disabled={loading || availableStudents.length === 0}
                        >
                          <QrCode className="mr-2 h-4 w-4" />
                          {availableStudents.length === 0 ? "Add Students First" : "Start QR Code Scanner"}
                        </Button>
                      </div>
                    )}

                    <Separator />

                    {/* Manual Application Number Input */}
                    <div className="space-y-2">
                      <Label htmlFor="manualQR">Manual Application Number Input</Label>
                      <div className="flex gap-2">
                        <Input
                          id="manualQR"
                          value={manualQRData}
                          onChange={(e) => setManualQRData(e.target.value)}
                          placeholder="Enter Application Number (e.g: APP20241234)"
                          className="flex-1"
                        />
                        <Button
                          onClick={handleManualQRInput}
                          variant="outline"
                          disabled={availableStudents.length === 0}
                        >
                          Validate
                        </Button>
                      </div>
                      <p className="text-xs text-gray-500">Enter Application Number from Student App</p>
                    </div>

                    {/* Application Number Requirements */}
                    <Alert className="border-blue-200 bg-blue-50">
                      <AlertTriangle className="h-4 w-4 text-blue-600" />
                      <AlertDescription className="text-blue-800">
                        <strong>Connected to Same Database:</strong>
                        <ul className="list-disc list-inside text-xs mt-1 space-y-1">
                          <li>QR code contains student's Application Number</li>
                          <li>Scanner reads Application Number from QR code</li>
                          <li>System finds student details from same admin database</li>
                          <li>Face verification with stored student photo</li>
                        </ul>
                      </AlertDescription>
                    </Alert>
                  </>
                ) : (
                  <div className="text-center py-8">
                    <CheckCircle className="h-16 w-16 mx-auto text-green-600 mb-2" />
                    <p className="text-green-700 font-semibold">Application Number Successfully Validated!</p>
                    <p className="text-sm text-green-600">Student found in database - Proceed to face verification</p>
                    <Button onClick={resetStation} variant="outline" className="mt-4 bg-transparent">
                      Scan Different Application Number
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Student Details Display */}
            {currentStudent && qrValidated && (
              <Card className="border-2 border-blue-200 bg-blue-50">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <User className="h-5 w-5" />
                      Student Found in Database
                    </span>
                    <Button variant="ghost" size="sm" onClick={resetStation}>
                      <XCircle className="h-4 w-4" />
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <div className="relative">
                      <img
                        src={currentStudent.image_url || "/placeholder.svg"}
                        alt={currentStudent.name}
                        className="w-24 h-24 rounded-full border-4 border-blue-300 object-cover"
                      />
                      <Badge variant="secondary" className="absolute -bottom-2 -right-2 text-xs">
                        Reference Photo
                      </Badge>
                    </div>
                    <div className="space-y-2">
                      <h3 className="text-xl font-semibold text-blue-800">{currentStudent.name}</h3>
                      <Badge variant="outline" className="text-sm font-mono">
                        {currentStudent.application_number}
                      </Badge>
                      <p className="text-blue-700">
                        {currentStudent.class} - {currentStudent.department}
                      </p>
                      <Badge variant="default" className="text-xs bg-green-600">
                        ✅ Found in Database
                      </Badge>
                    </div>
                  </div>

                  <Separator />

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="font-medium text-blue-700">Phone:</p>
                      <p className="text-blue-600">{currentStudent.phone}</p>
                    </div>
                    <div>
                      <p className="font-medium text-blue-700">Schedule:</p>
                      <p className="text-blue-600 text-xs">{currentStudent.schedule || "Not assigned"}</p>
                    </div>
                  </div>

                  <Alert className="border-yellow-200 bg-yellow-50">
                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                    <AlertDescription className="text-yellow-800">
                      <strong>Next Step:</strong> Face verification required to match with stored photo above
                    </AlertDescription>
                  </Alert>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right Panel - Face Verification & Recent Entries */}
          <div className="space-y-4">
            {/* Face Verification Camera */}
            <Card className={verificationStatus === "success" ? "border-green-200 bg-green-50" : ""}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Camera className="h-5 w-5" />
                  Step 2: Face Verification
                  {verificationStatus === "success" && (
                    <Badge variant="secondary" className="ml-2">
                      ✅ Verified
                    </Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="relative bg-gray-100 rounded-lg overflow-hidden">
                  {cameraActive ? (
                    <div className="space-y-4">
                      <div className="relative">
                        <video ref={videoRef} className="w-full h-64 object-cover rounded" autoPlay muted />
                        <div className="absolute top-2 right-2 bg-black/50 text-white px-2 py-1 rounded text-xs">
                          Live Camera
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button
                          onClick={verifyFace}
                          disabled={isScanning || verificationStatus !== "scanning" || !qrValidated}
                          className="flex-1"
                        >
                          {isScanning ? "Analyzing Face..." : "Verify Face Match"}
                        </Button>
                        <Button onClick={stopCamera} variant="outline">
                          <CameraOff className="h-4 w-4" />
                        </Button>
                      </div>

                      {faceMatchScore !== null && (
                        <div className="text-center">
                          <p className="text-sm text-gray-600">Face Match Score: {faceMatchScore}%</p>
                          <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                            <div
                              className={`h-2 rounded-full ${faceMatchScore > 75 ? "bg-green-500" : "bg-red-500"}`}
                              style={{ width: `${faceMatchScore}%` }}
                            ></div>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="h-64 flex items-center justify-center text-gray-500">
                      <div className="text-center">
                        <Camera className="h-12 w-12 mx-auto mb-2 opacity-50" />
                        <p>Face Camera Ready</p>
                        <p className="text-sm">
                          {qrValidated ? "Click to start face verification" : "Scan Application Number first"}
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Face Verification Status */}
                <div className="mt-4 space-y-3">
                  {verificationStatus === "idle" && qrValidated && (
                    <Button onClick={startCamera} className="w-full" variant="default">
                      <Camera className="mr-2 h-4 w-4" />
                      Start Face Verification
                    </Button>
                  )}

                  {verificationStatus === "success" && (
                    <Alert className="border-green-200 bg-green-50">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <AlertDescription className="text-green-800">
                        ✅ Face Verification Successful! Entry Recorded.
                        {faceMatchScore && <span className="block text-sm">Match Score: {faceMatchScore}%</span>}
                      </AlertDescription>
                    </Alert>
                  )}

                  {verificationStatus === "failed" && (
                    <Alert className="border-red-200 bg-red-50">
                      <XCircle className="h-4 w-4 text-red-600" />
                      <AlertDescription className="text-red-800">
                        ❌ Face Verification Failed! Face doesn't match stored photo.
                        {faceMatchScore && <span className="block text-sm">Match Score: {faceMatchScore}%</span>}
                      </AlertDescription>
                    </Alert>
                  )}

                  {showTryAgain && (
                    <div className="space-y-3">
                      <Alert className="border-orange-200 bg-orange-50">
                        <AlertTriangle className="h-4 w-4 text-orange-600" />
                        <AlertDescription className="text-orange-800">
                          <strong>Verification Failed!</strong> Choose an option below:
                        </AlertDescription>
                      </Alert>

                      <div className="grid grid-cols-1 gap-2">
                        {verificationStatus === "failed" && qrValidated ? (
                          // Face verification failed, but QR is valid
                          <>
                            <Button onClick={tryAgainFace} variant="outline" className="w-full">
                              <Camera className="mr-2 h-4 w-4" />
                              Try Face Verification Again
                            </Button>
                            <Button onClick={tryAgainQR} variant="outline" className="w-full">
                              <QrCode className="mr-2 h-4 w-4" />
                              Scan Different QR Code
                            </Button>
                          </>
                        ) : (
                          // QR validation failed
                          <>
                            <Button onClick={tryAgainQR} variant="outline" className="w-full">
                              <QrCode className="mr-2 h-4 w-4" />
                              Try QR Scan Again
                            </Button>
                          </>
                        )}
                        <Button onClick={resetStation} variant="destructive" className="w-full">
                          <RotateCcw className="mr-2 h-4 w-4" />
                          Reset Station
                        </Button>
                      </div>
                    </div>
                  )}

                  {!qrValidated && (
                    <Alert className="border-yellow-200 bg-yellow-50">
                      <AlertTriangle className="h-4 w-4 text-yellow-600" />
                      <AlertDescription className="text-yellow-800">
                        Please scan and validate an Application Number first before face verification.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Recent Entries */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Recent Entries
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 max-h-64 overflow-y-auto">
                  {recentEntries.length === 0 ? (
                    <p className="text-gray-500 text-center py-4">No entries yet</p>
                  ) : (
                    recentEntries.map((log) => (
                      <div key={log.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <p className="font-medium">{log.student_name}</p>
                          <p className="text-sm text-gray-600">{formatDateTime(log.entryTime)}</p>
                          <p className="text-xs text-gray-500">App: {log.application_number}</p>
                        </div>
                        <Badge variant={log.status === "entry" ? "default" : "secondary"}>
                          {log.status === "entry" ? "🟢 Entry" : "🔴 Exit"}
                        </Badge>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Database Connection & System Integration</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-purple-700 mb-2">Same Database Connection:</h3>
                <ol className="list-decimal list-inside space-y-1 text-sm">
                  <li>Station connects to same database as Admin Panel</li>
                  <li>Students added in Admin are instantly available here</li>
                  <li>Entry logs are shared across both systems</li>
                  <li>Real-time data synchronization</li>
                  <li>Fallback to local storage if database unavailable</li>
                  <li>Automatic data sync when connection restored</li>
                </ol>
              </div>
              <div>
                <h3 className="font-semibold text-green-700 mb-2">Professional Station Features:</h3>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Dedicated website for security staff</li>
                  <li>No login required - direct access</li>
                  <li>Real-time QR code scanning</li>
                  <li>Live face verification system</li>
                  <li>Automatic entry/exit logging</li>
                  <li>Professional security interface</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
