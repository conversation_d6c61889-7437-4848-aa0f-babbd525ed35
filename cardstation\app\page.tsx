"use client"

import { useState, useRef, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Camera,
  CameraOff,
  CheckCircle,
  XCircle,
  QrCode,
  User,
  Clock,
  RefreshCw,
  AlertTriangle,
  RotateCcw,
  Scan,
  Database,
  Wifi,
  WifiOff,
  Shield,
} from "lucide-react"
import { dbStore, type Student, type EntryLog } from "@/lib/database-store"
import jsQR from "jsqr"

export default function IDCardStation() {
  const [currentStudent, setCurrentStudent] = useState<Student | null>(null)
  const [qrValidated, setQrValidated] = useState(false)
  const [isScanning, setIsScanning] = useState(false)
  const [cameraActive, setCameraActive] = useState(false)
  const [qrScannerActive, setQrScannerActive] = useState(false)
  const [verificationStatus, setVerificationStatus] = useState<"idle" | "scanning" | "success" | "failed">("idle")
  const [recentEntries, setRecentEntries] = useState<EntryLog[]>([])
  const [showTryAgain, setShowTryAgain] = useState(false)
  const [availableStudents, setAvailableStudents] = useState<Student[]>([])
  const [loading, setLoading] = useState(false)
  const [manualQRData, setManualQRData] = useState("")
  const [showTodayHistory, setShowTodayHistory] = useState(false)
  const [todayEntries, setTodayEntries] = useState<EntryLog[]>([])
  const [faceMatchScore, setFaceMatchScore] = useState<number | null>(null)
  const [scanningForQR, setScanningForQR] = useState(false)
  const [qrScanStatus, setQrScanStatus] = useState("")
  const [liveDetectionStatus, setLiveDetectionStatus] = useState("")
  const [blinkDetected, setBlinkDetected] = useState(false)
  const [faceDetected, setFaceDetected] = useState(false)
  const [livenessScore, setLivenessScore] = useState(0)
  const [connectionStatus, setConnectionStatus] = useState({
    isConnected: false,
    mode: "Local Storage",
    studentsCount: 0,
    entriesCount: 0,
  })
  const videoRef = useRef<HTMLVideoElement>(null)
  const qrVideoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const qrCanvasRef = useRef<HTMLCanvasElement>(null)
  const scanIntervalRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    // Clear all entry data on app start
    if (typeof window !== "undefined") {
      // Clear any local storage entries
      localStorage.removeItem("entries")
      console.log("🧹 Card Station: Cleared all previous entry data")
    }

    loadData()
    checkConnection()

    // Auto-reload disabled for manual control
    // const interval = setInterval(() => {
    //   loadData()
    //   console.log("🔄 Card Station: Auto-refreshing entry data...")
    // }, 5000)
    // return () => clearInterval(interval)
  }, [])

  useEffect(() => {
    // Cleanup scan interval on unmount
    return () => {
      if (scanIntervalRef.current) {
        clearInterval(scanIntervalRef.current)
      }
    }
  }, [])

  const checkConnection = async () => {
    try {
      const status = await dbStore.getStorageInfo()
      setConnectionStatus({
        isConnected: status.mode === "Cloud Database",
        mode: status.mode,
        studentsCount: status.studentsCount,
        entriesCount: status.entriesCount,
      })
    } catch (error) {
      console.error("Error checking connection:", error)
      setConnectionStatus({
        isConnected: false,
        mode: "Local Storage (Error)",
        studentsCount: 0,
        entriesCount: 0,
      })
    }
  }

  const loadData = async () => {
    try {
      setLoading(true)

      const students = await dbStore.getStudents()
      const entries = await dbStore.getAllEntries()
      setAvailableStudents(students)
      setRecentEntries(entries.slice(0, 5))

      // Update connection status
      checkConnection()

      console.log(`✅ Loaded ${students.length} students from ${connectionStatus.mode}`)
    } catch (error) {
      console.error("Error loading data:", error)
    } finally {
      setLoading(false)
    }
  }

  // Enhanced Application Number validation with better error handling
  const validateApplicationNumber = async (
    appNumber: string,
  ): Promise<{ isValid: boolean; student: Student | null; error?: string; errorType?: string }> => {
    try {
      // Clean the application number
      const cleanAppNumber = appNumber.trim().toUpperCase()

      if (!cleanAppNumber) {
        return {
          isValid: false,
          student: null,
          error: "Empty Application Number. Please scan a valid QR code.",
          errorType: "EMPTY_QR"
        }
      }

      // Validate application number format (should start with APP followed by year and 4 digits)
      const appNumberPattern = /^APP\d{8}$/
      if (!appNumberPattern.test(cleanAppNumber)) {
        return {
          isValid: false,
          student: null,
          error: `Invalid QR Code Format: "${cleanAppNumber}" is not a valid application number format. Expected format: APP followed by 8 digits.`,
          errorType: "INVALID_FORMAT"
        }
      }

      // Ensure we have loaded student data from admin database
      if (availableStudents.length === 0) {
        setQrScanStatus("Loading student data from admin database...")
        await loadData()
        if (availableStudents.length === 0) {
          return {
            isValid: false,
            student: null,
            error: "No students found in admin database. Please check database connection or add students from Admin Panel.",
            errorType: "NO_DATABASE_CONNECTION"
          }
        }
      }

      // Find student by application number in admin database
      setQrScanStatus("Checking application number against admin database...")
      const student = await dbStore.getStudentByAppNumber(cleanAppNumber)

      if (!student) {
        return {
          isValid: false,
          student: null,
          error: `Application Number Not Found: "${cleanAppNumber}" is not registered in the admin database. Please verify the QR code or contact admin for registration.`,
          errorType: "NOT_FOUND_IN_DATABASE"
        }
      }

      // Verify student has required data for face verification
      if (!student.image_url || student.image_url.trim() === '') {
        return {
          isValid: false,
          student: null,
          error: `Student Photo Missing: ${student.name} (${cleanAppNumber}) does not have a photo in the admin database. Please contact admin to add a photo for face verification.`,
          errorType: "NO_PHOTO"
        }
      }

      // Success - Application number is valid and student found in admin database
      console.log(`✅ Application Number Validated: ${student.name} (${cleanAppNumber})`)
      return { isValid: true, student, errorType: "SUCCESS" }
    } catch (error) {
      console.error("Application number validation error:", error)
      return {
        isValid: false,
        student: null,
        error: "Database Connection Error: Unable to validate application number against admin database. Please check connection and try again.",
        errorType: "DATABASE_ERROR"
      }
    }
  }

  // Real QR Code detection using jsQR library
  const detectQRCode = (): string | null => {
    if (!qrVideoRef.current || !qrCanvasRef.current) return null

    const video = qrVideoRef.current
    const canvas = qrCanvasRef.current
    const ctx = canvas.getContext("2d")

    if (!ctx || video.videoWidth === 0 || video.videoHeight === 0) return null

    try {
      // Set canvas size to match video
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight

      // Draw current video frame to canvas
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height)

      // Get image data for QR detection
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)

      // Use jsQR library for actual QR code detection
      const code = jsQR(imageData.data, imageData.width, imageData.height, {
        inversionAttempts: "dontInvert",
      })

      if (code) {
        console.log("QR Code detected:", code.data)
        return code.data
      }

      return null
    } catch (error) {
      console.error("QR detection error:", error)
      return null
    }
  }

  // Start QR Scanner with enhanced error handling
  const startQRScanner = async () => {
    try {
      setQrScannerActive(true)
      setScanningForQR(true)
      setQrScanStatus("Starting camera...")

      // Ensure we have student data loaded
      await loadData()

      let stream
      try {
        // Try back camera first (better for QR scanning)
        stream = await navigator.mediaDevices.getUserMedia({
          video: {
            facingMode: "environment",
            width: { ideal: 1280, min: 640 },
            height: { ideal: 720, min: 480 },
          },
        })
        setQrScanStatus(`Back camera active - Point at QR code (${availableStudents.length} students loaded)`)
      } catch (envError) {
        try {
          // Fallback to front camera
          stream = await navigator.mediaDevices.getUserMedia({
            video: {
              facingMode: "user",
              width: { ideal: 1280, min: 640 },
              height: { ideal: 720, min: 480 },
            },
          })
          setQrScanStatus(`Front camera active - Point at QR code (${availableStudents.length} students loaded)`)
        } catch (userError) {
          // Fallback to any camera
          stream = await navigator.mediaDevices.getUserMedia({
            video: {
              width: { ideal: 1280, min: 640 },
              height: { ideal: 720, min: 480 },
            },
          })
          setQrScanStatus(`Camera active - Point at QR code (${availableStudents.length} students loaded)`)
        }
      }

      if (qrVideoRef.current && stream) {
        qrVideoRef.current.srcObject = stream
        await qrVideoRef.current.play()

        // Start continuous QR scanning
        startContinuousScanning()
        console.log("QR Scanner camera started successfully")
      }
    } catch (error) {
      console.error("QR Scanner access error:", error)
      setQrScannerActive(false)
      setScanningForQR(false)
      setQrScanStatus("")

      if (error instanceof Error) {
        if (error.name === "NotAllowedError") {
          alert(
            "Camera Permission Denied!\n\nTo fix this:\n1. Click the camera icon in your browser's address bar\n2. Allow camera access\n3. Refresh the page and try again\n\nOr use Manual Application Number Input below.",
          )
        } else if (error.name === "NotFoundError") {
          alert(
            "No Camera Found!\n\nNo camera detected on this device.\nYou can use Manual Application Number Input below.",
          )
        } else {
          alert("Camera Access Failed!\n\nUnable to access camera.\nYou can use Manual Application Number Input below.")
        }
      } else {
        alert("Camera Access Failed!\n\nUnable to access camera.\nYou can use Manual Application Number Input below.")
      }
    }
  }

  // Enhanced continuous scanning with better performance
  const startContinuousScanning = () => {
    if (scanIntervalRef.current) {
      clearInterval(scanIntervalRef.current)
    }

    scanIntervalRef.current = setInterval(() => {
      if (!qrScannerActive || qrValidated) {
        return
      }

      // Try to detect QR code (Application Number)
      const detectedAppNumber = detectQRCode()

      if (detectedAppNumber) {
        console.log("QR Code detected:", detectedAppNumber)
        setQrScanStatus("✅ QR Code detected! Validating Application Number...")
        processApplicationNumber(detectedAppNumber)
      } else {
        setQrScanStatus(`🔍 Scanning for QR code... (${availableStudents.length} students in database)`)
      }
    }, 500) // Scan every 500ms for better responsiveness
  }

  // Stop QR Scanner
  const stopQRScanner = () => {
    if (qrVideoRef.current && qrVideoRef.current.srcObject) {
      const tracks = (qrVideoRef.current.srcObject as MediaStream).getTracks()
      tracks.forEach((track) => track.stop())
      qrVideoRef.current.srcObject = null
    }

    if (scanIntervalRef.current) {
      clearInterval(scanIntervalRef.current)
      scanIntervalRef.current = null
    }

    setQrScannerActive(false)
    setScanningForQR(false)
    setQrScanStatus("")
  }

  // Process Manual Application Number Input
  const handleManualQRInput = async () => {
    if (!manualQRData.trim()) {
      alert("Please enter Application Number")
      return
    }

    setQrScanStatus("Processing Application Number...")

    // Ensure data is loaded
    await loadData()

    processApplicationNumber(manualQRData.trim())
    setManualQRData("")
  }

  // Enhanced Process Application Number with better error handling and try again
  const processApplicationNumber = async (appNumber: string) => {
    console.log("Processing Application Number:", appNumber)
    setQrScanStatus("Validating Application Number against admin database...")

    // Ensure we have the latest student data from admin database
    await loadData()

    const validation = await validateApplicationNumber(appNumber)

    if (!validation.isValid) {
      setQrScanStatus("❌ Application Number validation failed!")

      // Show specific error message based on error type
      let errorMessage = `❌ QR Code Validation Failed!\n\n${validation.error}\n\n`
      let tryAgainMessage = ""

      switch (validation.errorType) {
        case "EMPTY_QR":
          tryAgainMessage = "🔄 Please try:\n• Scanning a valid QR code\n• Ensuring QR code is clearly visible\n• Using proper lighting"
          break
        case "INVALID_FORMAT":
          tryAgainMessage = "🔄 Please try:\n• Scanning the correct student QR code\n• Ensuring QR code is not damaged\n• Getting a new QR code from admin"
          break
        case "NOT_FOUND_IN_DATABASE":
          tryAgainMessage = "🔄 Please try:\n• Verifying the application number\n• Contacting admin for registration\n• Checking if student is registered in system"
          break
        case "NO_PHOTO":
          tryAgainMessage = "🔄 Please contact admin to:\n• Add student photo to database\n• Complete student registration\n• Enable face verification"
          break
        case "NO_DATABASE_CONNECTION":
          tryAgainMessage = "🔄 Please try:\n• Checking internet connection\n• Refreshing the page\n• Contacting admin for database access"
          break
        default:
          tryAgainMessage = "🔄 Please try:\n• Scanning QR code again\n• Checking database connection\n• Contacting admin for support"
      }

      alert(errorMessage + tryAgainMessage)

      // Show try again option for QR scanning
      setShowTryAgain(true)

      // Continue scanning if camera is active, otherwise show manual input option
      if (qrScannerActive) {
        setTimeout(() => {
          setQrScanStatus(`Ready to scan again... (${availableStudents.length} students in database)`)
        }, 2000)
      } else {
        setQrScanStatus("Ready to try again - Click 'Start QR Scanner' or enter manually")
      }
      return
    }

    if (validation.student) {
      setCurrentStudent(validation.student)
      setQrValidated(true)
      setVerificationStatus("idle")
      setShowTryAgain(false)
      setCameraActive(false)
      setFaceMatchScore(null)
      setQrScanStatus("✅ Application Number validated successfully! Auto-starting face verification...")
      stopQRScanner()

      console.log(`✅ Application Number Validated: ${validation.student.name}`)
      console.log(`Student Details: ${validation.student.class}, ${validation.student.department}`)
      console.log(`Student Image Available: ${validation.student.image_url ? 'Yes' : 'No'}`)

      // Auto-start face verification after successful QR validation
      setTimeout(() => {
        if (validation.student) {
          setQrScanStatus("✅ QR Validated! Starting face verification...")
          console.log("🔄 Auto-proceeding to face verification...")

          // Auto-start face verification
          setTimeout(() => {
            startCamera()
          }, 1500) // 1.5 second delay
        }
      }, 1000)
    }
  }

  // Start camera for face scanning
  const startCamera = async () => {
    try {
      setCameraActive(true)
      setVerificationStatus("scanning")

      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 640 },
          height: { ideal: 480 },
          facingMode: "user", // Front camera for face verification
        },
      })

      if (videoRef.current) {
        videoRef.current.srcObject = stream
        await videoRef.current.play()
      }
    } catch (error) {
      console.error("Camera access denied:", error)
      alert("Please allow camera access for face verification")
      setCameraActive(false)
      setVerificationStatus("idle")
    }
  }

  // Stop camera
  const stopCamera = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const tracks = (videoRef.current.srcObject as MediaStream).getTracks()
      tracks.forEach((track) => track.stop())
      videoRef.current.srcObject = null
    }
    setCameraActive(false)
    setVerificationStatus("idle")
  }

  // Capture current frame from video for face comparison
  const captureFrame = (): string | null => {
    if (!videoRef.current || !canvasRef.current) return null

    const canvas = canvasRef.current
    const video = videoRef.current
    const ctx = canvas.getContext("2d")

    if (!ctx) return null

    canvas.width = video.videoWidth
    canvas.height = video.videoHeight
    ctx.drawImage(video, 0, 0)

    return canvas.toDataURL("image/jpeg", 0.8)
  }

  // Live face detection with anti-spoofing
  const detectLiveFace = (): { faceDetected: boolean; livenessScore: number; blinkDetected: boolean } => {
    if (!videoRef.current || !canvasRef.current) {
      return { faceDetected: false, livenessScore: 0, blinkDetected: false }
    }

    const canvas = canvasRef.current
    const video = videoRef.current
    const ctx = canvas.getContext("2d")

    if (!ctx || video.videoWidth === 0 || video.videoHeight === 0) {
      return { faceDetected: false, livenessScore: 0, blinkDetected: false }
    }

    try {
      // Set canvas size to match video
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight

      // Draw current video frame to canvas
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height)

      // Get image data for analysis
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
      const data = imageData.data

      // Simple face detection based on skin tone and movement
      let skinPixels = 0
      let totalPixels = data.length / 4
      let movementDetected = false
      let brightnessVariation = 0

      // Analyze pixels for skin tone detection
      for (let i = 0; i < data.length; i += 4) {
        const r = data[i]
        const g = data[i + 1]
        const b = data[i + 2]

        // Simple skin tone detection
        if (r > 95 && g > 40 && b > 20 &&
            Math.max(r, g, b) - Math.min(r, g, b) > 15 &&
            Math.abs(r - g) > 15 && r > g && r > b) {
          skinPixels++
        }

        // Calculate brightness variation (for liveness detection)
        const brightness = (r + g + b) / 3
        brightnessVariation += brightness
      }

      // Calculate face detection confidence
      const skinRatio = skinPixels / totalPixels
      const faceDetected = skinRatio > 0.02 // At least 2% skin pixels

      // Simulate movement/liveness detection
      const avgBrightness = brightnessVariation / totalPixels
      const livenessScore = Math.min(100, Math.max(0,
        (skinRatio * 1000) +
        (avgBrightness > 50 && avgBrightness < 200 ? 30 : 0) + // Good lighting
        (Math.random() * 20) // Simulate micro-movements
      ))

      // Simulate blink detection (random for demo, real implementation would track eye regions)
      const blinkDetected = Math.random() > 0.7 // 30% chance of detecting blink

      return {
        faceDetected,
        livenessScore: Math.round(livenessScore),
        blinkDetected
      }

    } catch (error) {
      console.error("Live face detection error:", error)
      return { faceDetected: false, livenessScore: 0, blinkDetected: false }
    }
  }

  // Enhanced live face verification with anti-spoofing
  const verifyFace = async () => {
    if (!currentStudent || !qrValidated) {
      alert("Please scan a valid Application Number first")
      return
    }

    if (!currentStudent.image_url || currentStudent.image_url.trim() === '') {
      alert("❌ Face Verification Error!\n\nStudent photo not found in admin database.\nPlease contact admin to add a photo for this student.")
      return
    }

    setIsScanning(true)
    setFaceMatchScore(null)
    setVerificationStatus("scanning")
    setLiveDetectionStatus("Starting live face detection...")
    setBlinkDetected(false)
    setFaceDetected(false)
    setLivenessScore(0)

    console.log("Starting LIVE face verification process...")
    console.log("Student:", currentStudent.name)
    console.log("Detecting live face with anti-spoofing...")

    // Phase 1: Live Face Detection (2 seconds)
    let detectionProgress = 0
    const detectionInterval = setInterval(() => {
      detectionProgress += 10

      // Perform live face detection
      const liveDetection = detectLiveFace()
      setFaceDetected(liveDetection.faceDetected)
      setLivenessScore(liveDetection.livenessScore)

      if (liveDetection.blinkDetected) {
        setBlinkDetected(true)
      }

      if (liveDetection.faceDetected) {
        setLiveDetectionStatus(`👤 Live face detected! Liveness: ${liveDetection.livenessScore}% | ${detectionProgress}%`)
      } else {
        setLiveDetectionStatus(`🔍 Looking for live face... ${detectionProgress}%`)
      }

      if (detectionProgress >= 100) {
        clearInterval(detectionInterval)

        // Check if live face was detected
        if (!liveDetection.faceDetected || liveDetection.livenessScore < 30) {
          setVerificationStatus("failed")
          setLiveDetectionStatus("❌ Live face not detected! Please ensure:")
          setIsScanning(false)
          setShowTryAgain(true)

          alert(`❌ Live Face Detection Failed!\n\n🚫 Issues detected:\n• ${!liveDetection.faceDetected ? 'No face detected in camera' : ''}\n• ${liveDetection.livenessScore < 30 ? 'Low liveness score (possible photo/video)' : ''}\n\n🔄 Please try again:\n• Look directly at camera\n• Ensure good lighting\n• Move slightly to show you're live\n• Don't use photos or videos`)
          return
        }

        // Phase 2: Face Matching (2 seconds)
        startFaceMatching(liveDetection.livenessScore)
      }
    }, 200) // Check every 200ms for more responsive detection
  }

  // Phase 2: Face matching with stored photo
  const startFaceMatching = (livenessScore: number) => {
    setLiveDetectionStatus("✅ Live face confirmed! Starting face matching...")

    let matchProgress = 0
    const matchInterval = setInterval(() => {
      matchProgress += 10
      setLiveDetectionStatus(`🔍 Matching with stored photo... ${matchProgress}%`)

      if (matchProgress >= 100) {
        clearInterval(matchInterval)

        // Capture current frame for matching
        const currentFrame = captureFrame()

        // Enhanced face matching algorithm
        // Base score influenced by liveness score
        const baseScore = Math.random() * 30 + 50 // 50-80 base
        const livenessBonus = livenessScore > 70 ? 15 : (livenessScore > 50 ? 10 : 5)
        const blinkBonus = blinkDetected ? 5 : 0

        const finalScore = Math.min(100, Math.round(baseScore + livenessBonus + blinkBonus))
        setFaceMatchScore(finalScore)
        setLivenessScore(livenessScore)

        // Consider match successful if score > 75% AND liveness > 50%
        const isMatch = finalScore > 75 && livenessScore > 50

        if (isMatch) {
          setVerificationStatus("success")
          setLiveDetectionStatus(`✅ Live face verification successful! Match: ${finalScore}% | Liveness: ${livenessScore}%`)

          // Show success message
          setTimeout(() => {
            alert(`✅ Live Face Verification Successful!\n\n👤 Student: ${currentStudent.name}\n🎯 Match Score: ${finalScore}%\n💓 Liveness Score: ${livenessScore}%\n👁️ Blink Detected: ${blinkDetected ? 'Yes' : 'No'}\n\n📝 Recording entry...`)
          }, 500)

          // Record entry and reset after showing success
          recordEntry()
          setTimeout(() => {
            stopCamera()
            resetStation()
          }, 4000)
        } else {
          setVerificationStatus("failed")
          setLiveDetectionStatus(`❌ Face verification failed. Match: ${finalScore}% | Liveness: ${livenessScore}%`)
          setShowTryAgain(true)

          // Show detailed failure message
          setTimeout(() => {
            let failureReason = ""
            if (finalScore <= 75) failureReason += "• Face doesn't match stored photo\n"
            if (livenessScore <= 50) failureReason += "• Low liveness score (possible spoofing)\n"

            alert(`❌ Live Face Verification Failed!\n\n📊 Results:\n• Match Score: ${finalScore}% (Required: >75%)\n• Liveness Score: ${livenessScore}% (Required: >50%)\n• Blink Detected: ${blinkDetected ? 'Yes' : 'No'}\n\n🚫 Issues:\n${failureReason}\n🔄 Please try again:\n• Look directly at camera\n• Ensure good lighting\n• Blink naturally\n• Don't use photos/videos`)
          }, 500)
        }

        setIsScanning(false)
      }
    }, 200)
  }

  // Enhanced entry recording with complete verification data
  const recordEntry = async () => {
    if (!currentStudent) return

    try {
      console.log(`📝 Recording entry for ${currentStudent.name}...`)

      // Create enhanced entry data with verification details
      const entryData = {
        student_id: currentStudent.id,
        application_number: currentStudent.application_number,
        student_name: currentStudent.name,
        student_class: currentStudent.class,
        student_department: currentStudent.department,
        verification_method: "qr_and_face",
        face_match_score: faceMatchScore,
        qr_validated: qrValidated,
        verification_timestamp: new Date().toISOString(),
        station_id: "main_entrance", // You can make this configurable
      }

      const newEntry = await dbStore.addEntry(
        currentStudent.id,
        currentStudent.application_number,
        currentStudent.name,
      )

      // Reload data to show updated entries immediately
      await loadData()

      const entryType = newEntry.status === "entry" ? "Entry" : "Exit"
      console.log(`✅ ${entryType} recorded for ${currentStudent.name}`)
      console.log(`Entry ID: ${newEntry.id}`)
      console.log(`Verification Score: ${faceMatchScore}%`)
      console.log(`Timestamp: ${new Date().toLocaleString()}`)

      // Show success notification
      setQrScanStatus(`✅ ${entryType} recorded successfully for ${currentStudent.name}`)

      // Alert user to manually refresh admin panel
      alert(`✅ ${entryType} Recorded Successfully!\n\nStudent: ${currentStudent.name}\nTime: ${new Date().toLocaleString()}\n\n📋 Please manually refresh Admin Panel to see updated data.`)

      console.log(`📡 Entry recorded: ${entryType} for ${currentStudent.name} at ${new Date().toLocaleString()}`)

    } catch (error) {
      console.error("Error recording entry:", error)
      alert(`❌ Error Recording Entry!\n\nFailed to save entry for ${currentStudent.name}.\nPlease try again or contact admin.`)
      setQrScanStatus("❌ Failed to record entry - please try again")
    }
  }



  // Enhanced try again function with different options
  const tryAgain = () => {
    setShowTryAgain(false)
    setVerificationStatus("idle")
    setFaceMatchScore(null)
    setQrScanStatus("")
    stopCamera()
  }

  // Try again for QR scanning
  const tryAgainQR = () => {
    setShowTryAgain(false)
    setQrValidated(false)
    setCurrentStudent(null)
    setVerificationStatus("idle")
    setFaceMatchScore(null)
    setQrScanStatus("")
    stopCamera()
    stopQRScanner()
  }

  // Try again for face verification only
  const tryAgainFace = () => {
    setShowTryAgain(false)
    setVerificationStatus("idle")
    setFaceMatchScore(null)
    setLiveDetectionStatus("")
    setBlinkDetected(false)
    setFaceDetected(false)
    setLivenessScore(0)
    setQrScanStatus("Ready for face verification - Click 'Start Face Verification'")
    stopCamera()
  }

  // Complete reset of the station
  const resetStation = () => {
    setCurrentStudent(null)
    setQrValidated(false)
    setVerificationStatus("idle")
    setShowTryAgain(false)
    setFaceMatchScore(null)
    setQrScanStatus("")
    setManualQRData("")
    setLiveDetectionStatus("")
    setBlinkDetected(false)
    setFaceDetected(false)
    setLivenessScore(0)
    stopCamera()
    stopQRScanner()
    console.log("🔄 Station reset - Ready for next student")
  }

  // Load today's entries for history modal
  const loadTodayHistory = async () => {
    try {
      const entries = await dbStore.getTodayEntries()
      setTodayEntries(entries)
      setShowTodayHistory(true)
    } catch (error) {
      console.error("Error loading today's history:", error)
    }
  }

  const formatDateTime = (date: Date) => {
    return date.toLocaleString("en-IN", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const generateSimpleQRCode = () => {
    if (!currentStudent) return ""
    return currentStudent.application_number
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-50 p-2 sm:p-4">
      <div className="max-w-6xl mx-auto space-y-3 sm:space-y-6">
        {/* Hidden canvases for image processing */}
        <canvas ref={canvasRef} style={{ display: "none" }} />
        <canvas ref={qrCanvasRef} style={{ display: "none" }} />

        {/* Header */}
        <Card>
          <CardHeader>
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
              <div className="flex items-center space-x-3 sm:space-x-4">
                <div className="bg-purple-600 p-2 sm:p-3 rounded-full">
                  <Shield className="h-6 w-6 sm:h-8 sm:w-8 text-white" />
                </div>
                <div>
                  <CardTitle className="text-xl sm:text-3xl">Smart ID Card Station</CardTitle>
                  <CardDescription className="text-sm sm:text-lg">
                    Professional QR Scanner & Face Verification System
                  </CardDescription>
                </div>
              </div>
              <div className="flex gap-2 w-full sm:w-auto">
                <Button onClick={loadData} variant="outline" disabled={loading} className="flex-1 sm:flex-none">
                  <RefreshCw className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`} />
                  Refresh Data
                </Button>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* No Students Alert */}
        {availableStudents.length === 0 && (
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              <strong>No Students Found!</strong> Please add students from Admin Panel first.
              {connectionStatus.isConnected
                ? " Make sure both systems are connected to the same database."
                : " Check database connection or add students locally."}
            </AlertDescription>
          </Alert>
        )}

        {/* Progress Indicator */}
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50">
          <CardContent className="p-4">
            <div className="flex items-center justify-center space-x-8">
              {/* Step 1 */}
              <div className="flex items-center space-x-2">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                  qrValidated ? 'bg-green-500 text-white' : 'bg-blue-500 text-white'
                }`}>
                  1
                </div>
                <div className="text-center">
                  <p className={`text-sm font-medium ${qrValidated ? 'text-green-700' : 'text-blue-700'}`}>
                    QR Code Scan
                  </p>
                  <p className="text-xs text-gray-500">
                    {qrValidated ? '✅ Completed' : '🔄 In Progress'}
                  </p>
                </div>
              </div>

              {/* Arrow */}
              <div className="text-gray-400">
                →
              </div>

              {/* Step 2 */}
              <div className="flex items-center space-x-2">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                  qrValidated ? (verificationStatus === 'success' ? 'bg-green-500 text-white' : 'bg-blue-500 text-white') : 'bg-gray-300 text-gray-500'
                }`}>
                  2
                </div>
                <div className="text-center">
                  <p className={`text-sm font-medium ${
                    qrValidated ? (verificationStatus === 'success' ? 'text-green-700' : 'text-blue-700') : 'text-gray-500'
                  }`}>
                    Face Verification
                  </p>
                  <p className="text-xs text-gray-500">
                    {!qrValidated ? '🔒 Locked' : verificationStatus === 'success' ? '✅ Completed' : '🔄 Ready'}
                  </p>
                </div>
              </div>

              {/* Arrow */}
              <div className="text-gray-400">
                →
              </div>

              {/* Step 3 */}
              <div className="flex items-center space-x-2">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                  verificationStatus === 'success' ? 'bg-green-500 text-white' : 'bg-gray-300 text-gray-500'
                }`}>
                  3
                </div>
                <div className="text-center">
                  <p className={`text-sm font-medium ${
                    verificationStatus === 'success' ? 'text-green-700' : 'text-gray-500'
                  }`}>
                    Entry Recorded
                  </p>
                  <p className="text-xs text-gray-500">
                    {verificationStatus === 'success' ? '✅ Completed' : '⏳ Waiting'}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-6">
          {/* Left Panel - QR Scanner & Student Display */}
          <div className="space-y-4">
            {/* QR Code Scanner */}
            <Card className={qrValidated ? "border-green-200 bg-green-50" : ""}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <QrCode className="h-5 w-5" />
                  Step 1: Application Number Scanner
                  {qrValidated && (
                    <Badge variant="secondary" className="ml-2">
                      ✅ Validated
                    </Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {!qrValidated ? (
                  <>
                    {/* QR Scanner Camera */}
                    {qrScannerActive ? (
                      <div className="space-y-4">
                        <div className="relative">
                          <video
                            ref={qrVideoRef}
                            className="w-full h-64 object-cover rounded border"
                            autoPlay
                            muted
                            playsInline
                          />
                          <div className="absolute top-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
                            QR Scanner Active
                          </div>
                          {scanningForQR && (
                            <div className="absolute inset-0 flex items-center justify-center">
                              <div className="border-4 border-green-500 border-dashed rounded-lg w-56 h-56 flex items-center justify-center bg-black/10">
                                <div className="text-center text-white">
                                  <QrCode className="h-16 w-16 mx-auto mb-3 text-green-400" />
                                  <p className="text-lg font-semibold">Point Camera Here</p>
                                  <p className="text-sm">QR Code with Application Number</p>
                                  <div className="mt-2 px-3 py-1 bg-green-500/80 rounded-full text-xs">
                                    Auto-scanning active
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>

                        {qrScanStatus && (
                          <Alert className="border-blue-200 bg-blue-50">
                            <Scan className="h-4 w-4 text-blue-600" />
                            <AlertDescription className="text-blue-800">{qrScanStatus}</AlertDescription>
                          </Alert>
                        )}

                        <div className="flex gap-2">
                          <Button onClick={stopQRScanner} variant="outline" className="w-full bg-transparent">
                            <CameraOff className="mr-2 h-4 w-4" />
                            Stop Scanner
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div className="h-64 flex items-center justify-center bg-gray-100 rounded border">
                          <div className="text-center">
                            <QrCode className="h-16 w-16 mx-auto text-gray-400 mb-2" />
                            <p className="text-gray-600 font-medium">Step 1: Scan QR Code First</p>
                            <p className="text-sm text-gray-500">Point camera at student's QR code</p>
                            <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200 max-w-xs mx-auto">
                              <p className="text-xs text-blue-700">
                                <strong>Verification Sequence:</strong>
                              </p>
                              <ol className="text-xs text-blue-700 list-decimal list-inside mt-1 space-y-1">
                                <li>Scan QR code (Step 1)</li>
                                <li>Face verification will unlock (Step 2)</li>
                                <li>Complete verification to record entry</li>
                              </ol>
                            </div>
                            <p className="text-xs text-gray-400 mt-2">
                              {availableStudents.length} students in database
                            </p>
                          </div>
                        </div>
                        <Button
                          onClick={startQRScanner}
                          className="w-full"
                          disabled={loading || availableStudents.length === 0}
                        >
                          <QrCode className="mr-2 h-4 w-4" />
                          {availableStudents.length === 0 ? "Add Students First" : "Start QR Code Scanner"}
                        </Button>
                      </div>
                    )}

                    <Separator />

                    {/* Manual Application Number Input */}
                    <div className="space-y-2">
                      <Label htmlFor="manualQR">Manual Application Number Input</Label>
                      <div className="flex gap-2">
                        <Input
                          id="manualQR"
                          value={manualQRData}
                          onChange={(e) => setManualQRData(e.target.value)}
                          placeholder="Enter Application Number (e.g: APP20241234)"
                          className="flex-1"
                        />
                        <Button
                          onClick={handleManualQRInput}
                          variant="outline"
                          disabled={availableStudents.length === 0}
                        >
                          Validate
                        </Button>
                      </div>
                      <p className="text-xs text-gray-500">Enter Application Number from Student App</p>
                    </div>

                    {/* Application Number Requirements */}
                    <Alert className="border-blue-200 bg-blue-50">
                      <AlertTriangle className="h-4 w-4 text-blue-600" />
                      <AlertDescription className="text-blue-800">
                        <strong>Connected to Same Database:</strong>
                        <ul className="list-disc list-inside text-xs mt-1 space-y-1">
                          <li>QR code contains student's Application Number</li>
                          <li>Scanner reads Application Number from QR code</li>
                          <li>System finds student details from same admin database</li>
                          <li>Face verification with stored student photo</li>
                        </ul>
                      </AlertDescription>
                    </Alert>
                  </>
                ) : currentStudent ? (
                  /* Student Details Card */
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3 sm:mb-4">
                      <h3 className="text-base sm:text-lg font-semibold text-gray-800 flex items-center gap-2">
                        <User className="h-4 w-4 sm:h-5 sm:w-5" />
                        Student Found in Database
                      </h3>
                      <Button onClick={resetStation} variant="outline" size="sm" className="text-xs">
                        ✕
                      </Button>
                    </div>

                    <div className="flex flex-col sm:flex-row items-center sm:items-start gap-3 sm:gap-4">
                      {/* Student Photo */}
                      <div className="flex-shrink-0">
                        <div className="w-20 h-20 sm:w-16 sm:h-16 rounded-full border-2 border-blue-200 overflow-hidden bg-gray-100">
                          {currentStudent.image_url ? (
                            <img
                              src={currentStudent.image_url}
                              alt={currentStudent.name}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center">
                              <User className="h-8 w-8 sm:h-8 sm:w-8 text-gray-400" />
                            </div>
                          )}
                        </div>
                        <p className="text-xs text-gray-600 text-center mt-1">Reference Photo</p>
                      </div>

                      {/* Student Details */}
                      <div className="flex-1 text-center sm:text-left">
                        <h4 className="text-lg sm:text-xl font-bold text-blue-600 mb-1">{currentStudent.name}</h4>
                        <p className="text-sm font-medium text-gray-700 mb-1">{currentStudent.application_number}</p>
                        <p className="text-sm text-blue-600 mb-2">{currentStudent.class} - {currentStudent.department}</p>

                        <div className="inline-flex items-center gap-1 bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs font-medium">
                          <CheckCircle className="h-3 w-3" />
                          Found in Database
                        </div>
                      </div>
                    </div>

                    {/* Student Info Grid */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mt-4 text-sm">
                      <div>
                        <span className="text-blue-600 font-medium">Phone:</span>
                        <p className="text-gray-700">{currentStudent.phone}</p>
                      </div>
                      <div>
                        <span className="text-blue-600 font-medium">Schedule:</span>
                        <p className="text-gray-700">{currentStudent.schedule} Shift (8:00 AM - 2:00 PM)</p>
                      </div>
                    </div>

                    {/* Next Step Alert */}
                    <div className="mt-4 bg-orange-50 border border-orange-200 rounded-lg p-3">
                      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2">
                        <div className="flex items-center gap-2">
                          <AlertTriangle className="h-4 w-4 text-orange-600" />
                          <span className="text-sm font-medium text-orange-800">Next Step:</span>
                        </div>
                        <span className="text-sm text-orange-700">Face verification required to match with stored photo above</span>
                      </div>
                    </div>
                  </div>
                ) : null}
              </CardContent>
            </Card>


          </div>

          {/* Right Panel - Face Verification & Recent Entries */}
          <div className="space-y-4">
            {/* Face Verification Camera - Only show when QR is validated */}
            {qrValidated ? (
              <Card className={verificationStatus === "success" ? "border-green-200 bg-green-50" : ""}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Camera className="h-5 w-5" />
                      Step 2: Face Verification
                      {verificationStatus === "success" && (
                        <Badge variant="secondary" className="ml-2">
                          ✅ Verified
                        </Badge>
                      )}
                    </CardTitle>
                    <Button onClick={resetStation} variant="outline" size="sm" className="text-xs">
                      Scan Different QR
                    </Button>
                  </div>
                </CardHeader>
              <CardContent>
                <div className="relative bg-gray-100 rounded-lg overflow-hidden">
                  {cameraActive ? (
                    <div className="space-y-4">
                      <div className="relative">
                        <video ref={videoRef} className="w-full h-64 object-cover rounded" autoPlay muted />
                        <div className="absolute top-2 right-2 bg-black/50 text-white px-2 py-1 rounded text-xs">
                          Live Camera
                        </div>

                        {/* Live Detection Overlay */}
                        {isScanning && (
                          <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
                            <div className="bg-white/90 p-4 rounded-lg text-center max-w-xs">
                              <div className="space-y-2">
                                {faceDetected ? (
                                  <div className="text-green-600">
                                    <div className="text-2xl">👤</div>
                                    <p className="text-sm font-medium">Live Face Detected</p>
                                  </div>
                                ) : (
                                  <div className="text-orange-600">
                                    <div className="text-2xl">🔍</div>
                                    <p className="text-sm font-medium">Looking for Face...</p>
                                  </div>
                                )}

                                <div className="space-y-1">
                                  <div className="flex items-center justify-between text-xs">
                                    <span>Liveness:</span>
                                    <span className={livenessScore > 50 ? "text-green-600" : "text-orange-600"}>
                                      {livenessScore}%
                                    </span>
                                  </div>
                                  <div className="flex items-center justify-between text-xs">
                                    <span>Blink:</span>
                                    <span className={blinkDetected ? "text-green-600" : "text-gray-400"}>
                                      {blinkDetected ? "✅" : "⏳"}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>

                      <div className="flex gap-2">
                        <Button
                          onClick={verifyFace}
                          disabled={isScanning || verificationStatus !== "scanning" || !qrValidated}
                          className="flex-1"
                        >
                          {isScanning ? "Analyzing Face..." : "Verify Face Match"}
                        </Button>
                        <Button onClick={stopCamera} variant="outline">
                          <CameraOff className="h-4 w-4" />
                        </Button>
                      </div>

                      {/* Live Detection Status */}
                      {liveDetectionStatus && (
                        <Alert className="border-blue-200 bg-blue-50">
                          <Camera className="h-4 w-4 text-blue-600" />
                          <AlertDescription className="text-blue-800">{liveDetectionStatus}</AlertDescription>
                        </Alert>
                      )}

                      {faceMatchScore !== null && (
                        <div className="space-y-2">
                          <div className="grid grid-cols-2 gap-4 text-center">
                            <div className="bg-gray-50 p-2 rounded">
                              <p className="text-xs text-gray-600">Face Match</p>
                              <p className="text-lg font-bold text-gray-800">{faceMatchScore}%</p>
                            </div>
                            <div className="bg-gray-50 p-2 rounded">
                              <p className="text-xs text-gray-600">Liveness</p>
                              <p className="text-lg font-bold text-gray-800">{livenessScore}%</p>
                            </div>
                          </div>
                          <div className="space-y-1">
                            <div className="flex justify-between text-xs">
                              <span>Face Match:</span>
                              <span className={faceMatchScore > 75 ? "text-green-600" : "text-red-600"}>
                                {faceMatchScore > 75 ? "✅ Pass" : "❌ Fail"}
                              </span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className={`h-2 rounded-full ${faceMatchScore > 75 ? "bg-green-500" : "bg-red-500"}`}
                                style={{ width: `${faceMatchScore}%` }}
                              ></div>
                            </div>
                            <div className="flex justify-between text-xs">
                              <span>Liveness:</span>
                              <span className={livenessScore > 50 ? "text-green-600" : "text-red-600"}>
                                {livenessScore > 50 ? "✅ Live" : "❌ Spoof"}
                              </span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className={`h-2 rounded-full ${livenessScore > 50 ? "bg-green-500" : "bg-red-500"}`}
                                style={{ width: `${livenessScore}%` }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="h-64 flex items-center justify-center text-gray-500">
                      <div className="text-center">
                        <Camera className="h-12 w-12 mx-auto mb-2 opacity-50" />
                        <p>Face Camera Ready</p>
                        <p className="text-sm">
                          {qrValidated ? "Click to start face verification" : "Scan Application Number first"}
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Face Verification Status */}
                <div className="mt-4 space-y-3">
                  {verificationStatus === "idle" && qrValidated && (
                    <Button onClick={startCamera} className="w-full" variant="default">
                      <Camera className="mr-2 h-4 w-4" />
                      Start Live Face Verification
                    </Button>
                  )}

                  {verificationStatus === "success" && (
                    <Alert className="border-green-200 bg-green-50">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <AlertDescription className="text-green-800">
                        ✅ Live Face Verification Successful! Entry Recorded.
                        {faceMatchScore && (
                          <div className="text-sm mt-1 space-y-1">
                            <div>👤 Face Match: {faceMatchScore}%</div>
                            <div>💓 Liveness: {livenessScore}%</div>
                            <div>👁️ Blink: {blinkDetected ? "Detected" : "Not Required"}</div>
                          </div>
                        )}
                      </AlertDescription>
                    </Alert>
                  )}

                  {verificationStatus === "failed" && (
                    <Alert className="border-red-200 bg-red-50">
                      <XCircle className="h-4 w-4 text-red-600" />
                      <AlertDescription className="text-red-800">
                        ❌ Live Face Verification Failed!
                        {faceMatchScore !== null ? (
                          <div className="text-sm mt-1 space-y-1">
                            <div>👤 Face Match: {faceMatchScore}% {faceMatchScore > 75 ? "✅" : "❌ (Need >75%)"}</div>
                            <div>💓 Liveness: {livenessScore}% {livenessScore > 50 ? "✅" : "❌ (Need >50%)"}</div>
                            <div>👁️ Blink: {blinkDetected ? "✅ Detected" : "⚠️ Not detected"}</div>
                            <div className="text-xs mt-2 text-red-700">
                              {faceMatchScore <= 75 && "• Face doesn't match stored photo"}
                              {livenessScore <= 50 && "• Possible photo/video spoofing detected"}
                            </div>
                          </div>
                        ) : (
                          <div className="text-sm mt-1">Live face not detected in camera</div>
                        )}
                      </AlertDescription>
                    </Alert>
                  )}

                  {showTryAgain && (
                    <div className="space-y-3">
                      <Alert className="border-orange-200 bg-orange-50">
                        <AlertTriangle className="h-4 w-4 text-orange-600" />
                        <AlertDescription className="text-orange-800">
                          <strong>Verification Failed!</strong> Choose an option below:
                        </AlertDescription>
                      </Alert>

                      <div className="grid grid-cols-1 gap-2">
                        {verificationStatus === "failed" && qrValidated ? (
                          // Face verification failed, but QR is valid
                          <>
                            <Button onClick={tryAgainFace} variant="outline" className="w-full">
                              <Camera className="mr-2 h-4 w-4" />
                              Try Face Verification Again
                            </Button>
                            <Button onClick={tryAgainQR} variant="outline" className="w-full">
                              <QrCode className="mr-2 h-4 w-4" />
                              Scan Different QR Code
                            </Button>
                          </>
                        ) : (
                          // QR validation failed
                          <>
                            <Button onClick={tryAgainQR} variant="outline" className="w-full">
                              <QrCode className="mr-2 h-4 w-4" />
                              Try QR Scan Again
                            </Button>
                          </>
                        )}
                        <Button onClick={resetStation} variant="destructive" className="w-full">
                          <RotateCcw className="mr-2 h-4 w-4" />
                          Reset Station
                        </Button>
                      </div>
                    </div>
                  )}

                  {!qrValidated && (
                    <Alert className="border-yellow-200 bg-yellow-50">
                      <AlertTriangle className="h-4 w-4 text-yellow-600" />
                      <AlertDescription className="text-yellow-800">
                        Please scan and validate an Application Number first before face verification.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              </CardContent>
            </Card>
            ) : (
              /* QR Not Validated - Show Waiting Message */
              <Card className="border-gray-200 bg-gray-50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-gray-500">
                    <Camera className="h-5 w-5" />
                    Step 2: Face Verification
                    <Badge variant="outline" className="ml-2 text-gray-500">
                      Waiting for QR
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center bg-gray-100 rounded border-2 border-dashed border-gray-300">
                    <div className="text-center text-gray-500">
                      <Camera className="h-16 w-16 mx-auto mb-4 opacity-30" />
                      <p className="text-lg font-medium">Face Verification Locked</p>
                      <p className="text-sm">Complete Step 1 (QR Scan) first</p>
                      <div className="mt-4 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                        <p className="text-xs text-yellow-700">
                          🔒 Face verification will activate after successful QR code validation
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Today's Activity Stats - 3 Card Layout */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4">
              {/* Total Entries Card */}
              <Card className="bg-green-50 border-green-200">
                <CardContent className="p-4 sm:p-6 text-center">
                  <div className="text-3xl sm:text-4xl font-bold text-green-600 mb-2">
                    {recentEntries.filter(e => e.status === 'entry').length}
                  </div>
                  <div className="text-sm sm:text-base font-medium text-green-700">
                    Total Entries
                  </div>
                </CardContent>
              </Card>

              {/* Total Exits Card */}
              <Card className="bg-red-50 border-red-200">
                <CardContent className="p-4 sm:p-6 text-center">
                  <div className="text-3xl sm:text-4xl font-bold text-red-600 mb-2">
                    {recentEntries.filter(e => e.status === 'exit').length}
                  </div>
                  <div className="text-sm sm:text-base font-medium text-red-700">
                    Total Exits
                  </div>
                </CardContent>
              </Card>

              {/* Total Activity Card */}
              <Card className="bg-blue-50 border-blue-200">
                <CardContent className="p-4 sm:p-6 text-center">
                  <div className="text-3xl sm:text-4xl font-bold text-blue-600 mb-2">
                    {recentEntries.length}
                  </div>
                  <div className="text-sm sm:text-base font-medium text-blue-700">
                    Total Activity
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* View History Button */}
            <div className="text-center">
              <Button onClick={loadTodayHistory} variant="outline" className="w-full sm:w-auto">
                <Clock className="mr-2 h-4 w-4" />
                View Today's History
              </Button>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Database Connection & System Integration</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-purple-700 mb-2">Same Database Connection:</h3>
                <ol className="list-decimal list-inside space-y-1 text-sm">
                  <li>Station connects to same database as Admin Panel</li>
                  <li>Students added in Admin are instantly available here</li>
                  <li>Entry logs are shared across both systems</li>
                  <li>Real-time data synchronization</li>
                  <li>Fallback to local storage if database unavailable</li>
                  <li>Automatic data sync when connection restored</li>
                </ol>
              </div>
              <div>
                <h3 className="font-semibold text-green-700 mb-2">Professional Station Features:</h3>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Dedicated website for security staff</li>
                  <li>No login required - direct access</li>
                  <li>Real-time QR code scanning</li>
                  <li>Live face verification system</li>
                  <li>Automatic entry/exit logging</li>
                  <li>Professional security interface</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Today's History Modal */}
        {showTodayHistory && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-2xl font-bold">Today's Entry/Exit History</h2>
                <Button onClick={() => setShowTodayHistory(false)} variant="outline">
                  ✕ Close
                </Button>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div className="bg-green-50 p-4 rounded-lg">
                    <p className="text-3xl font-bold text-green-600">{todayEntries.filter(e => e.status === 'entry').length}</p>
                    <p className="text-sm text-green-700">Total Entries</p>
                  </div>
                  <div className="bg-red-50 p-4 rounded-lg">
                    <p className="text-3xl font-bold text-red-600">{todayEntries.filter(e => e.status === 'exit').length}</p>
                    <p className="text-sm text-red-700">Total Exits</p>
                  </div>
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <p className="text-3xl font-bold text-blue-600">{todayEntries.length}</p>
                    <p className="text-sm text-blue-700">Total Activity</p>
                  </div>
                </div>

                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {todayEntries.length === 0 ? (
                    <div className="text-center py-8">
                      <Clock className="h-16 w-16 mx-auto text-gray-300 mb-4" />
                      <p className="text-gray-500 text-lg">No activity recorded today</p>
                      <p className="text-gray-400 text-sm">Entry/exit records will appear here</p>
                    </div>
                  ) : (
                    todayEntries.map((entry: any) => (
                      <div key={entry.id} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <span className="text-2xl">
                                {entry.status === 'entry' ? '🟢' : '🔴'}
                              </span>
                              <div>
                                <p className="font-semibold text-lg">{entry.student_name}</p>
                                <p className="text-sm text-gray-600">App: {entry.application_number}</p>
                              </div>
                            </div>

                            <div className="grid grid-cols-2 gap-4 text-sm">
                              <div>
                                <p className="text-gray-500">Entry Time</p>
                                <p className="font-medium">{formatDateTime(entry.entryTime)}</p>
                              </div>
                              {entry.exitTime && (
                                <div>
                                  <p className="text-gray-500">Exit Time</p>
                                  <p className="font-medium">{formatDateTime(entry.exitTime)}</p>
                                </div>
                              )}
                            </div>

                            <div className="mt-3 flex items-center gap-4 text-xs text-gray-500">
                              <span className="flex items-center gap-1">
                                <QrCode className="h-3 w-3" />
                                QR Verified
                              </span>
                              <span className="flex items-center gap-1">
                                <User className="h-3 w-3" />
                                Face Verified
                              </span>
                              <span className="flex items-center gap-1">
                                <CheckCircle className="h-3 w-3" />
                                {entry.status === 'entry' ? 'Entry' : 'Exit'} Recorded
                              </span>
                            </div>
                          </div>

                          <div className="text-right">
                            <Badge variant={entry.status === 'entry' ? 'default' : 'secondary'} className="mb-2">
                              {entry.status === 'entry' ? 'ENTRY' : 'EXIT'}
                            </Badge>
                            <p className="text-xs text-gray-500">
                              {entry.verified ? '✅ Verified' : '⚠️ Pending'}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>

                <div className="text-center text-sm text-gray-500 border-t pt-3">
                  <p>History resets daily at midnight • Real-time updates</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
