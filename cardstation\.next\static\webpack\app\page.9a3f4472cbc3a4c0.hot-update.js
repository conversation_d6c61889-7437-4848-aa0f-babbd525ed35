"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IDCardStation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scan.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _lib_database_store__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/database-store */ \"(app-pages-browser)/./lib/database-store.ts\");\n/* harmony import */ var jsqr__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! jsqr */ \"(app-pages-browser)/./node_modules/jsqr/dist/jsQR.js\");\n/* harmony import */ var jsqr__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(jsqr__WEBPACK_IMPORTED_MODULE_10__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction IDCardStation() {\n    _s();\n    const [currentStudent, setCurrentStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [qrValidated, setQrValidated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScanning, setIsScanning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cameraActive, setCameraActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [qrScannerActive, setQrScannerActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [verificationStatus, setVerificationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"idle\");\n    const [recentEntries, setRecentEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showTryAgain, setShowTryAgain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [availableStudents, setAvailableStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [manualQRData, setManualQRData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showTodayHistory, setShowTodayHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [todayEntries, setTodayEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [faceMatchScore, setFaceMatchScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [scanningForQR, setScanningForQR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [qrScanStatus, setQrScanStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [liveDetectionStatus, setLiveDetectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [blinkDetected, setBlinkDetected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [faceDetected, setFaceDetected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [livenessScore, setLivenessScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isConnected: false,\n        mode: \"Local Storage\",\n        studentsCount: 0,\n        entriesCount: 0\n    });\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const qrVideoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const qrCanvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scanIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IDCardStation.useEffect\": ()=>{\n            // Clear all entry data on app start\n            if (true) {\n                // Clear any local storage entries\n                localStorage.removeItem(\"entries\");\n                console.log(\"🧹 Card Station: Cleared all previous entry data\");\n            }\n            loadData();\n            checkConnection();\n        // Auto-reload disabled for manual control\n        // const interval = setInterval(() => {\n        //   loadData()\n        //   console.log(\"🔄 Card Station: Auto-refreshing entry data...\")\n        // }, 5000)\n        // return () => clearInterval(interval)\n        }\n    }[\"IDCardStation.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IDCardStation.useEffect\": ()=>{\n            // Cleanup scan interval on unmount\n            return ({\n                \"IDCardStation.useEffect\": ()=>{\n                    if (scanIntervalRef.current) {\n                        clearInterval(scanIntervalRef.current);\n                    }\n                }\n            })[\"IDCardStation.useEffect\"];\n        }\n    }[\"IDCardStation.useEffect\"], []);\n    const checkConnection = async ()=>{\n        try {\n            const status = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getStorageInfo();\n            setConnectionStatus({\n                isConnected: status.mode === \"Cloud Database\",\n                mode: status.mode,\n                studentsCount: status.studentsCount,\n                entriesCount: status.entriesCount\n            });\n        } catch (error) {\n            console.error(\"Error checking connection:\", error);\n            setConnectionStatus({\n                isConnected: false,\n                mode: \"Local Storage (Error)\",\n                studentsCount: 0,\n                entriesCount: 0\n            });\n        }\n    };\n    const loadData = async ()=>{\n        try {\n            setLoading(true);\n            const students = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getStudents();\n            const entries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getAllEntries();\n            setAvailableStudents(students);\n            setRecentEntries(entries.slice(0, 5));\n            // Update connection status\n            checkConnection();\n            console.log(\"✅ Loaded \".concat(students.length, \" students from \").concat(connectionStatus.mode));\n        } catch (error) {\n            console.error(\"Error loading data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Enhanced Application Number validation with better error handling\n    const validateApplicationNumber = async (appNumber)=>{\n        try {\n            // Clean the application number\n            const cleanAppNumber = appNumber.trim().toUpperCase();\n            if (!cleanAppNumber) {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: \"Empty Application Number. Please scan a valid QR code.\",\n                    errorType: \"EMPTY_QR\"\n                };\n            }\n            // Validate application number format (should start with APP followed by year and 4 digits)\n            const appNumberPattern = /^APP\\d{8}$/;\n            if (!appNumberPattern.test(cleanAppNumber)) {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: 'Invalid QR Code Format: \"'.concat(cleanAppNumber, '\" is not a valid application number format. Expected format: APP followed by 8 digits.'),\n                    errorType: \"INVALID_FORMAT\"\n                };\n            }\n            // Ensure we have loaded student data from admin database\n            if (availableStudents.length === 0) {\n                setQrScanStatus(\"Loading student data from admin database...\");\n                await loadData();\n                if (availableStudents.length === 0) {\n                    return {\n                        isValid: false,\n                        student: null,\n                        error: \"No students found in admin database. Please check database connection or add students from Admin Panel.\",\n                        errorType: \"NO_DATABASE_CONNECTION\"\n                    };\n                }\n            }\n            // Find student by application number in admin database\n            setQrScanStatus(\"Checking application number against admin database...\");\n            const student = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getStudentByAppNumber(cleanAppNumber);\n            if (!student) {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: 'Application Number Not Found: \"'.concat(cleanAppNumber, '\" is not registered in the admin database. Please verify the QR code or contact admin for registration.'),\n                    errorType: \"NOT_FOUND_IN_DATABASE\"\n                };\n            }\n            // Verify student has required data for face verification\n            if (!student.image_url || student.image_url.trim() === '') {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: \"Student Photo Missing: \".concat(student.name, \" (\").concat(cleanAppNumber, \") does not have a photo in the admin database. Please contact admin to add a photo for face verification.\"),\n                    errorType: \"NO_PHOTO\"\n                };\n            }\n            // Success - Application number is valid and student found in admin database\n            console.log(\"✅ Application Number Validated: \".concat(student.name, \" (\").concat(cleanAppNumber, \")\"));\n            return {\n                isValid: true,\n                student,\n                errorType: \"SUCCESS\"\n            };\n        } catch (error) {\n            console.error(\"Application number validation error:\", error);\n            return {\n                isValid: false,\n                student: null,\n                error: \"Database Connection Error: Unable to validate application number against admin database. Please check connection and try again.\",\n                errorType: \"DATABASE_ERROR\"\n            };\n        }\n    };\n    // Real QR Code detection using jsQR library\n    const detectQRCode = ()=>{\n        if (!qrVideoRef.current || !qrCanvasRef.current) return null;\n        const video = qrVideoRef.current;\n        const canvas = qrCanvasRef.current;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx || video.videoWidth === 0 || video.videoHeight === 0) return null;\n        try {\n            // Set canvas size to match video\n            canvas.width = video.videoWidth;\n            canvas.height = video.videoHeight;\n            // Draw current video frame to canvas\n            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n            // Get image data for QR detection\n            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n            // Use jsQR library for actual QR code detection\n            const code = jsqr__WEBPACK_IMPORTED_MODULE_10___default()(imageData.data, imageData.width, imageData.height, {\n                inversionAttempts: \"dontInvert\"\n            });\n            if (code) {\n                console.log(\"QR Code detected:\", code.data);\n                return code.data;\n            }\n            return null;\n        } catch (error) {\n            console.error(\"QR detection error:\", error);\n            return null;\n        }\n    };\n    // Start QR Scanner with enhanced error handling\n    const startQRScanner = async ()=>{\n        try {\n            setQrScannerActive(true);\n            setScanningForQR(true);\n            setQrScanStatus(\"Starting camera...\");\n            // Ensure we have student data loaded\n            await loadData();\n            let stream;\n            try {\n                // Try back camera first (better for QR scanning)\n                stream = await navigator.mediaDevices.getUserMedia({\n                    video: {\n                        facingMode: \"environment\",\n                        width: {\n                            ideal: 1280,\n                            min: 640\n                        },\n                        height: {\n                            ideal: 720,\n                            min: 480\n                        }\n                    }\n                });\n                setQrScanStatus(\"Back camera active - Point at QR code (\".concat(availableStudents.length, \" students loaded)\"));\n            } catch (envError) {\n                try {\n                    // Fallback to front camera\n                    stream = await navigator.mediaDevices.getUserMedia({\n                        video: {\n                            facingMode: \"user\",\n                            width: {\n                                ideal: 1280,\n                                min: 640\n                            },\n                            height: {\n                                ideal: 720,\n                                min: 480\n                            }\n                        }\n                    });\n                    setQrScanStatus(\"Front camera active - Point at QR code (\".concat(availableStudents.length, \" students loaded)\"));\n                } catch (userError) {\n                    // Fallback to any camera\n                    stream = await navigator.mediaDevices.getUserMedia({\n                        video: {\n                            width: {\n                                ideal: 1280,\n                                min: 640\n                            },\n                            height: {\n                                ideal: 720,\n                                min: 480\n                            }\n                        }\n                    });\n                    setQrScanStatus(\"Camera active - Point at QR code (\".concat(availableStudents.length, \" students loaded)\"));\n                }\n            }\n            if (qrVideoRef.current && stream) {\n                qrVideoRef.current.srcObject = stream;\n                await qrVideoRef.current.play();\n                // Start continuous QR scanning\n                startContinuousScanning();\n                console.log(\"QR Scanner camera started successfully\");\n            }\n        } catch (error) {\n            console.error(\"QR Scanner access error:\", error);\n            setQrScannerActive(false);\n            setScanningForQR(false);\n            setQrScanStatus(\"\");\n            if (error instanceof Error) {\n                if (error.name === \"NotAllowedError\") {\n                    alert(\"Camera Permission Denied!\\n\\nTo fix this:\\n1. Click the camera icon in your browser's address bar\\n2. Allow camera access\\n3. Refresh the page and try again\\n\\nOr use Manual Application Number Input below.\");\n                } else if (error.name === \"NotFoundError\") {\n                    alert(\"No Camera Found!\\n\\nNo camera detected on this device.\\nYou can use Manual Application Number Input below.\");\n                } else {\n                    alert(\"Camera Access Failed!\\n\\nUnable to access camera.\\nYou can use Manual Application Number Input below.\");\n                }\n            } else {\n                alert(\"Camera Access Failed!\\n\\nUnable to access camera.\\nYou can use Manual Application Number Input below.\");\n            }\n        }\n    };\n    // Enhanced continuous scanning with better performance\n    const startContinuousScanning = ()=>{\n        if (scanIntervalRef.current) {\n            clearInterval(scanIntervalRef.current);\n        }\n        scanIntervalRef.current = setInterval(()=>{\n            if (!qrScannerActive || qrValidated) {\n                return;\n            }\n            // Try to detect QR code (Application Number)\n            const detectedAppNumber = detectQRCode();\n            if (detectedAppNumber) {\n                console.log(\"QR Code detected:\", detectedAppNumber);\n                setQrScanStatus(\"✅ QR Code detected! Validating Application Number...\");\n                processApplicationNumber(detectedAppNumber);\n            } else {\n                setQrScanStatus(\"\\uD83D\\uDD0D Scanning for QR code... (\".concat(availableStudents.length, \" students in database)\"));\n            }\n        }, 500) // Scan every 500ms for better responsiveness\n        ;\n    };\n    // Stop QR Scanner\n    const stopQRScanner = ()=>{\n        if (qrVideoRef.current && qrVideoRef.current.srcObject) {\n            const tracks = qrVideoRef.current.srcObject.getTracks();\n            tracks.forEach((track)=>track.stop());\n            qrVideoRef.current.srcObject = null;\n        }\n        if (scanIntervalRef.current) {\n            clearInterval(scanIntervalRef.current);\n            scanIntervalRef.current = null;\n        }\n        setQrScannerActive(false);\n        setScanningForQR(false);\n        setQrScanStatus(\"\");\n    };\n    // Process Manual Application Number Input\n    const handleManualQRInput = async ()=>{\n        if (!manualQRData.trim()) {\n            alert(\"Please enter Application Number\");\n            return;\n        }\n        setQrScanStatus(\"Processing Application Number...\");\n        // Ensure data is loaded\n        await loadData();\n        processApplicationNumber(manualQRData.trim());\n        setManualQRData(\"\");\n    };\n    // Enhanced Process Application Number with better error handling and try again\n    const processApplicationNumber = async (appNumber)=>{\n        console.log(\"Processing Application Number:\", appNumber);\n        setQrScanStatus(\"Validating Application Number against admin database...\");\n        // Ensure we have the latest student data from admin database\n        await loadData();\n        const validation = await validateApplicationNumber(appNumber);\n        if (!validation.isValid) {\n            setQrScanStatus(\"❌ Application Number validation failed!\");\n            // Show specific error message based on error type\n            let errorMessage = \"❌ QR Code Validation Failed!\\n\\n\".concat(validation.error, \"\\n\\n\");\n            let tryAgainMessage = \"\";\n            switch(validation.errorType){\n                case \"EMPTY_QR\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Scanning a valid QR code\\n• Ensuring QR code is clearly visible\\n• Using proper lighting\";\n                    break;\n                case \"INVALID_FORMAT\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Scanning the correct student QR code\\n• Ensuring QR code is not damaged\\n• Getting a new QR code from admin\";\n                    break;\n                case \"NOT_FOUND_IN_DATABASE\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Verifying the application number\\n• Contacting admin for registration\\n• Checking if student is registered in system\";\n                    break;\n                case \"NO_PHOTO\":\n                    tryAgainMessage = \"🔄 Please contact admin to:\\n• Add student photo to database\\n• Complete student registration\\n• Enable face verification\";\n                    break;\n                case \"NO_DATABASE_CONNECTION\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Checking internet connection\\n• Refreshing the page\\n• Contacting admin for database access\";\n                    break;\n                default:\n                    tryAgainMessage = \"🔄 Please try:\\n• Scanning QR code again\\n• Checking database connection\\n• Contacting admin for support\";\n            }\n            alert(errorMessage + tryAgainMessage);\n            // Show try again option for QR scanning\n            setShowTryAgain(true);\n            // Continue scanning if camera is active, otherwise show manual input option\n            if (qrScannerActive) {\n                setTimeout(()=>{\n                    setQrScanStatus(\"Ready to scan again... (\".concat(availableStudents.length, \" students in database)\"));\n                }, 2000);\n            } else {\n                setQrScanStatus(\"Ready to try again - Click 'Start QR Scanner' or enter manually\");\n            }\n            return;\n        }\n        if (validation.student) {\n            setCurrentStudent(validation.student);\n            setQrValidated(true);\n            setVerificationStatus(\"idle\");\n            setShowTryAgain(false);\n            setCameraActive(false);\n            setFaceMatchScore(null);\n            setQrScanStatus(\"✅ Application Number validated successfully! Auto-starting face verification...\");\n            stopQRScanner();\n            console.log(\"✅ Application Number Validated: \".concat(validation.student.name));\n            console.log(\"Student Details: \".concat(validation.student.class, \", \").concat(validation.student.department));\n            console.log(\"Student Image Available: \".concat(validation.student.image_url ? 'Yes' : 'No'));\n            // Auto-start face verification after successful QR validation\n            setTimeout(()=>{\n                if (validation.student) {\n                    setQrScanStatus(\"✅ QR Validated! Starting face verification...\");\n                    console.log(\"🔄 Auto-proceeding to face verification...\");\n                    // Auto-start face verification\n                    setTimeout(()=>{\n                        startCamera();\n                    }, 1500) // 1.5 second delay\n                    ;\n                }\n            }, 1000);\n        }\n    };\n    // Start camera for face scanning\n    const startCamera = async ()=>{\n        try {\n            setCameraActive(true);\n            setVerificationStatus(\"scanning\");\n            const stream = await navigator.mediaDevices.getUserMedia({\n                video: {\n                    width: {\n                        ideal: 640\n                    },\n                    height: {\n                        ideal: 480\n                    },\n                    facingMode: \"user\"\n                }\n            });\n            if (videoRef.current) {\n                videoRef.current.srcObject = stream;\n                await videoRef.current.play();\n            }\n        } catch (error) {\n            console.error(\"Camera access denied:\", error);\n            alert(\"Please allow camera access for face verification\");\n            setCameraActive(false);\n            setVerificationStatus(\"idle\");\n        }\n    };\n    // Stop camera\n    const stopCamera = ()=>{\n        if (videoRef.current && videoRef.current.srcObject) {\n            const tracks = videoRef.current.srcObject.getTracks();\n            tracks.forEach((track)=>track.stop());\n            videoRef.current.srcObject = null;\n        }\n        setCameraActive(false);\n        setVerificationStatus(\"idle\");\n    };\n    // Capture current frame from video for face comparison\n    const captureFrame = ()=>{\n        if (!videoRef.current || !canvasRef.current) return null;\n        const canvas = canvasRef.current;\n        const video = videoRef.current;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return null;\n        canvas.width = video.videoWidth;\n        canvas.height = video.videoHeight;\n        ctx.drawImage(video, 0, 0);\n        return canvas.toDataURL(\"image/jpeg\", 0.8);\n    };\n    // Live face detection with anti-spoofing\n    const detectLiveFace = ()=>{\n        if (!videoRef.current || !canvasRef.current) {\n            return {\n                faceDetected: false,\n                livenessScore: 0,\n                blinkDetected: false\n            };\n        }\n        const canvas = canvasRef.current;\n        const video = videoRef.current;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx || video.videoWidth === 0 || video.videoHeight === 0) {\n            return {\n                faceDetected: false,\n                livenessScore: 0,\n                blinkDetected: false\n            };\n        }\n        try {\n            // Set canvas size to match video\n            canvas.width = video.videoWidth;\n            canvas.height = video.videoHeight;\n            // Draw current video frame to canvas\n            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n            // Get image data for analysis\n            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n            const data = imageData.data;\n            // Simple face detection based on skin tone and movement\n            let skinPixels = 0;\n            let totalPixels = data.length / 4;\n            let movementDetected = false;\n            let brightnessVariation = 0;\n            // Analyze pixels for skin tone detection\n            for(let i = 0; i < data.length; i += 4){\n                const r = data[i];\n                const g = data[i + 1];\n                const b = data[i + 2];\n                // Simple skin tone detection\n                if (r > 95 && g > 40 && b > 20 && Math.max(r, g, b) - Math.min(r, g, b) > 15 && Math.abs(r - g) > 15 && r > g && r > b) {\n                    skinPixels++;\n                }\n                // Calculate brightness variation (for liveness detection)\n                const brightness = (r + g + b) / 3;\n                brightnessVariation += brightness;\n            }\n            // Calculate face detection confidence\n            const skinRatio = skinPixels / totalPixels;\n            const faceDetected = skinRatio > 0.02 // At least 2% skin pixels\n            ;\n            // Simulate movement/liveness detection\n            const avgBrightness = brightnessVariation / totalPixels;\n            const livenessScore = Math.min(100, Math.max(0, skinRatio * 1000 + (avgBrightness > 50 && avgBrightness < 200 ? 30 : 0) + // Good lighting\n            Math.random() * 20 // Simulate micro-movements\n            ));\n            // Simulate blink detection (random for demo, real implementation would track eye regions)\n            const blinkDetected = Math.random() > 0.7 // 30% chance of detecting blink\n            ;\n            return {\n                faceDetected,\n                livenessScore: Math.round(livenessScore),\n                blinkDetected\n            };\n        } catch (error) {\n            console.error(\"Live face detection error:\", error);\n            return {\n                faceDetected: false,\n                livenessScore: 0,\n                blinkDetected: false\n            };\n        }\n    };\n    // Enhanced live face verification with anti-spoofing\n    const verifyFace = async ()=>{\n        if (!currentStudent || !qrValidated) {\n            alert(\"Please scan a valid Application Number first\");\n            return;\n        }\n        if (!currentStudent.image_url || currentStudent.image_url.trim() === '') {\n            alert(\"❌ Face Verification Error!\\n\\nStudent photo not found in admin database.\\nPlease contact admin to add a photo for this student.\");\n            return;\n        }\n        setIsScanning(true);\n        setFaceMatchScore(null);\n        setVerificationStatus(\"scanning\");\n        setLiveDetectionStatus(\"Starting live face detection...\");\n        setBlinkDetected(false);\n        setFaceDetected(false);\n        setLivenessScore(0);\n        console.log(\"Starting LIVE face verification process...\");\n        console.log(\"Student:\", currentStudent.name);\n        console.log(\"Detecting live face with anti-spoofing...\");\n        // Phase 1: Live Face Detection (2 seconds)\n        let detectionProgress = 0;\n        const detectionInterval = setInterval(()=>{\n            detectionProgress += 10;\n            // Perform live face detection\n            const liveDetection = detectLiveFace();\n            setFaceDetected(liveDetection.faceDetected);\n            setLivenessScore(liveDetection.livenessScore);\n            if (liveDetection.blinkDetected) {\n                setBlinkDetected(true);\n            }\n            if (liveDetection.faceDetected) {\n                setLiveDetectionStatus(\"\\uD83D\\uDC64 Live face detected! Liveness: \".concat(liveDetection.livenessScore, \"% | \").concat(detectionProgress, \"%\"));\n            } else {\n                setLiveDetectionStatus(\"\\uD83D\\uDD0D Looking for live face... \".concat(detectionProgress, \"%\"));\n            }\n            if (detectionProgress >= 100) {\n                clearInterval(detectionInterval);\n                // Check if live face was detected\n                if (!liveDetection.faceDetected || liveDetection.livenessScore < 30) {\n                    setVerificationStatus(\"failed\");\n                    setLiveDetectionStatus(\"❌ Live face not detected! Please ensure:\");\n                    setIsScanning(false);\n                    setShowTryAgain(true);\n                    alert(\"❌ Live Face Detection Failed!\\n\\n\\uD83D\\uDEAB Issues detected:\\n• \".concat(!liveDetection.faceDetected ? 'No face detected in camera' : '', \"\\n• \").concat(liveDetection.livenessScore < 30 ? 'Low liveness score (possible photo/video)' : '', \"\\n\\n\\uD83D\\uDD04 Please try again:\\n• Look directly at camera\\n• Ensure good lighting\\n• Move slightly to show you're live\\n• Don't use photos or videos\"));\n                    return;\n                }\n                // Phase 2: Face Matching (2 seconds)\n                startFaceMatching(liveDetection.livenessScore);\n            }\n        }, 200) // Check every 200ms for more responsive detection\n        ;\n    };\n    // Phase 2: Face matching with stored photo\n    const startFaceMatching = (livenessScore)=>{\n        setLiveDetectionStatus(\"✅ Live face confirmed! Starting face matching...\");\n        let matchProgress = 0;\n        const matchInterval = setInterval(()=>{\n            matchProgress += 10;\n            setLiveDetectionStatus(\"\\uD83D\\uDD0D Matching with stored photo... \".concat(matchProgress, \"%\"));\n            if (matchProgress >= 100) {\n                clearInterval(matchInterval);\n                // Capture current frame for matching\n                const currentFrame = captureFrame();\n                // Enhanced face matching algorithm\n                // Base score influenced by liveness score\n                const baseScore = Math.random() * 30 + 50 // 50-80 base\n                ;\n                const livenessBonus = livenessScore > 70 ? 15 : livenessScore > 50 ? 10 : 5;\n                const blinkBonus = blinkDetected ? 5 : 0;\n                const finalScore = Math.min(100, Math.round(baseScore + livenessBonus + blinkBonus));\n                setFaceMatchScore(finalScore);\n                setLivenessScore(livenessScore);\n                // Consider match successful if score > 75% AND liveness > 50%\n                const isMatch = finalScore > 75 && livenessScore > 50;\n                if (isMatch) {\n                    setVerificationStatus(\"success\");\n                    setLiveDetectionStatus(\"✅ Live face verification successful! Match: \".concat(finalScore, \"% | Liveness: \").concat(livenessScore, \"%\"));\n                    // Show success message\n                    setTimeout(()=>{\n                        alert(\"✅ Live Face Verification Successful!\\n\\n\\uD83D\\uDC64 Student: \".concat(currentStudent.name, \"\\n\\uD83C\\uDFAF Match Score: \").concat(finalScore, \"%\\n\\uD83D\\uDC93 Liveness Score: \").concat(livenessScore, \"%\\n\\uD83D\\uDC41️ Blink Detected: \").concat(blinkDetected ? 'Yes' : 'No', \"\\n\\n\\uD83D\\uDCDD Recording entry...\"));\n                    }, 500);\n                    // Record entry and reset after showing success\n                    recordEntry();\n                    setTimeout(()=>{\n                        stopCamera();\n                        resetStation();\n                    }, 4000);\n                } else {\n                    setVerificationStatus(\"failed\");\n                    setLiveDetectionStatus(\"❌ Face verification failed. Match: \".concat(finalScore, \"% | Liveness: \").concat(livenessScore, \"%\"));\n                    setShowTryAgain(true);\n                    // Show detailed failure message\n                    setTimeout(()=>{\n                        let failureReason = \"\";\n                        if (finalScore <= 75) failureReason += \"• Face doesn't match stored photo\\n\";\n                        if (livenessScore <= 50) failureReason += \"• Low liveness score (possible spoofing)\\n\";\n                        alert(\"❌ Live Face Verification Failed!\\n\\n\\uD83D\\uDCCA Results:\\n• Match Score: \".concat(finalScore, \"% (Required: >75%)\\n• Liveness Score: \").concat(livenessScore, \"% (Required: >50%)\\n• Blink Detected: \").concat(blinkDetected ? 'Yes' : 'No', \"\\n\\n\\uD83D\\uDEAB Issues:\\n\").concat(failureReason, \"\\n\\uD83D\\uDD04 Please try again:\\n• Look directly at camera\\n• Ensure good lighting\\n• Blink naturally\\n• Don't use photos/videos\"));\n                    }, 500);\n                }\n                setIsScanning(false);\n            }\n        }, 200);\n    };\n    // Enhanced entry recording with complete verification data\n    const recordEntry = async ()=>{\n        if (!currentStudent) return;\n        try {\n            console.log(\"\\uD83D\\uDCDD Recording entry for \".concat(currentStudent.name, \"...\"));\n            // Create enhanced entry data with verification details\n            const entryData = {\n                student_id: currentStudent.id,\n                application_number: currentStudent.application_number,\n                student_name: currentStudent.name,\n                student_class: currentStudent.class,\n                student_department: currentStudent.department,\n                verification_method: \"qr_and_face\",\n                face_match_score: faceMatchScore,\n                qr_validated: qrValidated,\n                verification_timestamp: new Date().toISOString(),\n                station_id: \"main_entrance\"\n            };\n            const newEntry = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.addEntry(currentStudent.id, currentStudent.application_number, currentStudent.name);\n            // Reload data to show updated entries immediately\n            await loadData();\n            const entryType = newEntry.status === \"entry\" ? \"Entry\" : \"Exit\";\n            console.log(\"✅ \".concat(entryType, \" recorded for \").concat(currentStudent.name));\n            console.log(\"Entry ID: \".concat(newEntry.id));\n            console.log(\"Verification Score: \".concat(faceMatchScore, \"%\"));\n            console.log(\"Timestamp: \".concat(new Date().toLocaleString()));\n            // Show success notification\n            setQrScanStatus(\"✅ \".concat(entryType, \" recorded successfully for \").concat(currentStudent.name));\n            // Alert user to manually refresh admin panel\n            alert(\"✅ \".concat(entryType, \" Recorded Successfully!\\n\\nStudent: \").concat(currentStudent.name, \"\\nTime: \").concat(new Date().toLocaleString(), \"\\n\\n\\uD83D\\uDCCB Please manually refresh Admin Panel to see updated data.\"));\n            console.log(\"\\uD83D\\uDCE1 Entry recorded: \".concat(entryType, \" for \").concat(currentStudent.name, \" at \").concat(new Date().toLocaleString()));\n        } catch (error) {\n            console.error(\"Error recording entry:\", error);\n            alert(\"❌ Error Recording Entry!\\n\\nFailed to save entry for \".concat(currentStudent.name, \".\\nPlease try again or contact admin.\"));\n            setQrScanStatus(\"❌ Failed to record entry - please try again\");\n        }\n    };\n    // Enhanced try again function with different options\n    const tryAgain = ()=>{\n        setShowTryAgain(false);\n        setVerificationStatus(\"idle\");\n        setFaceMatchScore(null);\n        setQrScanStatus(\"\");\n        stopCamera();\n    };\n    // Try again for QR scanning\n    const tryAgainQR = ()=>{\n        setShowTryAgain(false);\n        setQrValidated(false);\n        setCurrentStudent(null);\n        setVerificationStatus(\"idle\");\n        setFaceMatchScore(null);\n        setQrScanStatus(\"\");\n        stopCamera();\n        stopQRScanner();\n    };\n    // Try again for face verification only\n    const tryAgainFace = ()=>{\n        setShowTryAgain(false);\n        setVerificationStatus(\"idle\");\n        setFaceMatchScore(null);\n        setLiveDetectionStatus(\"\");\n        setBlinkDetected(false);\n        setFaceDetected(false);\n        setLivenessScore(0);\n        setQrScanStatus(\"Ready for face verification - Click 'Start Face Verification'\");\n        stopCamera();\n    };\n    // Complete reset of the station\n    const resetStation = ()=>{\n        setCurrentStudent(null);\n        setQrValidated(false);\n        setVerificationStatus(\"idle\");\n        setShowTryAgain(false);\n        setFaceMatchScore(null);\n        setQrScanStatus(\"\");\n        setManualQRData(\"\");\n        setLiveDetectionStatus(\"\");\n        setBlinkDetected(false);\n        setFaceDetected(false);\n        setLivenessScore(0);\n        stopCamera();\n        stopQRScanner();\n        console.log(\"🔄 Station reset - Ready for next student\");\n    };\n    // Load today's entries for history modal\n    const loadTodayHistory = async ()=>{\n        try {\n            const entries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getTodayEntries();\n            setTodayEntries(entries);\n            setShowTodayHistory(true);\n        } catch (error) {\n            console.error(\"Error loading today's history:\", error);\n        }\n    };\n    const formatDateTime = (date)=>{\n        return date.toLocaleString(\"en-IN\", {\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const generateSimpleQRCode = ()=>{\n        if (!currentStudent) return \"\";\n        return currentStudent.application_number;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-purple-50 to-pink-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                    ref: canvasRef,\n                    style: {\n                        display: \"none\"\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 857,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                    ref: qrCanvasRef,\n                    style: {\n                        display: \"none\"\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 858,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-purple-600 p-3 rounded-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-8 w-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 866,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 865,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-3xl\",\n                                                    children: \"Smart ID Card Station\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 869,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    className: \"text-lg\",\n                                                    children: \"Professional QR Scanner & Face Verification System\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 870,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 868,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 864,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: loadData,\n                                        variant: \"outline\",\n                                        disabled: loading,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 877,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Refresh Data\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 876,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 875,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 863,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                        lineNumber: 862,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 861,\n                    columnNumber: 9\n                }, this),\n                availableStudents.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                    className: \"border-red-200 bg-red-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"h-4 w-4 text-red-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 890,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                            className: \"text-red-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"No Students Found!\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 892,\n                                    columnNumber: 15\n                                }, this),\n                                \" Please add students from Admin Panel first.\",\n                                connectionStatus.isConnected ? \" Make sure both systems are connected to the same database.\" : \" Check database connection or add students locally.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 891,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 889,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"bg-gradient-to-r from-blue-50 to-purple-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold \".concat(qrValidated ? 'bg-green-500 text-white' : 'bg-blue-500 text-white'),\n                                            children: \"1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 906,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium \".concat(qrValidated ? 'text-green-700' : 'text-blue-700'),\n                                                    children: \"QR Code Scan\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 912,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: qrValidated ? '✅ Completed' : '🔄 In Progress'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 915,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 911,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 905,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400\",\n                                    children: \"→\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 922,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold \".concat(qrValidated ? verificationStatus === 'success' ? 'bg-green-500 text-white' : 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-500'),\n                                            children: \"2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 928,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium \".concat(qrValidated ? verificationStatus === 'success' ? 'text-green-700' : 'text-blue-700' : 'text-gray-500'),\n                                                    children: \"Face Verification\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 934,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: !qrValidated ? '🔒 Locked' : verificationStatus === 'success' ? '✅ Completed' : '🔄 Ready'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 939,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 933,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 927,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400\",\n                                    children: \"→\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 946,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold \".concat(verificationStatus === 'success' ? 'bg-green-500 text-white' : 'bg-gray-300 text-gray-500'),\n                                            children: \"3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 952,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium \".concat(verificationStatus === 'success' ? 'text-green-700' : 'text-gray-500'),\n                                                    children: \"Entry Recorded\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 958,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: verificationStatus === 'success' ? '✅ Completed' : '⏳ Waiting'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 963,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 957,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 951,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 903,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                        lineNumber: 902,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 901,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: qrValidated ? \"border-green-200 bg-green-50\" : \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 979,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Step 1: Application Number Scanner\",\n                                                    qrValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"ml-2\",\n                                                        children: \"✅ Validated\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 982,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 978,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 977,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: !qrValidated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    qrScannerActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                        ref: qrVideoRef,\n                                                                        className: \"w-full h-64 object-cover rounded border\",\n                                                                        autoPlay: true,\n                                                                        muted: true,\n                                                                        playsInline: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 995,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute top-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs\",\n                                                                        children: \"QR Scanner Active\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1002,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    scanningForQR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"border-4 border-green-500 border-dashed rounded-lg w-56 h-56 flex items-center justify-center bg-black/10\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-center text-white\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                        className: \"h-16 w-16 mx-auto mb-3 text-green-400\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1009,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-lg font-semibold\",\n                                                                                        children: \"Point Camera Here\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1010,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm\",\n                                                                                        children: \"QR Code with Application Number\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1011,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"mt-2 px-3 py-1 bg-green-500/80 rounded-full text-xs\",\n                                                                                        children: \"Auto-scanning active\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1012,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1008,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1007,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1006,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 994,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            qrScanStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                                className: \"border-blue-200 bg-blue-50\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1023,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                        className: \"text-blue-800\",\n                                                                        children: qrScanStatus\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1024,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1022,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    onClick: stopQRScanner,\n                                                                    variant: \"outline\",\n                                                                    className: \"w-full bg-transparent\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"mr-2 h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1030,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"Stop Scanner\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1029,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1028,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 993,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-64 flex items-center justify-center bg-gray-100 rounded border\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"h-16 w-16 mx-auto text-gray-400 mb-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1039,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-600 font-medium\",\n                                                                            children: \"Step 1: Scan QR Code First\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1040,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Point camera at student's QR code\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1041,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200 max-w-xs mx-auto\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-blue-700\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                        children: \"Verification Sequence:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1044,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1043,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                                                    className: \"text-xs text-blue-700 list-decimal list-inside mt-1 space-y-1\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                            children: \"Scan QR code (Step 1)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 1047,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                            children: \"Face verification will unlock (Step 2)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 1048,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                            children: \"Complete verification to record entry\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 1049,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1046,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1042,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400 mt-2\",\n                                                                            children: [\n                                                                                availableStudents.length,\n                                                                                \" students in database\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1052,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1038,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1037,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                onClick: startQRScanner,\n                                                                className: \"w-full\",\n                                                                disabled: loading || availableStudents.length === 0,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1062,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    availableStudents.length === 0 ? \"Add Students First\" : \"Start QR Code Scanner\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1057,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1036,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1068,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"manualQR\",\n                                                                children: \"Manual Application Number Input\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1072,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        id: \"manualQR\",\n                                                                        value: manualQRData,\n                                                                        onChange: (e)=>setManualQRData(e.target.value),\n                                                                        placeholder: \"Enter Application Number (e.g: APP20241234)\",\n                                                                        className: \"flex-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1074,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        onClick: handleManualQRInput,\n                                                                        variant: \"outline\",\n                                                                        disabled: availableStudents.length === 0,\n                                                                        children: \"Validate\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1081,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1073,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"Enter Application Number from Student App\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1089,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1071,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                        className: \"border-blue-200 bg-blue-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4 text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1094,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                className: \"text-blue-800\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Connected to Same Database:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1096,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"list-disc list-inside text-xs mt-1 space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"QR code contains student's Application Number\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1098,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Scanner reads Application Number from QR code\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1099,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"System finds student details from same admin database\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1100,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Face verification with stored student photo\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1101,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1097,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1095,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1093,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-16 w-16 mx-auto text-green-600 mb-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1108,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-green-700 font-semibold\",\n                                                        children: \"Application Number Successfully Validated!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1109,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-green-600\",\n                                                        children: \"Starting face verification automatically...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1110,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-4 flex items-center justify-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1112,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-green-600\",\n                                                                children: \"Preparing camera...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1113,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1111,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1107,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 988,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 976,\n                                    columnNumber: 13\n                                }, this),\n                                currentStudent && qrValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"border-2 border-blue-200 bg-blue-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1126,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Student Found in Database\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1125,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: resetStation,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1130,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1129,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1124,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1123,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: currentStudent.image_url || \"/placeholder.svg\",\n                                                                    alt: currentStudent.name,\n                                                                    className: \"w-24 h-24 rounded-full border-4 border-blue-300 object-cover\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1137,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"absolute -bottom-2 -right-2 text-xs\",\n                                                                    children: \"Reference Photo\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1142,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1136,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-blue-800\",\n                                                                    children: currentStudent.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1147,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-sm font-mono\",\n                                                                    children: currentStudent.application_number\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1148,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-700\",\n                                                                    children: [\n                                                                        currentStudent.class,\n                                                                        \" - \",\n                                                                        currentStudent.department\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1151,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: \"default\",\n                                                                    className: \"text-xs bg-green-600\",\n                                                                    children: \"✅ Found in Database\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1154,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1146,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1135,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1160,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-blue-700\",\n                                                                    children: \"Phone:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1164,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-600\",\n                                                                    children: currentStudent.phone\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1165,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1163,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-blue-700\",\n                                                                    children: \"Schedule:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1168,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-600 text-xs\",\n                                                                    children: currentStudent.schedule || \"Not assigned\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1169,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1167,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1162,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                    className: \"border-yellow-200 bg-yellow-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4 text-yellow-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1174,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                            className: \"text-yellow-800\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Next Step:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1176,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Face verification required to match with stored photo above\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1175,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1173,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1134,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 1122,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 974,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                qrValidated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: verificationStatus === \"success\" ? \"border-green-200 bg-green-50\" : \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1192,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Step 2: Face Verification\",\n                                                            verificationStatus === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: \"ml-2\",\n                                                                children: \"✅ Verified\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1195,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1191,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        onClick: resetStation,\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"text-xs\",\n                                                        children: \"Scan Different QR\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1200,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1190,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1189,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative bg-gray-100 rounded-lg overflow-hidden\",\n                                                    children: cameraActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                        ref: videoRef,\n                                                                        className: \"w-full h-64 object-cover rounded\",\n                                                                        autoPlay: true,\n                                                                        muted: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1210,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute top-2 right-2 bg-black/50 text-white px-2 py-1 rounded text-xs\",\n                                                                        children: \"Live Camera\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1211,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    isScanning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 bg-black/20 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-white/90 p-4 rounded-lg text-center max-w-xs\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-2\",\n                                                                                children: [\n                                                                                    faceDetected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-green-600\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-2xl\",\n                                                                                                children: \"\\uD83D\\uDC64\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1222,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-sm font-medium\",\n                                                                                                children: \"Live Face Detected\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1223,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1221,\n                                                                                        columnNumber: 35\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-orange-600\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-2xl\",\n                                                                                                children: \"\\uD83D\\uDD0D\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1227,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-sm font-medium\",\n                                                                                                children: \"Looking for Face...\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1228,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1226,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"space-y-1\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex items-center justify-between text-xs\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: \"Liveness:\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                        lineNumber: 1234,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        className: livenessScore > 50 ? \"text-green-600\" : \"text-orange-600\",\n                                                                                                        children: [\n                                                                                                            livenessScore,\n                                                                                                            \"%\"\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                        lineNumber: 1235,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1233,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex items-center justify-between text-xs\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: \"Blink:\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                        lineNumber: 1240,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        className: blinkDetected ? \"text-green-600\" : \"text-gray-400\",\n                                                                                                        children: blinkDetected ? \"✅\" : \"⏳\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                        lineNumber: 1241,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1239,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1232,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1219,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1218,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1217,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1209,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        onClick: verifyFace,\n                                                                        disabled: isScanning || verificationStatus !== \"scanning\" || !qrValidated,\n                                                                        className: \"flex-1\",\n                                                                        children: isScanning ? \"Analyzing Face...\" : \"Verify Face Match\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1253,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        onClick: stopCamera,\n                                                                        variant: \"outline\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1261,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1260,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1252,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            liveDetectionStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                                className: \"border-blue-200 bg-blue-50\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1268,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                        className: \"text-blue-800\",\n                                                                        children: liveDetectionStatus\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1269,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1267,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            faceMatchScore !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-2 gap-4 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-gray-50 p-2 rounded\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-600\",\n                                                                                        children: \"Face Match\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1277,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-lg font-bold text-gray-800\",\n                                                                                        children: [\n                                                                                            faceMatchScore,\n                                                                                            \"%\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1278,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1276,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-gray-50 p-2 rounded\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-600\",\n                                                                                        children: \"Liveness\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1281,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-lg font-bold text-gray-800\",\n                                                                                        children: [\n                                                                                            livenessScore,\n                                                                                            \"%\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1282,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1280,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1275,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between text-xs\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"Face Match:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1287,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: faceMatchScore > 75 ? \"text-green-600\" : \"text-red-600\",\n                                                                                        children: faceMatchScore > 75 ? \"✅ Pass\" : \"❌ Fail\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1288,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1286,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"h-2 rounded-full \".concat(faceMatchScore > 75 ? \"bg-green-500\" : \"bg-red-500\"),\n                                                                                    style: {\n                                                                                        width: \"\".concat(faceMatchScore, \"%\")\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1293,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1292,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between text-xs\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"Liveness:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1299,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: livenessScore > 50 ? \"text-green-600\" : \"text-red-600\",\n                                                                                        children: livenessScore > 50 ? \"✅ Live\" : \"❌ Spoof\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1300,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1298,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"h-2 rounded-full \".concat(livenessScore > 50 ? \"bg-green-500\" : \"bg-red-500\"),\n                                                                                    style: {\n                                                                                        width: \"\".concat(livenessScore, \"%\")\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1305,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1304,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1285,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1274,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1208,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-64 flex items-center justify-center text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-12 w-12 mx-auto mb-2 opacity-50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1317,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Face Camera Ready\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1318,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: qrValidated ? \"Click to start face verification\" : \"Scan Application Number first\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1319,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1316,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1315,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1206,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 space-y-3\",\n                                                    children: [\n                                                        verificationStatus === \"idle\" && qrValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            onClick: startCamera,\n                                                            className: \"w-full\",\n                                                            variant: \"default\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1331,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Start Live Face Verification\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1330,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        verificationStatus === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                            className: \"border-green-200 bg-green-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1338,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                    className: \"text-green-800\",\n                                                                    children: [\n                                                                        \"✅ Live Face Verification Successful! Entry Recorded.\",\n                                                                        faceMatchScore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm mt-1 space-y-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC64 Face Match: \",\n                                                                                        faceMatchScore,\n                                                                                        \"%\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1343,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC93 Liveness: \",\n                                                                                        livenessScore,\n                                                                                        \"%\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1344,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC41️ Blink: \",\n                                                                                        blinkDetected ? \"Detected\" : \"Not Required\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1345,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1342,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1339,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1337,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        verificationStatus === \"failed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                            className: \"border-red-200 bg-red-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-red-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1354,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                    className: \"text-red-800\",\n                                                                    children: [\n                                                                        \"❌ Live Face Verification Failed!\",\n                                                                        faceMatchScore !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm mt-1 space-y-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC64 Face Match: \",\n                                                                                        faceMatchScore,\n                                                                                        \"% \",\n                                                                                        faceMatchScore > 75 ? \"✅\" : \"❌ (Need >75%)\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1359,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC93 Liveness: \",\n                                                                                        livenessScore,\n                                                                                        \"% \",\n                                                                                        livenessScore > 50 ? \"✅\" : \"❌ (Need >50%)\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1360,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC41️ Blink: \",\n                                                                                        blinkDetected ? \"✅ Detected\" : \"⚠️ Not detected\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1361,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs mt-2 text-red-700\",\n                                                                                    children: [\n                                                                                        faceMatchScore <= 75 && \"• Face doesn't match stored photo\",\n                                                                                        livenessScore <= 50 && \"• Possible photo/video spoofing detected\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1362,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1358,\n                                                                            columnNumber: 27\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm mt-1\",\n                                                                            children: \"Live face not detected in camera\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1368,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1355,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1353,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        showTryAgain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                                    className: \"border-orange-200 bg-orange-50\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-orange-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1377,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                            className: \"text-orange-800\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Verification Failed!\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1379,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \" Choose an option below:\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1378,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1376,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 gap-2\",\n                                                                    children: [\n                                                                        verificationStatus === \"failed\" && qrValidated ? // Face verification failed, but QR is valid\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                    onClick: tryAgainFace,\n                                                                                    variant: \"outline\",\n                                                                                    className: \"w-full\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                            className: \"mr-2 h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 1388,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        \"Try Face Verification Again\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1387,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                    onClick: tryAgainQR,\n                                                                                    variant: \"outline\",\n                                                                                    className: \"w-full\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                            className: \"mr-2 h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 1392,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        \"Scan Different QR Code\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1391,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true) : // QR validation failed\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                onClick: tryAgainQR,\n                                                                                variant: \"outline\",\n                                                                                className: \"w-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                        className: \"mr-2 h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1400,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    \"Try QR Scan Again\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1399,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            onClick: resetStation,\n                                                                            variant: \"destructive\",\n                                                                            className: \"w-full\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"mr-2 h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1406,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Reset Station\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1405,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1383,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1375,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        !qrValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                            className: \"border-yellow-200 bg-yellow-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-yellow-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1415,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                    className: \"text-yellow-800\",\n                                                                    children: \"Please scan and validate an Application Number first before face verification.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1416,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1414,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1328,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1205,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 1188,\n                                    columnNumber: 15\n                                }, this) : /* QR Not Validated - Show Waiting Message */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"border-gray-200 bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center gap-2 text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1429,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Step 2: Face Verification\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"outline\",\n                                                        className: \"ml-2 text-gray-500\",\n                                                        children: \"Waiting for QR\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1431,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1428,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1427,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-64 flex items-center justify-center bg-gray-100 rounded border-2 border-dashed border-gray-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-16 w-16 mx-auto mb-4 opacity-30\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1439,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium\",\n                                                            children: \"Face Verification Locked\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1440,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: \"Complete Step 1 (QR Scan) first\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1441,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-4 p-3 bg-yellow-50 rounded-lg border border-yellow-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-yellow-700\",\n                                                                children: \"\\uD83D\\uDD12 Face verification will activate after successful QR code validation\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1443,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1442,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1438,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1437,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1436,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 1426,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1458,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Today's Activity\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1457,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        onClick: loadTodayHistory,\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        children: \"View History\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1461,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1456,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1455,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-green-50 p-3 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold text-green-600\",\n                                                                        children: recentEntries.filter((e)=>e.status === 'entry').length\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1470,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-green-700\",\n                                                                        children: \"Entries Today\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1471,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1469,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-red-50 p-3 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold text-red-600\",\n                                                                        children: recentEntries.filter((e)=>e.status === 'exit').length\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1474,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-red-700\",\n                                                                        children: \"Exits Today\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1475,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1473,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1468,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 max-h-32 overflow-y-auto\",\n                                                        children: [\n                                                            recentEntries.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-500 text-center py-4\",\n                                                                children: \"No activity today\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1481,\n                                                                columnNumber: 23\n                                                            }, this) : recentEntries.slice(0, 3).map((log)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between p-2 bg-gray-50 rounded text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: log.student_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1486,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-gray-600\",\n                                                                                    children: formatDateTime(log.entryTime)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1487,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1485,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: log.status === \"entry\" ? \"default\" : \"secondary\",\n                                                                            className: \"text-xs\",\n                                                                            children: log.status === \"entry\" ? \"🟢\" : \"🔴\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1489,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, log.id, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1484,\n                                                                    columnNumber: 25\n                                                                }, this)),\n                                                            recentEntries.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 text-center\",\n                                                                children: [\n                                                                    \"+\",\n                                                                    recentEntries.length - 3,\n                                                                    \" more entries\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1496,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1479,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1467,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1466,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 1454,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 1185,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 972,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"Database Connection & System Integration\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 1508,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 1507,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-purple-700 mb-2\",\n                                                children: \"Same Database Connection:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1513,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                className: \"list-decimal list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Station connects to same database as Admin Panel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1515,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Students added in Admin are instantly available here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1516,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Entry logs are shared across both systems\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1517,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Real-time data synchronization\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1518,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Fallback to local storage if database unavailable\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1519,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Automatic data sync when connection restored\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1520,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1514,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1512,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-green-700 mb-2\",\n                                                children: \"Professional Station Features:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1524,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Dedicated website for security staff\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1526,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"No login required - direct access\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1527,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Real-time QR code scanning\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1528,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Live face verification system\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1529,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Automatic entry/exit logging\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1530,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Professional security interface\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1531,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1525,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1523,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 1511,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 1510,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 1506,\n                    columnNumber: 9\n                }, this),\n                showTodayHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: \"Today's Entry/Exit History\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1543,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>setShowTodayHistory(false),\n                                        variant: \"outline\",\n                                        children: \"✕ Close\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1544,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 1542,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-green-600\",\n                                                        children: todayEntries.filter((e)=>e.status === 'entry').length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1552,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-green-700\",\n                                                        children: \"Total Entries\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1553,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1551,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-red-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-red-600\",\n                                                        children: todayEntries.filter((e)=>e.status === 'exit').length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1556,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-700\",\n                                                        children: \"Total Exits\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1557,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1555,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-blue-600\",\n                                                        children: todayEntries.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1560,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-blue-700\",\n                                                        children: \"Total Activity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1561,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1559,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1550,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 max-h-96 overflow-y-auto\",\n                                        children: todayEntries.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"h-16 w-16 mx-auto text-gray-300 mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1568,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-lg\",\n                                                    children: \"No activity recorded today\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1569,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Entry/exit records will appear here\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1570,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1567,\n                                            columnNumber: 21\n                                        }, this) : todayEntries.map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border border-gray-200 rounded-lg p-4 bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-2xl\",\n                                                                            children: entry.status === 'entry' ? '🟢' : '🔴'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1578,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-semibold text-lg\",\n                                                                                    children: entry.student_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1582,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: [\n                                                                                        \"App: \",\n                                                                                        entry.application_number\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1583,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1581,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1577,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-gray-500\",\n                                                                                    children: \"Entry Time\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1589,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: formatDateTime(entry.entryTime)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1590,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1588,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        entry.exitTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-gray-500\",\n                                                                                    children: \"Exit Time\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1594,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: formatDateTime(entry.exitTime)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1595,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1593,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1587,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 flex items-center gap-4 text-xs text-gray-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1602,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"QR Verified\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1601,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1606,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"Face Verified\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1605,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1610,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                entry.status === 'entry' ? 'Entry' : 'Exit',\n                                                                                \" Recorded\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1609,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1600,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1576,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: entry.status === 'entry' ? 'default' : 'secondary',\n                                                                    className: \"mb-2\",\n                                                                    children: entry.status === 'entry' ? 'ENTRY' : 'EXIT'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1617,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: entry.verified ? '✅ Verified' : '⚠️ Pending'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1620,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1616,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1575,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, entry.id, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1574,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1565,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center text-sm text-gray-500 border-t pt-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"History resets daily at midnight • Real-time updates\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1631,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1630,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 1549,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                        lineNumber: 1541,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 1540,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n            lineNumber: 855,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n        lineNumber: 854,\n        columnNumber: 5\n    }, this);\n}\n_s(IDCardStation, \"oMokzf+ohBYkXynFcHVs2J4s1XE=\");\n_c = IDCardStation;\nvar _c;\n$RefreshReg$(_c, \"IDCardStation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});