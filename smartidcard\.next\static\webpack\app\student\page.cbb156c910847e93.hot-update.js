"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/student/page",{

/***/ "(app-pages-browser)/./app/student/page.tsx":
/*!******************************!*\
  !*** ./app/student/page.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentApp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _lib_database_store__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/database-store */ \"(app-pages-browser)/./lib/database-store.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction StudentApp() {\n    _s();\n    const [currentStudent, setCurrentStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [studentEntries, setStudentEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [copiedQR, setCopiedQR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const entriesPerPage = 5;\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentApp.useEffect\": ()=>{\n            if (true) {\n                const studentLoggedIn = localStorage.getItem(\"studentLoggedIn\");\n                const studentId = localStorage.getItem(\"studentId\");\n                if (!studentLoggedIn || !studentId) {\n                    router.push(\"/\");\n                    return;\n                }\n                loadStudentData(studentId);\n            }\n        }\n    }[\"StudentApp.useEffect\"], [\n        router\n    ]);\n    // Auto-refresh student entries every 3 seconds for real-time updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentApp.useEffect\": ()=>{\n            if (!isAuthenticated || !currentStudent) return;\n            const interval = setInterval({\n                \"StudentApp.useEffect.interval\": ()=>{\n                    console.log(\"🔄 Auto-refreshing student entries...\");\n                    loadStudentEntries();\n                }\n            }[\"StudentApp.useEffect.interval\"], 3000) // 3 seconds for faster updates\n            ;\n            return ({\n                \"StudentApp.useEffect\": ()=>clearInterval(interval)\n            })[\"StudentApp.useEffect\"];\n        }\n    }[\"StudentApp.useEffect\"], [\n        isAuthenticated,\n        currentStudent\n    ]);\n    const loadStudentEntries = async ()=>{\n        if (!currentStudent) return;\n        try {\n            console.log(\"\\uD83D\\uDD0D Fetching entries for student: \".concat(currentStudent.name, \" (\").concat(currentStudent.application_number, \")\"));\n            // Try to get all entries and filter for this student\n            const entriesRes = await fetch('/api/entries');\n            if (entriesRes.ok) {\n                const allEntries = await entriesRes.json();\n                console.log(\"\\uD83D\\uDCCA Total entries in database: \".concat(allEntries.length));\n                // Filter entries for this student by both student_id and application_number\n                const studentEntries = allEntries.filter((entry)=>{\n                    const matchesId = entry.student_id === currentStudent.id;\n                    const matchesAppNumber = entry.application_number === currentStudent.application_number;\n                    const matchesName = entry.student_name === currentStudent.name;\n                    return matchesId || matchesAppNumber || matchesName;\n                });\n                // Sort by entry time (newest first)\n                studentEntries.sort((a, b)=>{\n                    const dateA = new Date(a.entry_time || a.entryTime || a.timestamp);\n                    const dateB = new Date(b.entry_time || b.entryTime || b.timestamp);\n                    return dateB.getTime() - dateA.getTime();\n                });\n                setStudentEntries(studentEntries);\n                console.log(\"✅ Found \".concat(studentEntries.length, \" entries for \").concat(currentStudent.name, \":\"), studentEntries);\n                // Debug: Check entry data structure\n                if (studentEntries.length > 0) {\n                    console.log(\"📊 Sample entry structure:\", studentEntries[0]);\n                    console.log(\"📊 Entry properties:\", Object.keys(studentEntries[0]));\n                }\n            } else {\n                console.error(\"❌ API error: \".concat(entriesRes.status));\n            }\n        } catch (error) {\n            console.error(\"❌ Error refreshing entries:\", error);\n        }\n    };\n    const loadStudentData = async (studentId)=>{\n        try {\n            setLoading(true);\n            setIsAuthenticated(true);\n            // Get student data from shared MongoDB via API\n            const studentsRes = await fetch('/api/students');\n            if (!studentsRes.ok) throw new Error('Failed to fetch students');\n            const students = await studentsRes.json();\n            const student = students.find((s)=>s.id === studentId);\n            if (student) {\n                setCurrentStudent(student);\n                // Get student's entry history from shared MongoDB\n                try {\n                    const entriesRes = await fetch(\"/api/entries?studentId=\".concat(student.id));\n                    if (entriesRes.ok) {\n                        const allEntries = await entriesRes.json();\n                        // Filter entries for this student\n                        const studentEntries = allEntries.filter((entry)=>entry.student_id === student.id || entry.application_number === student.application_number);\n                        setStudentEntries(studentEntries);\n                        console.log(\"✅ Loaded \".concat(studentEntries.length, \" entries for student \").concat(student.name));\n                    } else {\n                        console.log(\"⚠️ Could not fetch entries from API, using fallback\");\n                        const entries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_8__.dbStore.getStudentEntries(student.id);\n                        setStudentEntries(entries);\n                    }\n                } catch (entriesError) {\n                    console.log(\"⚠️ API error, using database fallback for entries\");\n                    const entries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_8__.dbStore.getStudentEntries(student.id);\n                    setStudentEntries(entries);\n                }\n            } else {\n                handleLogout();\n            }\n        } catch (error) {\n            console.error(\"Error loading student data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleLogout = ()=>{\n        if (true) {\n            localStorage.removeItem(\"studentLoggedIn\");\n            localStorage.removeItem(\"studentId\");\n            localStorage.removeItem(\"studentAppNumber\");\n        }\n        router.push(\"/\");\n    };\n    const handleRefresh = ()=>{\n        if (currentStudent) {\n            loadStudentData(currentStudent.id);\n        }\n    };\n    const generateSimpleQRCode = ()=>{\n        if (!currentStudent) return \"\";\n        return currentStudent.application_number;\n    };\n    const copyQRData = async ()=>{\n        try {\n            const qrData = generateSimpleQRCode();\n            await navigator.clipboard.writeText(qrData);\n            setCopiedQR(true);\n            setTimeout(()=>setCopiedQR(false), 2000);\n        } catch (error) {\n            alert(\"Failed to copy QR data\");\n        }\n    };\n    const formatTime = (date)=>{\n        if (!date) return \"N/A\";\n        const dateObj = typeof date === 'string' ? new Date(date) : date;\n        if (isNaN(dateObj.getTime())) return \"Invalid Date\";\n        return dateObj.toLocaleString(\"en-IN\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const formatDate = (date)=>{\n        if (!date) return \"N/A\";\n        const dateObj = typeof date === 'string' ? new Date(date) : date;\n        if (isNaN(dateObj.getTime())) return \"Invalid Date\";\n        return dateObj.toLocaleString(\"en-IN\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\"\n        });\n    };\n    const toggleSection = (section)=>{\n        setActiveSection(activeSection === section ? null : section);\n    };\n    if (!isAuthenticated || !currentStudent) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-blue-50 p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-700\",\n                        children: \"Loading student data...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                lineNumber: 205,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n            lineNumber: 204,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-green-50 to-blue-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"shadow-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: (currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.image_url) || \"/placeholder.svg?height=50&width=50\",\n                                            alt: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name,\n                                            className: \"w-10 h-10 rounded-full border-2 border-green-200 object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: [\n                                                        \"Welcome, \",\n                                                        currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"App No: \",\n                                                        currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.application_number\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 w-full sm:w-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleRefresh,\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            disabled: loading,\n                                            className: \"flex-1 sm:flex-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"mr-1 h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only sm:not-sr-only\",\n                                                    children: \"Refresh\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleLogout,\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"flex-1 sm:flex-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-1 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only sm:not-sr-only\",\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: ()=>router.push(\"/\"),\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"flex-1 sm:flex-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"mr-1 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only sm:not-sr-only\",\n                                                    children: \"Home\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"border border-blue-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>toggleSection(\"idCard\"),\n                                    className: \"w-full p-4 flex items-center justify-between text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-100 p-2 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-5 w-5 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Digital ID Card\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Show your QR code at security stations\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this),\n                                        activeSection === \"idCard\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this),\n                                activeSection === \"idCard\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"pt-0 px-4 pb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl p-5 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-xl font-bold\",\n                                                                children: \"College Identity Card\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-blue-100 text-sm\",\n                                                                children: \"Official Identification Document\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-blue-200\",\n                                                                children: \"Valid Until\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold\",\n                                                                children: \"31/12/2025\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col md:flex-row gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white border-2 border-white rounded-lg overflow-hidden\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: (currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.image_url) || \"/placeholder.svg?height=100&width=80\",\n                                                                            alt: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name,\n                                                                            className: \"w-20 h-24 object-cover\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                            lineNumber: 310,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 309,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                                className: \"text-lg font-bold\",\n                                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 318,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2 space-y-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-xs text-blue-200\",\n                                                                                                children: \"Application Number\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                                lineNumber: 321,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"font-mono font-bold\",\n                                                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.application_number\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                                lineNumber: 322,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 320,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-xs text-blue-200\",\n                                                                                                children: \"Department\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                                lineNumber: 325,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"font-bold\",\n                                                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.department\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                                lineNumber: 326,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 324,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 319,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 317,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 grid grid-cols-2 gap-3 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-blue-200\",\n                                                                                children: \"Class\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 334,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-bold\",\n                                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.class\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 335,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 333,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-blue-200\",\n                                                                                children: \"Phone\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 338,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-bold\",\n                                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.phone\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 339,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 337,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-white p-2 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: \"https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=\".concat(encodeURIComponent(generateSimpleQRCode())),\n                                                                    alt: \"Student QR Code\",\n                                                                    className: \"w-32 h-32\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 346,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2 text-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-mono text-xs bg-blue-400/20 px-2 py-1 rounded\",\n                                                                    children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.application_number\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                value: generateSimpleQRCode(),\n                                                                readOnly: true,\n                                                                className: \"bg-white/10 border-white/20 text-white placeholder-white/50 text-center font-mono text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                onClick: copyQRData,\n                                                                size: \"sm\",\n                                                                className: \"absolute top-1 right-1 h-6 px-2 bg-white/20 hover:bg-white/30\",\n                                                                children: copiedQR ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 37\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 69\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-blue-200 mt-1 text-center\",\n                                                        children: \"Copy application number for manual entry at station\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"border border-green-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>toggleSection(\"details\"),\n                                    className: \"w-full p-4 flex items-center justify-between text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-100 p-2 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-5 w-5 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Personal Details\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"View your registration information\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 15\n                                        }, this),\n                                        activeSection === \"details\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 13\n                                }, this),\n                                activeSection === \"details\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"pt-0 px-4 pb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col md:flex-row gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: (currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.image_url) || \"/placeholder.svg?height=120&width=120\",\n                                                        alt: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name,\n                                                        className: \"w-24 h-24 rounded-full border-4 border-green-200 object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"mt-2 text-lg font-semibold\",\n                                                        children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"mt-1\",\n                                                        children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.class\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-gray-500 text-sm\",\n                                                                        children: \"Phone\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 422,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.phone\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 423,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-gray-500 text-sm\",\n                                                                        children: \"Email\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 426,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: (currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.email) || \"Not provided\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 427,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-gray-500 text-sm\",\n                                                                        children: \"Department\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 430,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.department\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 431,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 429,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-gray-500 text-sm\",\n                                                                        children: \"Schedule\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 434,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: (currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.schedule) || \"Not assigned\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 435,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 433,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                className: \"text-gray-500 text-sm\",\n                                                                children: \"Application Number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                className: \"font-mono mt-1\",\n                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.application_number\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"border border-amber-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>toggleSection(\"history\"),\n                                    className: \"w-full p-4 flex items-center justify-between text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-amber-100 p-2 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5 text-amber-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Entry/Exit History\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"View your campus access records\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 15\n                                        }, this),\n                                        activeSection === \"history\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 13\n                                }, this),\n                                activeSection === \"history\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"pt-0 px-4 pb-4\",\n                                    children: studentEntries.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-8 w-8 mx-auto text-gray-300 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 text-sm\",\n                                                children: \"No entries recorded yet\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: studentEntries.slice((currentPage - 1) * entriesPerPage, currentPage * entriesPerPage).map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-2 bg-gray-50 rounded-lg border-l-4 border-l-blue-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 rounded-full \".concat(entry.status === \"entry\" ? \"bg-green-500\" : \"bg-red-500\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 492,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm font-medium\",\n                                                                                        children: entry.status === \"entry\" ? \"Entry\" : \"Exit\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 495,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                        variant: \"outline\",\n                                                                                        className: \"text-xs px-1 py-0\",\n                                                                                        children: entry.verified ? \"✓\" : \"⚠\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 498,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 494,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-gray-500\",\n                                                                                children: [\n                                                                                    formatDate(entry.entryTime),\n                                                                                    \" • \",\n                                                                                    formatTime(entry.entryTime)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 502,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 493,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 491,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-400\",\n                                                                    children: entry.status === \"entry\" ? \"In\" : \"Out\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 508,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 507,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, entry.id, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 27\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 21\n                                            }, this),\n                                            studentEntries.length > entriesPerPage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mt-4 pt-3 border-t\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            \"Showing \",\n                                                            (currentPage - 1) * entriesPerPage + 1,\n                                                            \"-\",\n                                                            Math.min(currentPage * entriesPerPage, studentEntries.length),\n                                                            \" of \",\n                                                            studentEntries.length\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>setCurrentPage((prev)=>Math.max(1, prev - 1)),\n                                                                disabled: currentPage === 1,\n                                                                className: \"h-7 w-7 p-0\",\n                                                                children: \"←\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 523,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>setCurrentPage((prev)=>Math.min(Math.ceil(studentEntries.length / entriesPerPage), prev + 1)),\n                                                                disabled: currentPage >= Math.ceil(studentEntries.length / entriesPerPage),\n                                                                className: \"h-7 w-7 p-0\",\n                                                                children: \"→\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-3 pt-3 border-t bg-blue-50 rounded-lg p-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: [\n                                                                \"Total Entries: \",\n                                                                studentEntries.length\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: [\n                                                                \"Last Updated: \",\n                                                                new Date().toLocaleTimeString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 549,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"border border-blue-100\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            className: \"pb-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-5 w-5 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 565,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"How to Use Your Digital ID Card\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                lineNumber: 564,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 563,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-50 p-4 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-blue-700 mb-2 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 573,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"QR Code Scanning\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                    className: \"list-decimal list-inside space-y-1 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Show your QR code to station operator\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 577,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Operator will scan with the camera\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 578,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Hold QR code steady in front of camera\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 579,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"System retrieves your details automatically\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Proceed to face verification\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 581,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 576,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-50 p-4 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-green-700 mb-2 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 586,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Manual Input Option\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 585,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"list-disc list-inside space-y-1 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Copy your Application Number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: 'Go to station\\'s \"Manual Entry\" section'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 591,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Paste your Application Number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 592,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: 'Click \"Validate\" to retrieve details'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 593,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Continue with face verification\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 594,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 584,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 570,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.Alert, {\n                                    className: \"mt-4 bg-yellow-50 border-yellow-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.AlertDescription, {\n                                        className: \"text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold\",\n                                                children: \"Important:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" Your digital ID card is for official use only. Do not share it with unauthorized persons. Report lost cards immediately.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 599,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 569,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                    lineNumber: 562,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n            lineNumber: 215,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n        lineNumber: 214,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentApp, \"oWzI3S3hIeSS/z40AxYZOhRxnqU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = StudentApp;\nvar _c;\n$RefreshReg$(_c, \"StudentApp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/student/page.tsx\n"));

/***/ })

});