"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./app/admin/page.tsx":
/*!****************************!*\
  !*** ./app/admin/page.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_database_store__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/database-store */ \"(app-pages-browser)/./lib/database-store.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_13__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AdminPanel() {\n    _s();\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingStudent, setEditingStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [copiedText, setCopiedText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [databaseConnected, setDatabaseConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [storageInfo, setStorageInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        mode: \"Local\",\n        studentsCount: 0,\n        entriesCount: 0\n    });\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalStudents: 0,\n        todayEntries: 0,\n        totalEntries: 0\n    });\n    const [showEntriesModal, setShowEntriesModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allEntries, setAllEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [todayEntries, setTodayEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [newStudent, setNewStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        phone: \"\",\n        email: \"\",\n        class: \"\",\n        department: \"\",\n        schedule: \"\",\n        image: \"\"\n    });\n    const [imagePreview, setImagePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageFile, setImageFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPanel.useEffect\": ()=>{\n            // Check if admin is logged in\n            if (true) {\n                const adminLoggedIn = localStorage.getItem(\"adminLoggedIn\");\n                if (!adminLoggedIn) {\n                    router.push(\"/\");\n                    return;\n                }\n            }\n            setIsAuthenticated(true);\n            checkDatabaseConnection();\n            loadData();\n            // Set up real-time polling for entry updates\n            const interval = setInterval({\n                \"AdminPanel.useEffect.interval\": ()=>{\n                    loadData();\n                    console.log(\"🔄 Admin Panel: Auto-refreshing entry data...\");\n                }\n            }[\"AdminPanel.useEffect.interval\"], 5000) // Refresh every 5 seconds\n            ;\n            return ({\n                \"AdminPanel.useEffect\": ()=>clearInterval(interval)\n            })[\"AdminPanel.useEffect\"];\n        }\n    }[\"AdminPanel.useEffect\"], [\n        router\n    ]);\n    const checkDatabaseConnection = ()=>{\n        const connected = _lib_supabase__WEBPACK_IMPORTED_MODULE_12__.supabase !== null;\n        setDatabaseConnected(connected);\n        setStorageInfo(_lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getStorageInfo());\n    };\n    const loadData = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"🔄 Admin Panel: Loading data...\");\n            const studentsData = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getStudents();\n            console.log(\"📊 Students loaded:\", studentsData.length);\n            const todayEntriesData = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getTodayEntries();\n            console.log(\"📊 Today's entries loaded:\", todayEntriesData.length);\n            const allEntriesData = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getAllEntries();\n            console.log(\"📊 All entries loaded:\", allEntriesData.length);\n            setStudents(studentsData);\n            setTodayEntries(todayEntriesData);\n            setAllEntries(allEntriesData);\n            setStats({\n                totalStudents: studentsData.length,\n                todayEntries: todayEntriesData.length,\n                totalEntries: allEntriesData.length\n            });\n            setStorageInfo(_lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getStorageInfo());\n            console.log(\"✅ Admin Panel: Data loaded successfully\");\n            console.log(\"📊 Final stats:\", {\n                students: studentsData.length,\n                todayEntries: todayEntriesData.length,\n                totalEntries: allEntriesData.length\n            });\n        } catch (error) {\n            console.error(\"❌ Error loading data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleRefresh = ()=>{\n        loadData();\n    };\n    const handleLogout = ()=>{\n        if (true) {\n            localStorage.removeItem(\"adminLoggedIn\");\n            localStorage.removeItem(\"adminUsername\");\n        }\n        router.push(\"/\");\n    };\n    const formatDateTime = (dateString)=>{\n        const date = new Date(dateString);\n        return date.toLocaleString('en-IN', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    const handleViewEntries = ()=>{\n        setShowEntriesModal(true);\n    };\n    // Handle image file selection\n    const handleImageSelect = (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (!file) return;\n        // Validate file type\n        if (!file.type.startsWith(\"image/\")) {\n            alert(\"Please select a valid image file (JPG, PNG, GIF, etc.)\");\n            return;\n        }\n        // Validate file size (max 5MB)\n        if (file.size > 5 * 1024 * 1024) {\n            alert(\"Image size should be less than 5MB\");\n            return;\n        }\n        setImageFile(file);\n        // Create preview\n        const reader = new FileReader();\n        reader.onload = (e)=>{\n            var _e_target;\n            const result = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n            setImagePreview(result);\n            setNewStudent({\n                ...newStudent,\n                image: result\n            });\n        };\n        reader.readAsDataURL(file);\n    };\n    // Remove selected image\n    const removeImage = ()=>{\n        setImageFile(null);\n        setImagePreview(null);\n        setNewStudent({\n            ...newStudent,\n            image: \"\"\n        });\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    // Take photo using camera\n    const takePhoto = async ()=>{\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({\n                video: true\n            });\n            // Create a video element to capture the stream\n            const video = document.createElement(\"video\");\n            video.srcObject = stream;\n            video.autoplay = true;\n            // Create a modal or popup to show camera feed\n            const modal = document.createElement(\"div\");\n            modal.style.cssText = \"\\n        position: fixed;\\n        top: 0;\\n        left: 0;\\n        width: 100%;\\n        height: 100%;\\n        background: rgba(0,0,0,0.8);\\n        display: flex;\\n        justify-content: center;\\n        align-items: center;\\n        z-index: 1000;\\n      \";\n            const container = document.createElement(\"div\");\n            container.style.cssText = \"\\n        background: white;\\n        padding: 20px;\\n        border-radius: 10px;\\n        text-align: center;\\n      \";\n            const canvas = document.createElement(\"canvas\");\n            const captureBtn = document.createElement(\"button\");\n            captureBtn.textContent = \"Capture Photo\";\n            captureBtn.style.cssText = \"\\n        background: #3b82f6;\\n        color: white;\\n        padding: 10px 20px;\\n        border: none;\\n        border-radius: 5px;\\n        margin: 10px;\\n        cursor: pointer;\\n      \";\n            const cancelBtn = document.createElement(\"button\");\n            cancelBtn.textContent = \"Cancel\";\n            cancelBtn.style.cssText = \"\\n        background: #6b7280;\\n        color: white;\\n        padding: 10px 20px;\\n        border: none;\\n        border-radius: 5px;\\n        margin: 10px;\\n        cursor: pointer;\\n      \";\n            container.appendChild(video);\n            container.appendChild(document.createElement(\"br\"));\n            container.appendChild(captureBtn);\n            container.appendChild(cancelBtn);\n            modal.appendChild(container);\n            document.body.appendChild(modal);\n            // Capture photo\n            captureBtn.onclick = ()=>{\n                canvas.width = video.videoWidth;\n                canvas.height = video.videoHeight;\n                const ctx = canvas.getContext(\"2d\");\n                ctx === null || ctx === void 0 ? void 0 : ctx.drawImage(video, 0, 0);\n                const imageData = canvas.toDataURL(\"image/jpeg\", 0.8);\n                setImagePreview(imageData);\n                setNewStudent({\n                    ...newStudent,\n                    image: imageData\n                });\n                // Stop camera and close modal\n                stream.getTracks().forEach((track)=>track.stop());\n                document.body.removeChild(modal);\n            };\n            // Cancel\n            cancelBtn.onclick = ()=>{\n                stream.getTracks().forEach((track)=>track.stop());\n                document.body.removeChild(modal);\n            };\n        } catch (error) {\n            alert(\"Camera access denied or not available\");\n        }\n    };\n    const validateForm = ()=>{\n        if (!newStudent.name.trim()) {\n            alert(\"Student name is required\");\n            return false;\n        }\n        if (!newStudent.phone.trim()) {\n            alert(\"Phone number is required\");\n            return false;\n        }\n        if (newStudent.phone.length !== 10 || !/^\\d+$/.test(newStudent.phone)) {\n            alert(\"Phone number must be exactly 10 digits\");\n            return false;\n        }\n        if (!newStudent.class) {\n            alert(\"Class selection is required\");\n            return false;\n        }\n        if (!newStudent.image) {\n            alert(\"Student photo is required. Please upload an image or take a photo.\");\n            return false;\n        }\n        return true;\n    };\n    const handleAddStudent = async ()=>{\n        if (!validateForm()) return;\n        // Check if phone number already exists\n        const existingStudent = students.find((s)=>s.phone === newStudent.phone);\n        if (existingStudent) {\n            alert(\"Phone number already exists!\");\n            return;\n        }\n        setLoading(true);\n        try {\n            const applicationNumber = _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.generateApplicationNumber();\n            const student = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.addStudent({\n                ...newStudent,\n                application_number: applicationNumber,\n                image_url: newStudent.image\n            });\n            await loadData();\n            resetForm();\n            alert(\"Student Added Successfully!\\n\\nName: \".concat(student.name, \"\\nApplication Number: \").concat(applicationNumber, \"\\nPhone: \").concat(student.phone, \"\\n\\nPlease provide Application Number and Phone Number to the student for login.\\n\\nData saved in \").concat(storageInfo.mode, \" storage.\"));\n        } catch (error) {\n            alert(\"Error adding student. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleEditStudent = (student)=>{\n        setEditingStudent(student);\n        setNewStudent({\n            name: student.name,\n            phone: student.phone,\n            email: student.email || \"\",\n            class: student.class,\n            department: student.department || \"\",\n            schedule: student.schedule || \"\",\n            image: student.image_url || \"\"\n        });\n        setImagePreview(student.image_url || null);\n        setShowAddForm(false);\n    };\n    const handleUpdateStudent = async ()=>{\n        if (!validateForm() || !editingStudent) return;\n        // Check if phone number already exists (excluding current student)\n        const existingStudent = students.find((s)=>s.phone === newStudent.phone && s.id !== editingStudent.id);\n        if (existingStudent) {\n            alert(\"Phone number already exists!\");\n            return;\n        }\n        setLoading(true);\n        try {\n            await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.updateStudent(editingStudent.id, {\n                name: newStudent.name,\n                phone: newStudent.phone,\n                email: newStudent.email || null,\n                class: newStudent.class,\n                department: newStudent.department || null,\n                schedule: newStudent.schedule || null,\n                image_url: newStudent.image\n            });\n            await loadData();\n            resetForm();\n            alert(\"Student updated successfully!\\n\\nData saved in \".concat(storageInfo.mode, \" storage.\"));\n        } catch (error) {\n            alert(\"Error updating student. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDeleteStudent = async (student)=>{\n        if (confirm(\"Are you sure you want to delete \".concat(student.name, \"?\\n\\nThis action cannot be undone.\"))) {\n            try {\n                setLoading(true);\n                await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.deleteStudent(student.id);\n                await loadData();\n                alert(\"Student deleted successfully!\\n\\nData updated in \".concat(storageInfo.mode, \" storage.\"));\n            } catch (error) {\n                alert(\"Error deleting student. Please try again.\");\n            } finally{\n                setLoading(false);\n            }\n        }\n    };\n    const copyToClipboard = async (text, type)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n            setCopiedText(\"\".concat(type, \"-\").concat(text));\n            setTimeout(()=>setCopiedText(null), 2000);\n        } catch (error) {\n            alert(\"Failed to copy to clipboard\");\n        }\n    };\n    const resetForm = ()=>{\n        setNewStudent({\n            name: \"\",\n            phone: \"\",\n            email: \"\",\n            class: \"\",\n            department: \"\",\n            schedule: \"\",\n            image: \"\"\n        });\n        setImagePreview(null);\n        setImageFile(null);\n        setShowAddForm(false);\n        setEditingStudent(null);\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    if (!isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 439,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-3xl\",\n                                            children: \"Admin Panel\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            className: \"text-lg\",\n                                            children: [\n                                                \"Student Management System - \",\n                                                storageInfo.mode,\n                                                \" Storage\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 px-3 py-2 bg-green-50 rounded-lg border border-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-green-700 font-medium\",\n                                                    children: \"Live Updates (5s)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleRefresh,\n                                            variant: \"outline\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Refresh\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleLogout,\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Logout\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_13___default()), {\n                                            href: \"/\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                size: \"lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Home\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 447,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 446,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.Alert, {\n                    className: databaseConnected ? \"border-green-200 bg-green-50\" : \"border-yellow-200 bg-yellow-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"h-4 w-4 \".concat(databaseConnected ? \"text-green-600\" : \"text-yellow-600\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.AlertDescription, {\n                            className: databaseConnected ? \"text-green-800\" : \"text-yellow-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: [\n                                        storageInfo.mode,\n                                        \" Storage Active:\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 13\n                                }, this),\n                                \" \",\n                                databaseConnected ? \"Data syncs across all devices automatically\" : \"Data saved locally on this device (\".concat(storageInfo.studentsCount, \" students, \").concat(storageInfo.entriesCount, \" entries)\")\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 482,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 480,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: stats.totalStudents\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Total Students\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 493,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-8 w-8 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: stats.todayEntries\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Today's Entries\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 505,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 504,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-8 w-8 text-purple-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold\",\n                                                            children: stats.totalEntries\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 522,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Total Entries\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 523,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleViewEntries,\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: \"View All\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 517,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 491,\n                    columnNumber: 9\n                }, this),\n                !showAddForm && !editingStudent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>setShowAddForm(true),\n                            className: \"w-full h-16 text-lg\",\n                            disabled: loading,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"mr-2 h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 17\n                                }, this),\n                                \"Add New Student\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 538,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 537,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 536,\n                    columnNumber: 11\n                }, this),\n                (showAddForm || editingStudent) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: editingStudent ? \"Edit Student\" : \"Add New Student\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: [\n                                        editingStudent ? \"Update student information\" : \"Fill required fields to register a new student\",\n                                        \" - Data will be saved in \",\n                                        storageInfo.mode,\n                                        \" storage\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 549,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            className: \"text-base font-semibold\",\n                                            children: \"Student Photo *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 17\n                                        }, this),\n                                        imagePreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: imagePreview || \"/placeholder.svg\",\n                                                            alt: \"Student preview\",\n                                                            className: \"w-32 h-32 rounded-full border-4 border-blue-200 object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 565,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: removeImage,\n                                                            size: \"sm\",\n                                                            variant: \"destructive\",\n                                                            className: \"absolute -top-2 -right-2 h-6 w-6 rounded-full p-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 570,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 564,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-green-600\",\n                                                            children: \"✅ Photo uploaded successfully\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: ()=>{\n                                                                var _fileInputRef_current;\n                                                                return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                            },\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 582,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Change Photo\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 581,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"h-16 w-16 mx-auto text-gray-400 mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4\",\n                                                    children: \"Upload student photo (Required)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 590,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: ()=>{\n                                                                var _fileInputRef_current;\n                                                                return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                            },\n                                                            variant: \"outline\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 593,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Upload Photo\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 592,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: takePhoto,\n                                                            variant: \"outline\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 597,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Take Photo\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 596,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 591,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-2\",\n                                                    children: \"Supported formats: JPG, PNG, GIF (Max 5MB)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 588,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            ref: fileInputRef,\n                                            type: \"file\",\n                                            accept: \"image/*\",\n                                            onChange: handleImageSelect,\n                                            className: \"hidden\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 606,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 558,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_9__.Separator, {}, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 615,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"name\",\n                                                    children: \"Student Name *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 620,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"name\",\n                                                    value: newStudent.name,\n                                                    onChange: (e)=>setNewStudent({\n                                                            ...newStudent,\n                                                            name: e.target.value\n                                                        }),\n                                                    placeholder: \"Enter full name\",\n                                                    disabled: loading\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 621,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 619,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"phone\",\n                                                    children: \"Phone Number *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 630,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"phone\",\n                                                    value: newStudent.phone,\n                                                    onChange: (e)=>setNewStudent({\n                                                            ...newStudent,\n                                                            phone: e.target.value\n                                                        }),\n                                                    placeholder: \"10-digit phone number\",\n                                                    maxLength: 10,\n                                                    disabled: loading\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 631,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 629,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"email\",\n                                                    children: \"Email Address\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"email\",\n                                                    type: \"email\",\n                                                    value: newStudent.email,\n                                                    onChange: (e)=>setNewStudent({\n                                                            ...newStudent,\n                                                            email: e.target.value\n                                                        }),\n                                                    placeholder: \"<EMAIL>\",\n                                                    disabled: loading\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 642,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"class\",\n                                                    children: \"Class *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 652,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: newStudent.class,\n                                                    onValueChange: (value)=>setNewStudent({\n                                                            ...newStudent,\n                                                            class: value\n                                                        }),\n                                                    disabled: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select class\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 659,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 658,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"10th-A\",\n                                                                    children: \"10th A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 662,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"10th-B\",\n                                                                    children: \"10th B\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 663,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"10th-C\",\n                                                                    children: \"10th C\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 664,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"11th-A\",\n                                                                    children: \"11th A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 665,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"11th-B\",\n                                                                    children: \"11th B\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 666,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"11th-C\",\n                                                                    children: \"11th C\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 667,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"12th-A\",\n                                                                    children: \"12th A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 668,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"12th-B\",\n                                                                    children: \"12th B\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 669,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"12th-C\",\n                                                                    children: \"12th C\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 670,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 661,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 651,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"department\",\n                                                    children: \"Department\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 675,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: newStudent.department,\n                                                    onValueChange: (value)=>setNewStudent({\n                                                            ...newStudent,\n                                                            department: value\n                                                        }),\n                                                    disabled: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select department\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 682,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 681,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Science\",\n                                                                    children: \"Science\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 685,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Commerce\",\n                                                                    children: \"Commerce\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 686,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Arts\",\n                                                                    children: \"Arts\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 687,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Computer Science\",\n                                                                    children: \"Computer Science\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 688,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 684,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 676,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 674,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"schedule\",\n                                                    children: \"Time Schedule\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 693,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: newStudent.schedule,\n                                                    onValueChange: (value)=>setNewStudent({\n                                                            ...newStudent,\n                                                            schedule: value\n                                                        }),\n                                                    disabled: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select schedule\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 700,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Morning Shift (8:00 AM - 2:00 PM)\",\n                                                                    children: \"Morning Shift (8:00 AM - 2:00 PM)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 703,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Afternoon Shift (2:00 PM - 8:00 PM)\",\n                                                                    children: \"Afternoon Shift (2:00 PM - 8:00 PM)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 706,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Full Day (8:00 AM - 4:00 PM)\",\n                                                                    children: \"Full Day (8:00 AM - 4:00 PM)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 709,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 702,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 692,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 618,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_9__.Separator, {}, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 715,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        editingStudent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleUpdateStudent,\n                                            className: \"flex-1\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 720,\n                                                    columnNumber: 21\n                                                }, this),\n                                                loading ? \"Updating...\" : \"Update Student\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 719,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleAddStudent,\n                                            className: \"flex-1\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 725,\n                                                    columnNumber: 21\n                                                }, this),\n                                                loading ? \"Adding...\" : \"Add Student\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 724,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: resetForm,\n                                            variant: \"outline\",\n                                            className: \"flex-1 bg-transparent\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 730,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Cancel\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 729,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 717,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 556,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 548,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: [\n                                        \"Registered Students (\",\n                                        students.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 741,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: [\n                                        \"All registered students with their login credentials - Stored in \",\n                                        storageInfo.mode,\n                                        \" storage\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 742,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 740,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: students.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-16 w-16 mx-auto text-gray-300 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 749,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-500 mb-2\",\n                                        children: \"No students registered yet\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 750,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: 'Click \"Add New Student\" to get started'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 751,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 748,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: students.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: student.image_url || \"/placeholder.svg?height=60&width=60\",\n                                                        alt: student.name,\n                                                        className: \"w-12 h-12 rounded-full border-2 border-gray-200 object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 758,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-lg\",\n                                                                children: student.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 764,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: [\n                                                                    student.class,\n                                                                    \" \",\n                                                                    student.department && \"- \".concat(student.department)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 765,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: student.phone\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 768,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            student.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: student.email\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 769,\n                                                                columnNumber: 43\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 763,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 757,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"font-mono text-xs\",\n                                                                        children: [\n                                                                            \"App: \",\n                                                                            student.application_number\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 777,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"ghost\",\n                                                                        onClick: ()=>copyToClipboard(student.application_number, \"app\"),\n                                                                        className: \"h-6 w-6 p-0\",\n                                                                        children: copiedText === \"app-\".concat(student.application_number) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 787,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 789,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 780,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 776,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                        variant: \"secondary\",\n                                                                        className: \"text-xs\",\n                                                                        children: [\n                                                                            \"Phone: \",\n                                                                            student.phone\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 794,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"ghost\",\n                                                                        onClick: ()=>copyToClipboard(student.phone, \"phone\"),\n                                                                        className: \"h-6 w-6 p-0\",\n                                                                        children: copiedText === \"phone-\".concat(student.phone) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 804,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 806,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 797,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 793,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 775,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleEditStudent(student),\n                                                                disabled: loading,\n                                                                className: \"h-8 w-8 p-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 821,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 814,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"destructive\",\n                                                                onClick: ()=>handleDeleteStudent(student),\n                                                                disabled: loading,\n                                                                className: \"h-8 w-8 p-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 830,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 823,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 813,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 773,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, student.id, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 756,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 754,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 746,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 739,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: [\n                                    \"Admin Instructions - \",\n                                    storageInfo.mode,\n                                    \" Storage\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 844,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 843,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-blue-700 mb-2\",\n                                                children: \"Required Fields:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 849,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Student Name (Full name required)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 851,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Phone Number (10 digits, unique)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 852,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Class Selection (from dropdown)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 853,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Student Photo (Upload or camera)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 854,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCDD Email (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 855,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCDD Department (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 856,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCDD Schedule (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 857,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 850,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 848,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-green-700 mb-2\",\n                                                children: \"Photo Requirements:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 861,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Clear face photo required\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 863,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 JPG, PNG, GIF formats supported\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 864,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Maximum file size: 5MB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 865,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Upload from device or take with camera\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 866,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Used for face verification at station\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 867,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Can be changed during editing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 868,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 862,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 860,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 847,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 846,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 842,\n                    columnNumber: 9\n                }, this),\n                showEntriesModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: \"Entry/Exit History\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 880,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: ()=>setShowEntriesModal(false),\n                                        variant: \"outline\",\n                                        children: \"✕ Close\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 881,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 879,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-green-600 mb-2\",\n                                                    children: [\n                                                        \"Today's Entries (\",\n                                                        todayEntries.length,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 889,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 max-h-60 overflow-y-auto\",\n                                                    children: todayEntries.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 text-sm\",\n                                                        children: \"No entries today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 892,\n                                                        columnNumber: 25\n                                                    }, this) : todayEntries.map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border border-green-200 rounded p-3 bg-green-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-medium\",\n                                                                                children: entry.student_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 898,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"App: \",\n                                                                                    entry.application_number\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 899,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    entry.status === 'entry' ? '🟢 Entry' : '🔴 Exit',\n                                                                                    \" - \",\n                                                                                    formatDateTime(entry.entry_time)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 900,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            entry.exit_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    \"Exit: \",\n                                                                                    formatDateTime(entry.exit_time)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 904,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 897,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs bg-green-100 text-green-800 px-2 py-1 rounded\",\n                                                                        children: entry.verified ? '✅ Verified' : '⚠️ Pending'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 907,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 896,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, entry.id, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 895,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 890,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 888,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-blue-600 mb-2\",\n                                                    children: [\n                                                        \"All Entries (\",\n                                                        allEntries.length,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 918,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 max-h-60 overflow-y-auto\",\n                                                    children: [\n                                                        allEntries.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 text-sm\",\n                                                            children: \"No entries found\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 921,\n                                                            columnNumber: 25\n                                                        }, this) : allEntries.slice(0, 10).map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"border border-blue-200 rounded p-3 bg-blue-50\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-start\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: entry.student_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                                    lineNumber: 927,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: [\n                                                                                        \"App: \",\n                                                                                        entry.application_number\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                                    lineNumber: 928,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: [\n                                                                                        entry.status === 'entry' ? '🟢 Entry' : '🔴 Exit',\n                                                                                        \" - \",\n                                                                                        formatDateTime(entry.entry_time)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                                    lineNumber: 929,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                entry.exit_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: [\n                                                                                        \"Exit: \",\n                                                                                        formatDateTime(entry.exit_time)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                                    lineNumber: 933,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 926,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\",\n                                                                            children: entry.verified ? '✅ Verified' : '⚠️ Pending'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 936,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 925,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, entry.id, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 924,\n                                                                columnNumber: 27\n                                                            }, this)),\n                                                        allEntries.length > 10 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 text-center\",\n                                                            children: [\n                                                                \"... and \",\n                                                                allEntries.length - 10,\n                                                                \" more entries\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 944,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 919,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 917,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 887,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 886,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 878,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 877,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 444,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 443,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPanel, \"K+NNj2fE6sZnkT/+Dtmevz37Jxg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AdminPanel;\nvar _c;\n$RefreshReg$(_c, \"AdminPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/page.tsx\n"));

/***/ })

});