"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./app/admin/page.tsx":
/*!****************************!*\
  !*** ./app/admin/page.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _lib_database_store__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/database-store */ \"(app-pages-browser)/./lib/database-store.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AdminPanel() {\n    _s();\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingStudent, setEditingStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [copiedText, setCopiedText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastUpdated, setLastUpdated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dataSource, setDataSource] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"local\");\n    const [databaseConnected, setDatabaseConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [storageInfo, setStorageInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        mode: \"Local\",\n        studentsCount: 0,\n        entriesCount: 0\n    });\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalStudents: 0,\n        todayEntries: 0,\n        todayExits: 0,\n        totalEntries: 0\n    });\n    const [newStudent, setNewStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        phone: \"\",\n        email: \"\",\n        class: \"\",\n        department: \"\",\n        schedule: \"\",\n        image: \"\"\n    });\n    const [imagePreview, setImagePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageFile, setImageFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPanel.useEffect\": ()=>{\n            // Check if admin is logged in\n            if (true) {\n                const adminLoggedIn = localStorage.getItem(\"adminLoggedIn\");\n                if (!adminLoggedIn) {\n                    router.push(\"/\");\n                    return;\n                }\n            }\n            setIsAuthenticated(true);\n            checkDatabaseConnection();\n            loadData();\n        }\n    }[\"AdminPanel.useEffect\"], [\n        router\n    ]);\n    // Separate function to refresh only stats (not full page)\n    const refreshStats = async ()=>{\n        try {\n            console.log(\"🔄 Refreshing stats from shared MongoDB database...\");\n            let allEntries = [];\n            let todayEntries = [];\n            let studentsData = [];\n            let dataSource = \"mongodb\";\n            try {\n                // Use local API which connects to shared MongoDB\n                console.log(\"🔍 Fetching from shared MongoDB via local API...\");\n                const [localStudentsRes, localEntriesRes] = await Promise.all([\n                    fetch('/api/students'),\n                    fetch('/api/entries')\n                ]);\n                if (localStudentsRes.ok && localEntriesRes.ok) {\n                    studentsData = await localStudentsRes.json();\n                    allEntries = await localEntriesRes.json();\n                    dataSource = \"mongodb\";\n                    console.log(\"✅ Data fetched from shared MongoDB database\");\n                } else {\n                    throw new Error(\"MongoDB API not available\");\n                }\n            } catch (apiError) {\n                console.log(\"⚠️ API not available, using database store fallback...\");\n                // Fallback to database store\n                allEntries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getAllEntries();\n                todayEntries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getTodayEntries();\n                studentsData = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getStudents();\n                dataSource = \"fallback\";\n            }\n            // Filter today's entries if we got data from API\n            if (dataSource !== \"fallback\") {\n                const today = new Date().toDateString();\n                todayEntries = allEntries.filter((entry)=>{\n                    const entryDate = new Date(entry.entryTime || entry.entry_time).toDateString();\n                    return entryDate === today;\n                });\n            }\n            console.log(\"📊 Raw data:\", {\n                source: dataSource,\n                allEntries: allEntries.length,\n                todayEntries: todayEntries.length,\n                todayEntriesData: todayEntries,\n                students: studentsData.length\n            });\n            // Debug: Show sample entry data\n            if (allEntries.length > 0) {\n                console.log(\"📝 Sample entry:\", allEntries[0]);\n            }\n            if (todayEntries.length > 0) {\n                console.log(\"📅 Sample today entry:\", todayEntries[0]);\n            }\n            const entryCount = todayEntries.filter((e)=>e.status === 'entry').length;\n            const exitCount = todayEntries.filter((e)=>e.status === 'exit').length;\n            setStats({\n                totalStudents: studentsData.length,\n                todayEntries: entryCount,\n                todayExits: exitCount,\n                totalEntries: allEntries.length\n            });\n            setDataSource(dataSource);\n            setLastUpdated(new Date());\n            console.log(\"✅ Stats refreshed:\", {\n                source: dataSource,\n                totalStudents: studentsData.length,\n                todayEntries: entryCount,\n                todayExits: exitCount,\n                totalActivity: entryCount + exitCount,\n                allEntries: allEntries.length\n            });\n        } catch (error) {\n            console.error(\"❌ Error refreshing stats:\", error);\n        }\n    };\n    // Auto-reload only stats every 5 seconds (not full page)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPanel.useEffect\": ()=>{\n            if (!isAuthenticated) return;\n            const interval = setInterval({\n                \"AdminPanel.useEffect.interval\": ()=>{\n                    refreshStats() // Only refresh stats, not full page\n                    ;\n                }\n            }[\"AdminPanel.useEffect.interval\"], 5000) // 5 seconds\n            ;\n            return ({\n                \"AdminPanel.useEffect\": ()=>clearInterval(interval)\n            })[\"AdminPanel.useEffect\"];\n        }\n    }[\"AdminPanel.useEffect\"], [\n        isAuthenticated\n    ]);\n    const checkDatabaseConnection = async ()=>{\n        try {\n            // Check if we can connect to MongoDB via API\n            const studentsRes = await fetch('/api/students');\n            const entriesRes = await fetch('/api/entries');\n            if (studentsRes.ok && entriesRes.ok) {\n                const students = await studentsRes.json();\n                const entries = await entriesRes.json();\n                setDatabaseConnected(true);\n                setStorageInfo({\n                    mode: \"MongoDB Cloud\",\n                    studentsCount: students.length,\n                    entriesCount: entries.length\n                });\n                console.log(\"✅ MongoDB connection verified\");\n            } else {\n                throw new Error(\"API not responding\");\n            }\n        } catch (error) {\n            console.log(\"⚠️ MongoDB not available, using local storage\");\n            setDatabaseConnected(false);\n            const storageInfo = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getStorageInfo();\n            setStorageInfo(storageInfo);\n        }\n    };\n    const loadData = async ()=>{\n        try {\n            setLoading(true);\n            // Load students from local database (for admin management)\n            const studentsData = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getStudents();\n            setStudents(studentsData);\n            // Load stats from cardstation if available\n            await refreshStats();\n            const storageInfo = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getStorageInfo();\n            setStorageInfo(storageInfo);\n        } catch (error) {\n            console.error(\"Error loading data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleRefresh = ()=>{\n        loadData() // Full page refresh\n        ;\n    };\n    const handleStatsRefresh = ()=>{\n        refreshStats() // Only stats refresh\n        ;\n    };\n    const createTestEntry = async ()=>{\n        try {\n            console.log(\"🧪 Creating test entry in shared MongoDB...\");\n            const testEntry = {\n                student_id: \"test_student_1\",\n                application_number: \"APP20254105\",\n                student_name: \"Test Student\",\n                verification_method: \"manual_test\",\n                qr_validated: true,\n                verification_timestamp: new Date().toISOString(),\n                station_id: \"admin_panel\"\n            };\n            // Use local API which connects to shared MongoDB\n            const response = await fetch('/api/entries', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(testEntry)\n            });\n            if (response.ok) {\n                const result = await response.json();\n                console.log(\"✅ Test entry created in shared MongoDB:\", result);\n                alert(\"Test entry created successfully in shared database!\");\n                refreshStats() // Refresh stats to show new entry\n                ;\n            } else {\n                console.error(\"❌ Failed to create test entry:\", response.status);\n                alert(\"Failed to create test entry\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error creating test entry:\", error);\n            alert(\"Error creating test entry\");\n        }\n    };\n    const handleLogout = ()=>{\n        if (true) {\n            localStorage.removeItem(\"adminLoggedIn\");\n            localStorage.removeItem(\"adminUsername\");\n        }\n        router.push(\"/\");\n    };\n    // Handle image file selection\n    const handleImageSelect = (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (!file) return;\n        // Validate file type\n        if (!file.type.startsWith(\"image/\")) {\n            alert(\"Please select a valid image file (JPG, PNG, GIF, etc.)\");\n            return;\n        }\n        // Validate file size (max 5MB)\n        if (file.size > 5 * 1024 * 1024) {\n            alert(\"Image size should be less than 5MB\");\n            return;\n        }\n        setImageFile(file);\n        // Create preview\n        const reader = new FileReader();\n        reader.onload = (e)=>{\n            var _e_target;\n            const result = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n            setImagePreview(result);\n            setNewStudent({\n                ...newStudent,\n                image: result\n            });\n        };\n        reader.readAsDataURL(file);\n    };\n    // Remove selected image\n    const removeImage = ()=>{\n        setImageFile(null);\n        setImagePreview(null);\n        setNewStudent({\n            ...newStudent,\n            image: \"\"\n        });\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    // Take photo using camera\n    const takePhoto = async ()=>{\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({\n                video: true\n            });\n            // Create a video element to capture the stream\n            const video = document.createElement(\"video\");\n            video.srcObject = stream;\n            video.autoplay = true;\n            // Create a modal or popup to show camera feed\n            const modal = document.createElement(\"div\");\n            modal.style.cssText = \"\\n        position: fixed;\\n        top: 0;\\n        left: 0;\\n        width: 100%;\\n        height: 100%;\\n        background: rgba(0,0,0,0.8);\\n        display: flex;\\n        justify-content: center;\\n        align-items: center;\\n        z-index: 1000;\\n      \";\n            const container = document.createElement(\"div\");\n            container.style.cssText = \"\\n        background: white;\\n        padding: 20px;\\n        border-radius: 10px;\\n        text-align: center;\\n      \";\n            const canvas = document.createElement(\"canvas\");\n            const captureBtn = document.createElement(\"button\");\n            captureBtn.textContent = \"Capture Photo\";\n            captureBtn.style.cssText = \"\\n        background: #3b82f6;\\n        color: white;\\n        padding: 10px 20px;\\n        border: none;\\n        border-radius: 5px;\\n        margin: 10px;\\n        cursor: pointer;\\n      \";\n            const cancelBtn = document.createElement(\"button\");\n            cancelBtn.textContent = \"Cancel\";\n            cancelBtn.style.cssText = \"\\n        background: #6b7280;\\n        color: white;\\n        padding: 10px 20px;\\n        border: none;\\n        border-radius: 5px;\\n        margin: 10px;\\n        cursor: pointer;\\n      \";\n            container.appendChild(video);\n            container.appendChild(document.createElement(\"br\"));\n            container.appendChild(captureBtn);\n            container.appendChild(cancelBtn);\n            modal.appendChild(container);\n            document.body.appendChild(modal);\n            // Capture photo\n            captureBtn.onclick = ()=>{\n                canvas.width = video.videoWidth;\n                canvas.height = video.videoHeight;\n                const ctx = canvas.getContext(\"2d\");\n                ctx === null || ctx === void 0 ? void 0 : ctx.drawImage(video, 0, 0);\n                const imageData = canvas.toDataURL(\"image/jpeg\", 0.8);\n                setImagePreview(imageData);\n                setNewStudent({\n                    ...newStudent,\n                    image: imageData\n                });\n                // Stop camera and close modal\n                stream.getTracks().forEach((track)=>track.stop());\n                document.body.removeChild(modal);\n            };\n            // Cancel\n            cancelBtn.onclick = ()=>{\n                stream.getTracks().forEach((track)=>track.stop());\n                document.body.removeChild(modal);\n            };\n        } catch (error) {\n            alert(\"Camera access denied or not available\");\n        }\n    };\n    const validateForm = ()=>{\n        if (!newStudent.name.trim()) {\n            alert(\"Student name is required\");\n            return false;\n        }\n        if (!newStudent.phone.trim()) {\n            alert(\"Phone number is required\");\n            return false;\n        }\n        if (newStudent.phone.length !== 10 || !/^\\d+$/.test(newStudent.phone)) {\n            alert(\"Phone number must be exactly 10 digits\");\n            return false;\n        }\n        if (!newStudent.class) {\n            alert(\"Class selection is required\");\n            return false;\n        }\n        if (!newStudent.image) {\n            alert(\"Student photo is required. Please upload an image or take a photo.\");\n            return false;\n        }\n        return true;\n    };\n    const handleAddStudent = async ()=>{\n        if (!validateForm()) return;\n        // Check if phone number already exists\n        const existingStudent = students.find((s)=>s.phone === newStudent.phone);\n        if (existingStudent) {\n            alert(\"Phone number already exists!\");\n            return;\n        }\n        setLoading(true);\n        try {\n            const applicationNumber = _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.generateApplicationNumber();\n            const student = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.addStudent({\n                ...newStudent,\n                application_number: applicationNumber,\n                image_url: newStudent.image\n            });\n            await loadData();\n            resetForm();\n            alert(\"Student Added Successfully!\\n\\nName: \".concat(student.name, \"\\nApplication Number: \").concat(applicationNumber, \"\\nPhone: \").concat(student.phone, \"\\n\\nPlease provide Application Number and Phone Number to the student for login.\\n\\nData saved in \").concat(storageInfo.mode, \" storage.\"));\n        } catch (error) {\n            alert(\"Error adding student. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleEditStudent = (student)=>{\n        setEditingStudent(student);\n        setNewStudent({\n            name: student.name,\n            phone: student.phone,\n            email: student.email || \"\",\n            class: student.class,\n            department: student.department || \"\",\n            schedule: student.schedule || \"\",\n            image: student.image_url || \"\"\n        });\n        setImagePreview(student.image_url || null);\n        setShowAddForm(false);\n    };\n    const handleUpdateStudent = async ()=>{\n        if (!validateForm() || !editingStudent) return;\n        // Check if phone number already exists (excluding current student)\n        const existingStudent = students.find((s)=>s.phone === newStudent.phone && s.id !== editingStudent.id);\n        if (existingStudent) {\n            alert(\"Phone number already exists!\");\n            return;\n        }\n        setLoading(true);\n        try {\n            await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.updateStudent(editingStudent.id, {\n                name: newStudent.name,\n                phone: newStudent.phone,\n                email: newStudent.email || null,\n                class: newStudent.class,\n                department: newStudent.department || null,\n                schedule: newStudent.schedule || null,\n                image_url: newStudent.image\n            });\n            await loadData();\n            resetForm();\n            alert(\"Student updated successfully!\\n\\nData saved in \".concat(storageInfo.mode, \" storage.\"));\n        } catch (error) {\n            alert(\"Error updating student. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDeleteStudent = async (student)=>{\n        if (confirm(\"Are you sure you want to delete \".concat(student.name, \"?\\n\\nThis action cannot be undone.\"))) {\n            try {\n                setLoading(true);\n                await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.deleteStudent(student.id);\n                await loadData();\n                alert(\"Student deleted successfully!\\n\\nData updated in \".concat(storageInfo.mode, \" storage.\"));\n            } catch (error) {\n                alert(\"Error deleting student. Please try again.\");\n            } finally{\n                setLoading(false);\n            }\n        }\n    };\n    const copyToClipboard = async (text, type)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n            setCopiedText(\"\".concat(type, \"-\").concat(text));\n            setTimeout(()=>setCopiedText(null), 2000);\n        } catch (error) {\n            alert(\"Failed to copy to clipboard\");\n        }\n    };\n    const resetForm = ()=>{\n        setNewStudent({\n            name: \"\",\n            phone: \"\",\n            email: \"\",\n            class: \"\",\n            department: \"\",\n            schedule: \"\",\n            image: \"\"\n        });\n        setImagePreview(null);\n        setImageFile(null);\n        setShowAddForm(false);\n        setEditingStudent(null);\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    const today = new Date().toISOString().slice(0, 10);\n    const totalStudents = students.length;\n    // Replace the following with your actual attendance/logs array if available\n    // For demonstration, using an empty array as placeholder\n    const logs = [] // Replace with actual logs source\n    ;\n    const todaysEntries = logs.filter((e)=>e.type === \"entry\" && e.timestamp.slice(0, 10) === today).length;\n    const todaysExits = logs.filter((e)=>e.type === \"exit\" && e.timestamp.slice(0, 10) === today).length;\n    const totalEntries = logs.filter((e)=>e.type === \"entry\").length;\n    const remainingStudents = totalStudents - todaysExits;\n    if (!isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 572,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-3xl\",\n                                            children: \"Admin Panel\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            className: \"text-lg\",\n                                            children: [\n                                                \"Student Management System - \",\n                                                storageInfo.mode,\n                                                \" Storage\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 584,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 582,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleRefresh,\n                                            variant: \"outline\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 590,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Refresh\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 589,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleLogout,\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Logout\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 588,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 581,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 580,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 579,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.Alert, {\n                    className: databaseConnected ? \"border-green-200 bg-green-50\" : \"border-yellow-200 bg-yellow-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"h-4 w-4 \".concat(databaseConnected ? \"text-green-600\" : \"text-yellow-600\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 604,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.AlertDescription, {\n                            className: databaseConnected ? \"text-green-800\" : \"text-yellow-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: [\n                                        storageInfo.mode,\n                                        \" Storage Active:\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 606,\n                                    columnNumber: 13\n                                }, this),\n                                \" \",\n                                databaseConnected ? \"Data syncs across all devices automatically\" : \"Data saved locally on this device (\".concat(storageInfo.studentsCount, \" students, \").concat(storageInfo.entriesCount, \" entries)\")\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 605,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 603,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"bg-blue-50 border-blue-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 sm:p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl sm:text-4xl font-bold text-blue-600 mb-2\",\n                                        children: stats.totalStudents\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm sm:text-base font-medium text-blue-700\",\n                                        children: \"Total Students\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 621,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-500 mt-1\",\n                                        children: \"Registered\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 624,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 616,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"bg-green-50 border-green-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 sm:p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl sm:text-4xl font-bold text-green-600 mb-2\",\n                                        children: stats.todayEntries\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm sm:text-base font-medium text-green-700\",\n                                        children: \"Total Entries\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-green-500 mt-1\",\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 639,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 632,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 631,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"bg-red-50 border-red-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 sm:p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl sm:text-4xl font-bold text-red-600 mb-2\",\n                                        children: stats.todayExits\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm sm:text-base font-medium text-red-700\",\n                                        children: \"Total Exits\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 651,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-red-500 mt-1\",\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 654,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 647,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 646,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"bg-purple-50 border-purple-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 sm:p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl sm:text-4xl font-bold text-purple-600 mb-2\",\n                                        children: stats.todayEntries + stats.todayExits\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm sm:text-base font-medium text-purple-700\",\n                                        children: \"Total Activity\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 666,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-purple-500 mt-1\",\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 669,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 662,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 661,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 614,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center gap-2 text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 679,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Auto-refreshing every 5 seconds\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 680,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs px-2 py-1 rounded \".concat(dataSource === 'mongodb' ? 'bg-green-100 text-green-700' : 'bg-yellow-100 text-yellow-700'),\n                                    children: dataSource === 'mongodb' ? '�️ Shared MongoDB' : '💾 Local Fallback'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 681,\n                                    columnNumber: 13\n                                }, this),\n                                lastUpdated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: [\n                                        \"• \",\n                                        lastUpdated.toLocaleTimeString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 689,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 678,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: handleStatsRefresh,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"text-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"mr-1 h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 701,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Refresh Stats\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 695,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: createTestEntry,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"text-xs bg-green-50 hover:bg-green-100\",\n                                    children: \"\\uD83E\\uDDEA Test Entry\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 704,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 694,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 677,\n                    columnNumber: 9\n                }, this),\n                !showAddForm && !editingStudent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>setShowAddForm(true),\n                            className: \"w-full h-16 text-lg\",\n                            disabled: loading,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"mr-2 h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 720,\n                                    columnNumber: 17\n                                }, this),\n                                \"Add New Student\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 719,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 718,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 717,\n                    columnNumber: 11\n                }, this),\n                (showAddForm || editingStudent) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: editingStudent ? \"Edit Student\" : \"Add New Student\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 731,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: [\n                                        editingStudent ? \"Update student information\" : \"Fill required fields to register a new student\",\n                                        \" - Data will be saved in \",\n                                        storageInfo.mode,\n                                        \" storage\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 732,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 730,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            className: \"text-base font-semibold\",\n                                            children: \"Student Photo *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 740,\n                                            columnNumber: 17\n                                        }, this),\n                                        imagePreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: imagePreview || \"/placeholder.svg\",\n                                                            alt: \"Student preview\",\n                                                            className: \"w-32 h-32 rounded-full border-4 border-blue-200 object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 746,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: removeImage,\n                                                            size: \"sm\",\n                                                            variant: \"destructive\",\n                                                            className: \"absolute -top-2 -right-2 h-6 w-6 rounded-full p-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 757,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 751,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 745,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-green-600\",\n                                                            children: \"✅ Photo uploaded successfully\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 761,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: ()=>{\n                                                                var _fileInputRef_current;\n                                                                return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                            },\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 763,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Change Photo\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 762,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 760,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 744,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-16 w-16 mx-auto text-gray-400 mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 770,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4\",\n                                                    children: \"Upload student photo (Required)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 771,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: ()=>{\n                                                                var _fileInputRef_current;\n                                                                return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                            },\n                                                            variant: \"outline\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 774,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Upload Photo\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 773,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: takePhoto,\n                                                            variant: \"outline\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 778,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Take Photo\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 777,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 772,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-2\",\n                                                    children: \"Supported formats: JPG, PNG, GIF (Max 5MB)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 782,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 769,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            ref: fileInputRef,\n                                            type: \"file\",\n                                            accept: \"image/*\",\n                                            onChange: handleImageSelect,\n                                            className: \"hidden\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 787,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 739,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_9__.Separator, {}, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 796,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"name\",\n                                                    children: \"Student Name *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 801,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"name\",\n                                                    value: newStudent.name,\n                                                    onChange: (e)=>setNewStudent({\n                                                            ...newStudent,\n                                                            name: e.target.value\n                                                        }),\n                                                    placeholder: \"Enter full name\",\n                                                    disabled: loading\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 802,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 800,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"phone\",\n                                                    children: \"Phone Number *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 811,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"phone\",\n                                                    value: newStudent.phone,\n                                                    onChange: (e)=>setNewStudent({\n                                                            ...newStudent,\n                                                            phone: e.target.value\n                                                        }),\n                                                    placeholder: \"10-digit phone number\",\n                                                    maxLength: 10,\n                                                    disabled: loading\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 812,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 810,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"email\",\n                                                    children: \"Email Address\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 822,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"email\",\n                                                    type: \"email\",\n                                                    value: newStudent.email,\n                                                    onChange: (e)=>setNewStudent({\n                                                            ...newStudent,\n                                                            email: e.target.value\n                                                        }),\n                                                    placeholder: \"<EMAIL>\",\n                                                    disabled: loading\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 823,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 821,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"class\",\n                                                    children: \"Class *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 833,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: newStudent.class,\n                                                    onValueChange: (value)=>setNewStudent({\n                                                            ...newStudent,\n                                                            class: value\n                                                        }),\n                                                    disabled: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select class\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 840,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 839,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"10th-A\",\n                                                                    children: \"10th A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 843,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"10th-B\",\n                                                                    children: \"10th B\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 844,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"10th-C\",\n                                                                    children: \"10th C\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 845,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"11th-A\",\n                                                                    children: \"11th A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 846,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"11th-B\",\n                                                                    children: \"11th B\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 847,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"11th-C\",\n                                                                    children: \"11th C\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 848,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"12th-A\",\n                                                                    children: \"12th A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 849,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"12th-B\",\n                                                                    children: \"12th B\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 850,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"12th-C\",\n                                                                    children: \"12th C\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 851,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 842,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 834,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 832,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"department\",\n                                                    children: \"Department\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 856,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: newStudent.department,\n                                                    onValueChange: (value)=>setNewStudent({\n                                                            ...newStudent,\n                                                            department: value\n                                                        }),\n                                                    disabled: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select department\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 863,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 862,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Science\",\n                                                                    children: \"Science\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 866,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Commerce\",\n                                                                    children: \"Commerce\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 867,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Arts\",\n                                                                    children: \"Arts\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 868,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Computer Science\",\n                                                                    children: \"Computer Science\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 869,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 865,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 857,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 855,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"schedule\",\n                                                    children: \"Time Schedule\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 874,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: newStudent.schedule,\n                                                    onValueChange: (value)=>setNewStudent({\n                                                            ...newStudent,\n                                                            schedule: value\n                                                        }),\n                                                    disabled: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select schedule\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 881,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 880,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Morning Shift (8:00 AM - 2:00 PM)\",\n                                                                    children: \"Morning Shift (8:00 AM - 2:00 PM)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 884,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Afternoon Shift (2:00 PM - 8:00 PM)\",\n                                                                    children: \"Afternoon Shift (2:00 PM - 8:00 PM)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 887,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Full Day (8:00 AM - 4:00 PM)\",\n                                                                    children: \"Full Day (8:00 AM - 4:00 PM)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 890,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 883,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 875,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 873,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 799,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_9__.Separator, {}, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 896,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        editingStudent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleUpdateStudent,\n                                            className: \"flex-1\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 901,\n                                                    columnNumber: 21\n                                                }, this),\n                                                loading ? \"Updating...\" : \"Update Student\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 900,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleAddStudent,\n                                            className: \"flex-1\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 906,\n                                                    columnNumber: 21\n                                                }, this),\n                                                loading ? \"Adding...\" : \"Add Student\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 905,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: resetForm,\n                                            variant: \"outline\",\n                                            className: \"flex-1 bg-transparent\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 911,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Cancel\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 910,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 898,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 737,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 729,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: [\n                                        \"Registered Students (\",\n                                        students.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 922,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: [\n                                        \"All registered students with their login credentials - Stored in \",\n                                        storageInfo.mode,\n                                        \" storage\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 923,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 921,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: students.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-16 w-16 mx-auto text-gray-300 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 930,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-500 mb-2\",\n                                        children: \"No students registered yet\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 931,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: 'Click \"Add New Student\" to get started'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 932,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 929,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: students.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: student.image_url || \"/placeholder.svg?height=60&width=60\",\n                                                        alt: student.name,\n                                                        className: \"w-12 h-12 rounded-full border-2 border-gray-200 object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 939,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-lg\",\n                                                                children: student.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 945,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: [\n                                                                    student.class,\n                                                                    \" \",\n                                                                    student.department && \"- \".concat(student.department)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 946,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: student.phone\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 949,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            student.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: student.email\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 950,\n                                                                columnNumber: 43\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 944,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 938,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"font-mono text-xs\",\n                                                                        children: [\n                                                                            \"App: \",\n                                                                            student.application_number\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 958,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"ghost\",\n                                                                        onClick: ()=>copyToClipboard(student.application_number, \"app\"),\n                                                                        className: \"h-6 w-6 p-0\",\n                                                                        children: copiedText === \"app-\".concat(student.application_number) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 968,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 970,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 961,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 957,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                        variant: \"secondary\",\n                                                                        className: \"text-xs\",\n                                                                        children: [\n                                                                            \"Phone: \",\n                                                                            student.phone\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 975,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"ghost\",\n                                                                        onClick: ()=>copyToClipboard(student.phone, \"phone\"),\n                                                                        className: \"h-6 w-6 p-0\",\n                                                                        children: copiedText === \"phone-\".concat(student.phone) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 985,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 987,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 978,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 974,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 956,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleEditStudent(student),\n                                                                disabled: loading,\n                                                                className: \"h-8 w-8 p-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 1002,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 995,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"destructive\",\n                                                                onClick: ()=>handleDeleteStudent(student),\n                                                                disabled: loading,\n                                                                className: \"h-8 w-8 p-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 1011,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 1004,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 994,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 954,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, student.id, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 937,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 935,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 927,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 920,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: [\n                                    \"Admin Instructions - \",\n                                    storageInfo.mode,\n                                    \" Storage\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 1025,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 1024,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-blue-700 mb-2\",\n                                                children: \"Required Fields:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 1030,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Student Name (Full name required)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1032,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Phone Number (10 digits, unique)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1033,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Class Selection (from dropdown)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1034,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Student Photo (Upload or camera)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1035,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCDD Email (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1036,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCDD Department (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1037,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCDD Schedule (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1038,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 1031,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 1029,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-green-700 mb-2\",\n                                                children: \"Photo Requirements:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 1042,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Clear face photo required\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1044,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 JPG, PNG, GIF formats supported\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1045,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Maximum file size: 5MB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1046,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Upload from device or take with camera\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1047,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Used for face verification at station\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1048,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Can be changed during editing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1049,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 1043,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 1041,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 1028,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 1027,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 1023,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 577,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 576,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPanel, \"gDv0hcFIWUxGhFUhf6ke2DYOoWQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AdminPanel;\nfunction StatCard(param) {\n    let { icon, value, label, color } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow p-6 flex items-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-\".concat(color, \"-500 text-3xl mr-4\"),\n                children: icon\n            }, void 0, false, {\n                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 1063,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-2xl font-bold\",\n                        children: value\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 1065,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600\",\n                        children: label\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 1066,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 1064,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 1062,\n        columnNumber: 5\n    }, this);\n}\n_c1 = StatCard;\nconst UserIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n        className: \"h-6 w-6 text-blue-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 1072,\n        columnNumber: 24\n    }, undefined);\n_c2 = UserIcon;\nconst EntryIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n        className: \"h-6 w-6 text-green-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 1073,\n        columnNumber: 25\n    }, undefined);\n_c3 = EntryIcon;\nconst ExitIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n        className: \"h-6 w-6 text-red-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 1074,\n        columnNumber: 24\n    }, undefined);\n_c4 = ExitIcon;\nconst TotalIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n        className: \"h-6 w-6 text-purple-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 1075,\n        columnNumber: 25\n    }, undefined);\n_c5 = TotalIcon;\nconst RemainIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n        className: \"h-6 w-6 text-orange-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 1076,\n        columnNumber: 26\n    }, undefined);\n_c6 = RemainIcon;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"AdminPanel\");\n$RefreshReg$(_c1, \"StatCard\");\n$RefreshReg$(_c2, \"UserIcon\");\n$RefreshReg$(_c3, \"EntryIcon\");\n$RefreshReg$(_c4, \"ExitIcon\");\n$RefreshReg$(_c5, \"TotalIcon\");\n$RefreshReg$(_c6, \"RemainIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/page.tsx\n"));

/***/ })

});