/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/admin/page";
exports.ids = ["app/admin/page"];
exports.modules = {

/***/ "(rsc)/./app/admin/page.tsx":
/*!****************************!*\
  !*** ./app/admin/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\idcard\\smartidcard\\app\\admin\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4c5ceb8252e9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkQ6XFxpZGNhcmRcXHNtYXJ0aWRjYXJkXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNGM1Y2ViODI1MmU5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: 'v0 App',\n    description: 'Created with v0',\n    generator: 'v0.dev'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDc0I7QUFFZixNQUFNQSxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFdBQVc7QUFDYixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdSO0lBQ0EscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO3NCQUFNSDs7Ozs7Ozs7Ozs7QUFHYiIsInNvdXJjZXMiOlsiRDpcXGlkY2FyZFxcc21hcnRpZGNhcmRcXGFwcFxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICd2MCBBcHAnLFxuICBkZXNjcmlwdGlvbjogJ0NyZWF0ZWQgd2l0aCB2MCcsXG4gIGdlbmVyYXRvcjogJ3YwLmRldicsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5PntjaGlsZHJlbn08L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiZ2VuZXJhdG9yIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=D%3A%5Cidcard%5Csmartidcard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Csmartidcard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=D%3A%5Cidcard%5Csmartidcard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Csmartidcard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/admin/page.tsx */ \"(rsc)/./app/admin/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'admin',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\idcard\\\\smartidcard\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/admin/page\",\n        pathname: \"/admin\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=D%3A%5Cidcard%5Csmartidcard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Csmartidcard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Capp%5C%5Cadmin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Capp%5C%5Cadmin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/admin/page.tsx */ \"(rsc)/./app/admin/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNpZGNhcmQlNUMlNUNzbWFydGlkY2FyZCU1QyU1Q2FwcCU1QyU1Q2FkbWluJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9KQUFrRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcaWRjYXJkXFxcXHNtYXJ0aWRjYXJkXFxcXGFwcFxcXFxhZG1pblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Capp%5C%5Cadmin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/admin/page.tsx":
/*!****************************!*\
  !*** ./app/admin/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(ssr)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/separator */ \"(ssr)/./components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/alert */ \"(ssr)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,Home,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _lib_database_store__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/database-store */ \"(ssr)/./lib/database-store.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./lib/supabase.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_13__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AdminPanel() {\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingStudent, setEditingStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [copiedText, setCopiedText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastUpdated, setLastUpdated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [databaseConnected, setDatabaseConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [storageInfo, setStorageInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        mode: \"Local\",\n        studentsCount: 0,\n        entriesCount: 0\n    });\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalStudents: 0,\n        todayEntries: 0,\n        todayExits: 0,\n        totalEntries: 0\n    });\n    const [newStudent, setNewStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        phone: \"\",\n        email: \"\",\n        class: \"\",\n        department: \"\",\n        schedule: \"\",\n        image: \"\"\n    });\n    const [imagePreview, setImagePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageFile, setImageFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPanel.useEffect\": ()=>{\n            // Check if admin is logged in\n            if (false) {}\n            setIsAuthenticated(true);\n            checkDatabaseConnection();\n            loadData();\n        }\n    }[\"AdminPanel.useEffect\"], [\n        router\n    ]);\n    // Auto-reload data every 5 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPanel.useEffect\": ()=>{\n            if (!isAuthenticated) return;\n            const interval = setInterval({\n                \"AdminPanel.useEffect.interval\": ()=>{\n                    console.log(\"🔄 Auto-refreshing admin data...\");\n                    loadData();\n                }\n            }[\"AdminPanel.useEffect.interval\"], 5000) // 5 seconds\n            ;\n            return ({\n                \"AdminPanel.useEffect\": ()=>clearInterval(interval)\n            })[\"AdminPanel.useEffect\"];\n        }\n    }[\"AdminPanel.useEffect\"], [\n        isAuthenticated\n    ]);\n    const checkDatabaseConnection = async ()=>{\n        const connected = _lib_supabase__WEBPACK_IMPORTED_MODULE_12__.supabase !== null;\n        setDatabaseConnected(connected);\n        const storageInfo = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getStorageInfo();\n        setStorageInfo(storageInfo);\n    };\n    const loadData = async ()=>{\n        try {\n            setLoading(true);\n            const studentsData = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getStudents();\n            const allEntries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getAllEntries();\n            const todayEntries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getTodayEntries();\n            setStudents(studentsData);\n            setStats({\n                totalStudents: studentsData.length,\n                todayEntries: todayEntries.filter((e)=>e.status === 'entry').length,\n                todayExits: todayEntries.filter((e)=>e.status === 'exit').length,\n                totalEntries: allEntries.length\n            });\n            const storageInfo = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getStorageInfo();\n            setStorageInfo(storageInfo);\n            setLastUpdated(new Date());\n        } catch (error) {\n            console.error(\"Error loading data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleRefresh = ()=>{\n        loadData();\n    };\n    const handleLogout = ()=>{\n        if (false) {}\n        router.push(\"/\");\n    };\n    // Handle image file selection\n    const handleImageSelect = (event)=>{\n        const file = event.target.files?.[0];\n        if (!file) return;\n        // Validate file type\n        if (!file.type.startsWith(\"image/\")) {\n            alert(\"Please select a valid image file (JPG, PNG, GIF, etc.)\");\n            return;\n        }\n        // Validate file size (max 5MB)\n        if (file.size > 5 * 1024 * 1024) {\n            alert(\"Image size should be less than 5MB\");\n            return;\n        }\n        setImageFile(file);\n        // Create preview\n        const reader = new FileReader();\n        reader.onload = (e)=>{\n            const result = e.target?.result;\n            setImagePreview(result);\n            setNewStudent({\n                ...newStudent,\n                image: result\n            });\n        };\n        reader.readAsDataURL(file);\n    };\n    // Remove selected image\n    const removeImage = ()=>{\n        setImageFile(null);\n        setImagePreview(null);\n        setNewStudent({\n            ...newStudent,\n            image: \"\"\n        });\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    // Take photo using camera\n    const takePhoto = async ()=>{\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({\n                video: true\n            });\n            // Create a video element to capture the stream\n            const video = document.createElement(\"video\");\n            video.srcObject = stream;\n            video.autoplay = true;\n            // Create a modal or popup to show camera feed\n            const modal = document.createElement(\"div\");\n            modal.style.cssText = `\n        position: fixed;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        background: rgba(0,0,0,0.8);\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        z-index: 1000;\n      `;\n            const container = document.createElement(\"div\");\n            container.style.cssText = `\n        background: white;\n        padding: 20px;\n        border-radius: 10px;\n        text-align: center;\n      `;\n            const canvas = document.createElement(\"canvas\");\n            const captureBtn = document.createElement(\"button\");\n            captureBtn.textContent = \"Capture Photo\";\n            captureBtn.style.cssText = `\n        background: #3b82f6;\n        color: white;\n        padding: 10px 20px;\n        border: none;\n        border-radius: 5px;\n        margin: 10px;\n        cursor: pointer;\n      `;\n            const cancelBtn = document.createElement(\"button\");\n            cancelBtn.textContent = \"Cancel\";\n            cancelBtn.style.cssText = `\n        background: #6b7280;\n        color: white;\n        padding: 10px 20px;\n        border: none;\n        border-radius: 5px;\n        margin: 10px;\n        cursor: pointer;\n      `;\n            container.appendChild(video);\n            container.appendChild(document.createElement(\"br\"));\n            container.appendChild(captureBtn);\n            container.appendChild(cancelBtn);\n            modal.appendChild(container);\n            document.body.appendChild(modal);\n            // Capture photo\n            captureBtn.onclick = ()=>{\n                canvas.width = video.videoWidth;\n                canvas.height = video.videoHeight;\n                const ctx = canvas.getContext(\"2d\");\n                ctx?.drawImage(video, 0, 0);\n                const imageData = canvas.toDataURL(\"image/jpeg\", 0.8);\n                setImagePreview(imageData);\n                setNewStudent({\n                    ...newStudent,\n                    image: imageData\n                });\n                // Stop camera and close modal\n                stream.getTracks().forEach((track)=>track.stop());\n                document.body.removeChild(modal);\n            };\n            // Cancel\n            cancelBtn.onclick = ()=>{\n                stream.getTracks().forEach((track)=>track.stop());\n                document.body.removeChild(modal);\n            };\n        } catch (error) {\n            alert(\"Camera access denied or not available\");\n        }\n    };\n    const validateForm = ()=>{\n        if (!newStudent.name.trim()) {\n            alert(\"Student name is required\");\n            return false;\n        }\n        if (!newStudent.phone.trim()) {\n            alert(\"Phone number is required\");\n            return false;\n        }\n        if (newStudent.phone.length !== 10 || !/^\\d+$/.test(newStudent.phone)) {\n            alert(\"Phone number must be exactly 10 digits\");\n            return false;\n        }\n        if (!newStudent.class) {\n            alert(\"Class selection is required\");\n            return false;\n        }\n        if (!newStudent.image) {\n            alert(\"Student photo is required. Please upload an image or take a photo.\");\n            return false;\n        }\n        return true;\n    };\n    const handleAddStudent = async ()=>{\n        if (!validateForm()) return;\n        // Check if phone number already exists\n        const existingStudent = students.find((s)=>s.phone === newStudent.phone);\n        if (existingStudent) {\n            alert(\"Phone number already exists!\");\n            return;\n        }\n        setLoading(true);\n        try {\n            const applicationNumber = _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.generateApplicationNumber();\n            const student = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.addStudent({\n                ...newStudent,\n                application_number: applicationNumber,\n                image_url: newStudent.image\n            });\n            await loadData();\n            resetForm();\n            alert(`Student Added Successfully!\\n\\nName: ${student.name}\\nApplication Number: ${applicationNumber}\\nPhone: ${student.phone}\\n\\nPlease provide Application Number and Phone Number to the student for login.\\n\\nData saved in ${storageInfo.mode} storage.`);\n        } catch (error) {\n            alert(\"Error adding student. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleEditStudent = (student)=>{\n        setEditingStudent(student);\n        setNewStudent({\n            name: student.name,\n            phone: student.phone,\n            email: student.email || \"\",\n            class: student.class,\n            department: student.department || \"\",\n            schedule: student.schedule || \"\",\n            image: student.image_url || \"\"\n        });\n        setImagePreview(student.image_url || null);\n        setShowAddForm(false);\n    };\n    const handleUpdateStudent = async ()=>{\n        if (!validateForm() || !editingStudent) return;\n        // Check if phone number already exists (excluding current student)\n        const existingStudent = students.find((s)=>s.phone === newStudent.phone && s.id !== editingStudent.id);\n        if (existingStudent) {\n            alert(\"Phone number already exists!\");\n            return;\n        }\n        setLoading(true);\n        try {\n            await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.updateStudent(editingStudent.id, {\n                name: newStudent.name,\n                phone: newStudent.phone,\n                email: newStudent.email || null,\n                class: newStudent.class,\n                department: newStudent.department || null,\n                schedule: newStudent.schedule || null,\n                image_url: newStudent.image\n            });\n            await loadData();\n            resetForm();\n            alert(`Student updated successfully!\\n\\nData saved in ${storageInfo.mode} storage.`);\n        } catch (error) {\n            alert(\"Error updating student. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDeleteStudent = async (student)=>{\n        if (confirm(`Are you sure you want to delete ${student.name}?\\n\\nThis action cannot be undone.`)) {\n            try {\n                setLoading(true);\n                await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.deleteStudent(student.id);\n                await loadData();\n                alert(`Student deleted successfully!\\n\\nData updated in ${storageInfo.mode} storage.`);\n            } catch (error) {\n                alert(\"Error deleting student. Please try again.\");\n            } finally{\n                setLoading(false);\n            }\n        }\n    };\n    const copyToClipboard = async (text, type)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n            setCopiedText(`${type}-${text}`);\n            setTimeout(()=>setCopiedText(null), 2000);\n        } catch (error) {\n            alert(\"Failed to copy to clipboard\");\n        }\n    };\n    const resetForm = ()=>{\n        setNewStudent({\n            name: \"\",\n            phone: \"\",\n            email: \"\",\n            class: \"\",\n            department: \"\",\n            schedule: \"\",\n            image: \"\"\n        });\n        setImagePreview(null);\n        setImageFile(null);\n        setShowAddForm(false);\n        setEditingStudent(null);\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    const today = new Date().toISOString().slice(0, 10);\n    const totalStudents = students.length;\n    // Replace the following with your actual attendance/logs array if available\n    // For demonstration, using an empty array as placeholder\n    const logs = [] // Replace with actual logs source\n    ;\n    const todaysEntries = logs.filter((e)=>e.type === \"entry\" && e.timestamp.slice(0, 10) === today).length;\n    const todaysExits = logs.filter((e)=>e.type === \"exit\" && e.timestamp.slice(0, 10) === today).length;\n    const totalEntries = logs.filter((e)=>e.type === \"entry\").length;\n    const remainingStudents = totalStudents - todaysExits;\n    if (!isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 433,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-3xl\",\n                                            children: \"Admin Panel\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            className: \"text-lg\",\n                                            children: [\n                                                \"Student Management System - \",\n                                                storageInfo.mode,\n                                                \" Storage\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleRefresh,\n                                            variant: \"outline\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: `mr-2 h-4 w-4 ${loading ? \"animate-spin\" : \"\"}`\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Refresh\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleLogout,\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Logout\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_13___default()), {\n                                            href: \"/\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                size: \"lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Home\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 442,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 441,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 440,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.Alert, {\n                    className: databaseConnected ? \"border-green-200 bg-green-50\" : \"border-yellow-200 bg-yellow-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: `h-4 w-4 ${databaseConnected ? \"text-green-600\" : \"text-yellow-600\"}`\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.AlertDescription, {\n                            className: databaseConnected ? \"text-green-800\" : \"text-yellow-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: [\n                                        storageInfo.mode,\n                                        \" Storage Active:\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 13\n                                }, this),\n                                \" \",\n                                databaseConnected ? \"Data syncs across all devices automatically\" : `Data saved locally on this device (${storageInfo.studentsCount} students, ${storageInfo.entriesCount} entries)`\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 470,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"bg-blue-50 border-blue-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 sm:p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl sm:text-4xl font-bold text-blue-600 mb-2\",\n                                        children: stats.totalStudents\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm sm:text-base font-medium text-blue-700\",\n                                        children: \"Total Students\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-500 mt-1\",\n                                        children: \"Registered\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 484,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 483,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"bg-green-50 border-green-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 sm:p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl sm:text-4xl font-bold text-green-600 mb-2\",\n                                        children: stats.todayEntries\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm sm:text-base font-medium text-green-700\",\n                                        children: \"Total Entries\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-green-500 mt-1\",\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 506,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 498,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"bg-red-50 border-red-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 sm:p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl sm:text-4xl font-bold text-red-600 mb-2\",\n                                        children: stats.todayExits\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm sm:text-base font-medium text-red-700\",\n                                        children: \"Total Exits\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-red-500 mt-1\",\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 513,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"bg-purple-50 border-purple-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 sm:p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl sm:text-4xl font-bold text-purple-600 mb-2\",\n                                        children: stats.todayEntries + stats.todayExits\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 530,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm sm:text-base font-medium text-purple-700\",\n                                        children: \"Total Activity\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 533,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-purple-500 mt-1\",\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 529,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 528,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 481,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center gap-2 text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Auto-refreshing every 5 seconds\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 547,\n                                    columnNumber: 13\n                                }, this),\n                                lastUpdated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: [\n                                        \"• Last updated: \",\n                                        lastUpdated.toLocaleTimeString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 549,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 545,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: handleRefresh,\n                            variant: \"outline\",\n                            size: \"sm\",\n                            disabled: loading,\n                            className: \"text-xs\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: `mr-1 h-3 w-3 ${loading ? \"animate-spin\" : \"\"}`\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 561,\n                                    columnNumber: 13\n                                }, this),\n                                \"Refresh Now\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 554,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 544,\n                    columnNumber: 9\n                }, this),\n                !showAddForm && !editingStudent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>setShowAddForm(true),\n                            className: \"w-full h-16 text-lg\",\n                            disabled: loading,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"mr-2 h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 17\n                                }, this),\n                                \"Add New Student\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 570,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 569,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 568,\n                    columnNumber: 11\n                }, this),\n                (showAddForm || editingStudent) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: editingStudent ? \"Edit Student\" : \"Add New Student\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 582,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: [\n                                        editingStudent ? \"Update student information\" : \"Fill required fields to register a new student\",\n                                        \" - Data will be saved in \",\n                                        storageInfo.mode,\n                                        \" storage\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 583,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 581,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            className: \"text-base font-semibold\",\n                                            children: \"Student Photo *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 591,\n                                            columnNumber: 17\n                                        }, this),\n                                        imagePreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: imagePreview || \"/placeholder.svg\",\n                                                            alt: \"Student preview\",\n                                                            className: \"w-32 h-32 rounded-full border-4 border-blue-200 object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: removeImage,\n                                                            size: \"sm\",\n                                                            variant: \"destructive\",\n                                                            className: \"absolute -top-2 -right-2 h-6 w-6 rounded-full p-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 608,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 596,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-green-600\",\n                                                            children: \"✅ Photo uploaded successfully\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 612,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: ()=>fileInputRef.current?.click(),\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 614,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Change Photo\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 613,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 611,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-16 w-16 mx-auto text-gray-400 mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 621,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4\",\n                                                    children: \"Upload student photo (Required)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 622,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: ()=>fileInputRef.current?.click(),\n                                                            variant: \"outline\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 625,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Upload Photo\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 624,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: takePhoto,\n                                                            variant: \"outline\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 629,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Take Photo\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 628,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 623,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-2\",\n                                                    children: \"Supported formats: JPG, PNG, GIF (Max 5MB)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 633,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 620,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            ref: fileInputRef,\n                                            type: \"file\",\n                                            accept: \"image/*\",\n                                            onChange: handleImageSelect,\n                                            className: \"hidden\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 638,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_9__.Separator, {}, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 647,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"name\",\n                                                    children: \"Student Name *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 652,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"name\",\n                                                    value: newStudent.name,\n                                                    onChange: (e)=>setNewStudent({\n                                                            ...newStudent,\n                                                            name: e.target.value\n                                                        }),\n                                                    placeholder: \"Enter full name\",\n                                                    disabled: loading\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 651,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"phone\",\n                                                    children: \"Phone Number *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"phone\",\n                                                    value: newStudent.phone,\n                                                    onChange: (e)=>setNewStudent({\n                                                            ...newStudent,\n                                                            phone: e.target.value\n                                                        }),\n                                                    placeholder: \"10-digit phone number\",\n                                                    maxLength: 10,\n                                                    disabled: loading\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 661,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"email\",\n                                                    children: \"Email Address\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 673,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"email\",\n                                                    type: \"email\",\n                                                    value: newStudent.email,\n                                                    onChange: (e)=>setNewStudent({\n                                                            ...newStudent,\n                                                            email: e.target.value\n                                                        }),\n                                                    placeholder: \"<EMAIL>\",\n                                                    disabled: loading\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 674,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 672,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"class\",\n                                                    children: \"Class *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 684,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: newStudent.class,\n                                                    onValueChange: (value)=>setNewStudent({\n                                                            ...newStudent,\n                                                            class: value\n                                                        }),\n                                                    disabled: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select class\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 691,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 690,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"10th-A\",\n                                                                    children: \"10th A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 694,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"10th-B\",\n                                                                    children: \"10th B\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 695,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"10th-C\",\n                                                                    children: \"10th C\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 696,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"11th-A\",\n                                                                    children: \"11th A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 697,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"11th-B\",\n                                                                    children: \"11th B\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 698,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"11th-C\",\n                                                                    children: \"11th C\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 699,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"12th-A\",\n                                                                    children: \"12th A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 700,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"12th-B\",\n                                                                    children: \"12th B\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 701,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"12th-C\",\n                                                                    children: \"12th C\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 702,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 685,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 683,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"department\",\n                                                    children: \"Department\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 707,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: newStudent.department,\n                                                    onValueChange: (value)=>setNewStudent({\n                                                            ...newStudent,\n                                                            department: value\n                                                        }),\n                                                    disabled: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select department\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 714,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 713,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Science\",\n                                                                    children: \"Science\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 717,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Commerce\",\n                                                                    children: \"Commerce\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 718,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Arts\",\n                                                                    children: \"Arts\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 719,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Computer Science\",\n                                                                    children: \"Computer Science\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 720,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 716,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 708,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 706,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"schedule\",\n                                                    children: \"Time Schedule\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 725,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: newStudent.schedule,\n                                                    onValueChange: (value)=>setNewStudent({\n                                                            ...newStudent,\n                                                            schedule: value\n                                                        }),\n                                                    disabled: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select schedule\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 732,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 731,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Morning Shift (8:00 AM - 2:00 PM)\",\n                                                                    children: \"Morning Shift (8:00 AM - 2:00 PM)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 735,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Afternoon Shift (2:00 PM - 8:00 PM)\",\n                                                                    children: \"Afternoon Shift (2:00 PM - 8:00 PM)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 738,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Full Day (8:00 AM - 4:00 PM)\",\n                                                                    children: \"Full Day (8:00 AM - 4:00 PM)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 741,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 734,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 726,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 724,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 650,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_9__.Separator, {}, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 747,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        editingStudent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleUpdateStudent,\n                                            className: \"flex-1\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 752,\n                                                    columnNumber: 21\n                                                }, this),\n                                                loading ? \"Updating...\" : \"Update Student\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 751,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleAddStudent,\n                                            className: \"flex-1\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 757,\n                                                    columnNumber: 21\n                                                }, this),\n                                                loading ? \"Adding...\" : \"Add Student\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 756,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: resetForm,\n                                            variant: \"outline\",\n                                            className: \"flex-1 bg-transparent\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 762,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Cancel\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 761,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 749,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 588,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 580,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: [\n                                        \"Registered Students (\",\n                                        students.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 773,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: [\n                                        \"All registered students with their login credentials - Stored in \",\n                                        storageInfo.mode,\n                                        \" storage\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 774,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 772,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: students.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"h-16 w-16 mx-auto text-gray-300 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 781,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-500 mb-2\",\n                                        children: \"No students registered yet\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 782,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: 'Click \"Add New Student\" to get started'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 783,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 780,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: students.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: student.image_url || \"/placeholder.svg?height=60&width=60\",\n                                                        alt: student.name,\n                                                        className: \"w-12 h-12 rounded-full border-2 border-gray-200 object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 790,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-lg\",\n                                                                children: student.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 796,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: [\n                                                                    student.class,\n                                                                    \" \",\n                                                                    student.department && `- ${student.department}`\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 797,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: student.phone\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 800,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            student.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: student.email\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 801,\n                                                                columnNumber: 43\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 795,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 789,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"font-mono text-xs\",\n                                                                        children: [\n                                                                            \"App: \",\n                                                                            student.application_number\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 809,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"ghost\",\n                                                                        onClick: ()=>copyToClipboard(student.application_number, \"app\"),\n                                                                        className: \"h-6 w-6 p-0\",\n                                                                        children: copiedText === `app-${student.application_number}` ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 819,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 821,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 812,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 808,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                        variant: \"secondary\",\n                                                                        className: \"text-xs\",\n                                                                        children: [\n                                                                            \"Phone: \",\n                                                                            student.phone\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 826,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"ghost\",\n                                                                        onClick: ()=>copyToClipboard(student.phone, \"phone\"),\n                                                                        className: \"h-6 w-6 p-0\",\n                                                                        children: copiedText === `phone-${student.phone}` ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 836,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 838,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 829,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 825,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 807,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleEditStudent(student),\n                                                                disabled: loading,\n                                                                className: \"h-8 w-8 p-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 853,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 846,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"destructive\",\n                                                                onClick: ()=>handleDeleteStudent(student),\n                                                                disabled: loading,\n                                                                className: \"h-8 w-8 p-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 862,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 855,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 845,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 805,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, student.id, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 788,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 786,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 778,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 771,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: [\n                                    \"Admin Instructions - \",\n                                    storageInfo.mode,\n                                    \" Storage\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 876,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 875,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-blue-700 mb-2\",\n                                                children: \"Required Fields:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 881,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Student Name (Full name required)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 883,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Phone Number (10 digits, unique)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 884,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Class Selection (from dropdown)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 885,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Student Photo (Upload or camera)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 886,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCDD Email (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 887,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCDD Department (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 888,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCDD Schedule (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 889,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 882,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 880,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-green-700 mb-2\",\n                                                children: \"Photo Requirements:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 893,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Clear face photo required\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 895,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 JPG, PNG, GIF formats supported\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 896,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Maximum file size: 5MB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 897,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Upload from device or take with camera\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 898,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Used for face verification at station\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 899,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Can be changed during editing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 900,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 894,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 892,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 879,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 878,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 874,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 438,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 437,\n        columnNumber: 5\n    }, this);\n}\nfunction StatCard({ icon, value, label, color }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow p-6 flex items-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: `text-${color}-500 text-3xl mr-4`,\n                children: icon\n            }, void 0, false, {\n                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 914,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-2xl font-bold\",\n                        children: value\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 916,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600\",\n                        children: label\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 917,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 915,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 913,\n        columnNumber: 5\n    }, this);\n}\nconst UserIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n        className: \"h-6 w-6 text-blue-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 923,\n        columnNumber: 24\n    }, undefined);\nconst EntryIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n        className: \"h-6 w-6 text-green-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 924,\n        columnNumber: 25\n    }, undefined);\nconst ExitIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n        className: \"h-6 w-6 text-red-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 925,\n        columnNumber: 24\n    }, undefined);\nconst TotalIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_Home_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n        className: \"h-6 w-6 text-purple-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 926,\n        columnNumber: 25\n    }, undefined);\nconst RemainIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n        className: \"h-6 w-6 text-orange-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 927,\n        columnNumber: 26\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/admin/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/alert.tsx":
/*!*********************************!*\
  !*** ./components/ui/alert.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* binding */ Alert),\n/* harmony export */   AlertDescription: () => (/* binding */ AlertDescription),\n/* harmony export */   AlertTitle: () => (/* binding */ AlertTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst alertVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\", {\n    variants: {\n        variant: {\n            default: \"bg-background text-foreground\",\n            destructive: \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Alert = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        role: \"alert\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(alertVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 26,\n        columnNumber: 3\n    }, undefined));\nAlert.displayName = \"Alert\";\nconst AlertTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"mb-1 font-medium leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined));\nAlertTitle.displayName = \"AlertTitle\";\nconst AlertDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm [&_p]:leading-relaxed\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nAlertDescription.displayName = \"AlertDescription\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/alert.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_1__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 26,\n        columnNumber: 10\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 8,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFFaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGtZQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJEOlxcaWRjYXJkXFxzbWFydGlkY2FyZFxcY29tcG9uZW50c1xcdWlcXGlucHV0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmNvbnN0IElucHV0ID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MSW5wdXRFbGVtZW50LCBSZWFjdC5Db21wb25lbnRQcm9wczxcImlucHV0XCI+PihcbiAgKHsgY2xhc3NOYW1lLCB0eXBlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICByZXR1cm4gKFxuICAgICAgPGlucHV0XG4gICAgICAgIHR5cGU9e3R5cGV9XG4gICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgXCJmbGV4IGgtMTAgdy1mdWxsIHJvdW5kZWQtbWQgYm9yZGVyIGJvcmRlci1pbnB1dCBiZy1iYWNrZ3JvdW5kIHB4LTMgcHktMiB0ZXh0LWJhc2UgcmluZy1vZmZzZXQtYmFja2dyb3VuZCBmaWxlOmJvcmRlci0wIGZpbGU6YmctdHJhbnNwYXJlbnQgZmlsZTp0ZXh0LXNtIGZpbGU6Zm9udC1tZWRpdW0gZmlsZTp0ZXh0LWZvcmVncm91bmQgcGxhY2Vob2xkZXI6dGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1vZmZzZXQtMiBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZGlzYWJsZWQ6b3BhY2l0eS01MCBtZDp0ZXh0LXNtXCIsXG4gICAgICAgICAgY2xhc3NOYW1lXG4gICAgICAgICl9XG4gICAgICAgIHJlZj17cmVmfVxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAvPlxuICAgIClcbiAgfVxuKVxuSW5wdXQuZGlzcGxheU5hbWUgPSBcIklucHV0XCJcblxuZXhwb3J0IHsgSW5wdXQgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJJbnB1dCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJ0eXBlIiwicHJvcHMiLCJyZWYiLCJpbnB1dCIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/label.tsx":
/*!*********************************!*\
  !*** ./components/ui/label.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 18,\n        columnNumber: 3\n    }, undefined));\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2xhYmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFOEI7QUFDeUI7QUFDVTtBQUVqQztBQUVoQyxNQUFNSSxnQkFBZ0JGLDZEQUFHQSxDQUN2QjtBQUdGLE1BQU1HLHNCQUFRTCw2Q0FBZ0IsQ0FJNUIsQ0FBQyxFQUFFTyxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNSLHVEQUFtQjtRQUNsQlEsS0FBS0E7UUFDTEYsV0FBV0osOENBQUVBLENBQUNDLGlCQUFpQkc7UUFDOUIsR0FBR0MsS0FBSzs7Ozs7O0FBR2JILE1BQU1NLFdBQVcsR0FBR1YsdURBQW1CLENBQUNVLFdBQVc7QUFFbkMiLCJzb3VyY2VzIjpbIkQ6XFxpZGNhcmRcXHNtYXJ0aWRjYXJkXFxjb21wb25lbnRzXFx1aVxcbGFiZWwudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgKiBhcyBMYWJlbFByaW1pdGl2ZSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LWxhYmVsXCJcbmltcG9ydCB7IGN2YSwgdHlwZSBWYXJpYW50UHJvcHMgfSBmcm9tIFwiY2xhc3MtdmFyaWFuY2UtYXV0aG9yaXR5XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBsYWJlbFZhcmlhbnRzID0gY3ZhKFxuICBcInRleHQtc20gZm9udC1tZWRpdW0gbGVhZGluZy1ub25lIHBlZXItZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIHBlZXItZGlzYWJsZWQ6b3BhY2l0eS03MFwiXG4pXG5cbmNvbnN0IExhYmVsID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4sXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4gJlxuICAgIFZhcmlhbnRQcm9wczx0eXBlb2YgbGFiZWxWYXJpYW50cz5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPExhYmVsUHJpbWl0aXZlLlJvb3RcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKGxhYmVsVmFyaWFudHMoKSwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuTGFiZWwuZGlzcGxheU5hbWUgPSBMYWJlbFByaW1pdGl2ZS5Sb290LmRpc3BsYXlOYW1lXG5cbmV4cG9ydCB7IExhYmVsIH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkxhYmVsUHJpbWl0aXZlIiwiY3ZhIiwiY24iLCJsYWJlbFZhcmlhbnRzIiwiTGFiZWwiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJyZWYiLCJSb290IiwiZGlzcGxheU5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/select.tsx":
/*!**********************************!*\
  !*** ./components/ui/select.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Select: () => (/* binding */ Select),\n/* harmony export */   SelectContent: () => (/* binding */ SelectContent),\n/* harmony export */   SelectGroup: () => (/* binding */ SelectGroup),\n/* harmony export */   SelectItem: () => (/* binding */ SelectItem),\n/* harmony export */   SelectLabel: () => (/* binding */ SelectLabel),\n/* harmony export */   SelectScrollDownButton: () => (/* binding */ SelectScrollDownButton),\n/* harmony export */   SelectScrollUpButton: () => (/* binding */ SelectScrollUpButton),\n/* harmony export */   SelectSeparator: () => (/* binding */ SelectSeparator),\n/* harmony export */   SelectTrigger: () => (/* binding */ SelectTrigger),\n/* harmony export */   SelectValue: () => (/* binding */ SelectValue)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-select */ \"(ssr)/./node_modules/@radix-ui/react-select/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Select,SelectGroup,SelectValue,SelectTrigger,SelectContent,SelectLabel,SelectItem,SelectSeparator,SelectScrollUpButton,SelectScrollDownButton auto */ \n\n\n\n\nconst Select = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst SelectGroup = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Group;\nconst SelectValue = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Value;\nconst SelectTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Trigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 opacity-50\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\select.tsx\",\n                lineNumber: 28,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 19,\n        columnNumber: 3\n    }, undefined));\nSelectTrigger.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Trigger.displayName;\nconst SelectScrollUpButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollUpButton, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default items-center justify-center py-1\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\select.tsx\",\n            lineNumber: 47,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined));\nSelectScrollUpButton.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollUpButton.displayName;\nconst SelectScrollDownButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollDownButton, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default items-center justify-center py-1\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\select.tsx\",\n            lineNumber: 64,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 56,\n        columnNumber: 3\n    }, undefined));\nSelectScrollDownButton.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollDownButton.displayName;\nconst SelectContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, position = \"popper\", ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            ref: ref,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", position === \"popper\" && \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\", className),\n            position: position,\n            ...props,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectScrollUpButton, {}, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Viewport, {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-1\", position === \"popper\" && \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"),\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectScrollDownButton, {}, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\select.tsx\",\n            lineNumber: 75,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 74,\n        columnNumber: 3\n    }, undefined));\nSelectContent.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst SelectLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 106,\n        columnNumber: 3\n    }, undefined));\nSelectLabel.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Label.displayName;\nconst SelectItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\select.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\select.tsx\",\n                lineNumber: 126,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ItemText, {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\select.tsx\",\n                lineNumber: 132,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 118,\n        columnNumber: 3\n    }, undefined));\nSelectItem.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Item.displayName;\nconst SelectSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"-mx-1 my-1 h-px bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 141,\n        columnNumber: 3\n    }, undefined));\nSelectSeparator.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Separator.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/select.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/separator.tsx":
/*!*************************************!*\
  !*** ./components/ui/separator.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Separator: () => (/* binding */ Separator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-separator */ \"(ssr)/./node_modules/@radix-ui/react-separator/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Separator auto */ \n\n\n\nconst Separator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, orientation = \"horizontal\", decorative = true, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        decorative: decorative,\n        orientation: orientation,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"shrink-0 bg-border\", orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\components\\\\ui\\\\separator.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, undefined));\nSeparator.displayName = _radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3NlcGFyYXRvci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFOEI7QUFDaUM7QUFFL0I7QUFFaEMsTUFBTUcsMEJBQVlILDZDQUFnQixDQUloQyxDQUNFLEVBQUVLLFNBQVMsRUFBRUMsY0FBYyxZQUFZLEVBQUVDLGFBQWEsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFDdEVDLG9CQUVBLDhEQUFDUiwyREFBdUI7UUFDdEJRLEtBQUtBO1FBQ0xGLFlBQVlBO1FBQ1pELGFBQWFBO1FBQ2JELFdBQVdILDhDQUFFQSxDQUNYLHNCQUNBSSxnQkFBZ0IsZUFBZSxtQkFBbUIsa0JBQ2xERDtRQUVELEdBQUdHLEtBQUs7Ozs7OztBQUlmTCxVQUFVUSxXQUFXLEdBQUdWLDJEQUF1QixDQUFDVSxXQUFXO0FBRXZDIiwic291cmNlcyI6WyJEOlxcaWRjYXJkXFxzbWFydGlkY2FyZFxcY29tcG9uZW50c1xcdWlcXHNlcGFyYXRvci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCAqIGFzIFNlcGFyYXRvclByaW1pdGl2ZSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXNlcGFyYXRvclwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuY29uc3QgU2VwYXJhdG9yID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgU2VwYXJhdG9yUHJpbWl0aXZlLlJvb3Q+LFxuICBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIFNlcGFyYXRvclByaW1pdGl2ZS5Sb290PlxuPihcbiAgKFxuICAgIHsgY2xhc3NOYW1lLCBvcmllbnRhdGlvbiA9IFwiaG9yaXpvbnRhbFwiLCBkZWNvcmF0aXZlID0gdHJ1ZSwgLi4ucHJvcHMgfSxcbiAgICByZWZcbiAgKSA9PiAoXG4gICAgPFNlcGFyYXRvclByaW1pdGl2ZS5Sb290XG4gICAgICByZWY9e3JlZn1cbiAgICAgIGRlY29yYXRpdmU9e2RlY29yYXRpdmV9XG4gICAgICBvcmllbnRhdGlvbj17b3JpZW50YXRpb259XG4gICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICBcInNocmluay0wIGJnLWJvcmRlclwiLFxuICAgICAgICBvcmllbnRhdGlvbiA9PT0gXCJob3Jpem9udGFsXCIgPyBcImgtWzFweF0gdy1mdWxsXCIgOiBcImgtZnVsbCB3LVsxcHhdXCIsXG4gICAgICAgIGNsYXNzTmFtZVxuICAgICAgKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApXG4pXG5TZXBhcmF0b3IuZGlzcGxheU5hbWUgPSBTZXBhcmF0b3JQcmltaXRpdmUuUm9vdC5kaXNwbGF5TmFtZVxuXG5leHBvcnQgeyBTZXBhcmF0b3IgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiU2VwYXJhdG9yUHJpbWl0aXZlIiwiY24iLCJTZXBhcmF0b3IiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwib3JpZW50YXRpb24iLCJkZWNvcmF0aXZlIiwicHJvcHMiLCJyZWYiLCJSb290IiwiZGlzcGxheU5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/separator.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/database-store.ts":
/*!*******************************!*\
  !*** ./lib/database-store.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dbStore: () => (/* binding */ dbStore)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/./lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ dbStore auto */ \n// Local storage keys\nconst STUDENTS_KEY = \"smart_id_students\";\nconst ENTRIES_KEY = \"smart_id_entries\";\nclass DatabaseStore {\n    isSupabaseAvailable() {\n        return _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase !== null && \"undefined\" !== \"undefined\";\n    }\n    isLocalStorageAvailable() {\n        return  false && 0;\n    }\n    // Local Storage Methods\n    saveStudentsToLocal(students) {\n        if (this.isLocalStorageAvailable()) {\n            localStorage.setItem(STUDENTS_KEY, JSON.stringify(students));\n        }\n    }\n    loadStudentsFromLocal() {\n        if (!this.isLocalStorageAvailable()) return [];\n        try {\n            const data = localStorage.getItem(STUDENTS_KEY);\n            if (!data) return [];\n            const students = JSON.parse(data);\n            return students.map((s)=>({\n                    ...s,\n                    createdAt: new Date(s.createdAt),\n                    updatedAt: new Date(s.updatedAt)\n                }));\n        } catch (error) {\n            console.error(\"Error loading students from localStorage:\", error);\n            return [];\n        }\n    }\n    saveEntriesToLocal(entries) {\n        if (this.isLocalStorageAvailable()) {\n            localStorage.setItem(ENTRIES_KEY, JSON.stringify(entries));\n        }\n    }\n    loadEntriesFromLocal() {\n        if (!this.isLocalStorageAvailable()) return [];\n        try {\n            const data = localStorage.getItem(ENTRIES_KEY);\n            if (!data) return [];\n            const entries = JSON.parse(data);\n            return entries.map((e)=>({\n                    ...e,\n                    entryTime: new Date(e.entryTime),\n                    exitTime: e.exitTime ? new Date(e.exitTime) : undefined,\n                    createdAt: new Date(e.createdAt)\n                }));\n        } catch (error) {\n            console.error(\"Error loading entries from localStorage:\", error);\n            return [];\n        }\n    }\n    // Student Management\n    async addStudent(student) {\n        const res = await fetch(\"/api/students\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(student)\n        });\n        if (!res.ok) throw new Error(\"Failed to add student\");\n        const data = await res.json();\n        return {\n            ...data,\n            createdAt: new Date(data.createdAt),\n            updatedAt: new Date(data.updatedAt)\n        };\n    }\n    async getStudents() {\n        const res = await fetch(\"/api/students\");\n        if (!res.ok) throw new Error(\"Failed to fetch students\");\n        const data = await res.json();\n        return data.map((s)=>({\n                ...s,\n                createdAt: new Date(s.createdAt),\n                updatedAt: new Date(s.updatedAt)\n            }));\n    }\n    async getStudentByAppNumber(appNumber) {\n        const res = await fetch(`/api/students?application_number=${encodeURIComponent(appNumber)}`);\n        if (!res.ok) return null;\n        const data = await res.json();\n        if (!data || data.length === 0) return null;\n        const s = data[0];\n        return {\n            ...s,\n            createdAt: new Date(s.createdAt),\n            updatedAt: new Date(s.updatedAt)\n        };\n    }\n    async getStudentByAppAndPhone(appNumber, phone) {\n        const url = `/api/students?application_number=${encodeURIComponent(appNumber)}&phone=${encodeURIComponent(phone)}`;\n        const res = await fetch(url);\n        if (!res.ok) return null;\n        const data = await res.json();\n        if (!data || data.length === 0) return null;\n        const s = data[0];\n        return {\n            ...s,\n            createdAt: new Date(s.createdAt),\n            updatedAt: new Date(s.updatedAt)\n        };\n    }\n    async updateStudent(id, updates) {\n        const res = await fetch(\"/api/students\", {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                id,\n                ...updates\n            })\n        });\n        if (!res.ok) throw new Error(\"Failed to update student\");\n        const data = await res.json();\n        return {\n            ...data,\n            createdAt: new Date(data.createdAt),\n            updatedAt: new Date(data.updatedAt)\n        };\n    }\n    async deleteStudent(id) {\n        const res = await fetch(\"/api/students\", {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                id\n            })\n        });\n        if (!res.ok) throw new Error(\"Failed to delete student\");\n        return true;\n    }\n    // Entry Log Management\n    async addEntry(studentId, applicationNumber, studentName) {\n        if (this.isSupabaseAvailable() && _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase) {\n            // Use Supabase\n            const { data: existingEntry } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"entry_logs\").select(\"*\").eq(\"student_id\", studentId).is(\"exit_time\", null).order(\"entry_time\", {\n                ascending: false\n            }).limit(1).single();\n            if (existingEntry) {\n                // Student is inside, mark exit\n                const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"entry_logs\").update({\n                    exit_time: new Date().toISOString(),\n                    status: \"exit\"\n                }).eq(\"id\", existingEntry.id).select().single();\n                if (error) {\n                    console.error(\"Error updating entry:\", error);\n                    throw new Error(\"Failed to record exit\");\n                }\n                return this.convertEntryLogDates(data);\n            } else {\n                // New entry\n                const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"entry_logs\").insert({\n                    student_id: studentId,\n                    application_number: applicationNumber,\n                    student_name: studentName,\n                    status: \"entry\",\n                    verified: true\n                }).select().single();\n                if (error) {\n                    console.error(\"Error adding entry:\", error);\n                    throw new Error(\"Failed to record entry\");\n                }\n                return this.convertEntryLogDates(data);\n            }\n        } else {\n            // Use localStorage\n            const entries = this.loadEntriesFromLocal();\n            // Check if student is already inside\n            const existingEntry = entries.find((e)=>e.student_id === studentId && !e.exitTime);\n            if (existingEntry) {\n                // Student is inside, mark exit\n                existingEntry.exitTime = new Date();\n                existingEntry.status = \"exit\";\n                this.saveEntriesToLocal(entries);\n                return existingEntry;\n            } else {\n                // New entry\n                const newEntry = {\n                    id: `entry_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n                    student_id: studentId,\n                    application_number: applicationNumber,\n                    student_name: studentName,\n                    entryTime: new Date(),\n                    status: \"entry\",\n                    verified: true,\n                    createdAt: new Date()\n                };\n                entries.unshift(newEntry);\n                this.saveEntriesToLocal(entries);\n                return newEntry;\n            }\n        }\n    }\n    async getStudentEntries(studentId) {\n        if (this.isSupabaseAvailable() && _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase) {\n            // Use Supabase\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"entry_logs\").select(\"*\").eq(\"student_id\", studentId).order(\"entry_time\", {\n                ascending: false\n            });\n            if (error) {\n                console.error(\"Error fetching student entries:\", error);\n                return [];\n            }\n            return data.map(this.convertEntryLogDates);\n        } else {\n            // Use localStorage\n            const entries = this.loadEntriesFromLocal();\n            return entries.filter((e)=>e.student_id === studentId);\n        }\n    }\n    async getAllEntries() {\n        if (this.isSupabaseAvailable() && _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase) {\n            // Use Supabase\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"entry_logs\").select(\"*\").order(\"entry_time\", {\n                ascending: false\n            });\n            if (error) {\n                console.error(\"Error fetching entries:\", error);\n                return [];\n            }\n            return data.map(this.convertEntryLogDates);\n        } else {\n            // Use localStorage\n            return this.loadEntriesFromLocal();\n        }\n    }\n    async getTodayEntries() {\n        if (this.isSupabaseAvailable() && _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase) {\n            // Use Supabase\n            const today = new Date();\n            today.setHours(0, 0, 0, 0);\n            const tomorrow = new Date(today);\n            tomorrow.setDate(tomorrow.getDate() + 1);\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"entry_logs\").select(\"*\").gte(\"entry_time\", today.toISOString()).lt(\"entry_time\", tomorrow.toISOString()).order(\"entry_time\", {\n                ascending: false\n            });\n            if (error) {\n                console.error(\"Error fetching today entries:\", error);\n                return [];\n            }\n            return data.map(this.convertEntryLogDates);\n        } else {\n            // Use localStorage\n            const entries = this.loadEntriesFromLocal();\n            const today = new Date().toDateString();\n            return entries.filter((e)=>e.entryTime.toDateString() === today);\n        }\n    }\n    async getEntriesByDate(date) {\n        if (this.isSupabaseAvailable() && _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase) {\n            // Use Supabase\n            const startDate = new Date(date);\n            startDate.setHours(0, 0, 0, 0);\n            const endDate = new Date(date);\n            endDate.setHours(23, 59, 59, 999);\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"entry_logs\").select(\"*\").gte(\"entry_time\", startDate.toISOString()).lte(\"entry_time\", endDate.toISOString()).order(\"entry_time\", {\n                ascending: false\n            });\n            if (error) {\n                console.error(\"Error fetching entries by date:\", error);\n                return [];\n            }\n            return data.map(this.convertEntryLogDates);\n        } else {\n            // Use localStorage\n            const entries = this.loadEntriesFromLocal();\n            const targetDate = new Date(date).toDateString();\n            return entries.filter((e)=>e.entryTime.toDateString() === targetDate);\n        }\n    }\n    async recordEntry(entryData) {\n        if (this.isSupabaseAvailable() && _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase) {\n            // Use Supabase\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"entry_logs\").insert({\n                student_id: entryData.student_id,\n                student_name: entryData.student_name,\n                application_number: entryData.application_number,\n                status: entryData.status,\n                face_match_score: entryData.face_match_score,\n                entry_time: entryData.entryTime.toISOString()\n            }).select().single();\n            if (error) {\n                throw new Error(`Failed to record entry: ${error.message}`);\n            }\n            return this.convertEntryLogDates(data);\n        } else {\n            // Use localStorage\n            const entries = this.loadEntriesFromLocal();\n            const newEntry = {\n                id: `entry_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n                student_id: entryData.student_id,\n                student_name: entryData.student_name,\n                application_number: entryData.application_number,\n                status: entryData.status,\n                verified: true,\n                face_match_score: entryData.face_match_score || null,\n                entryTime: entryData.entryTime,\n                exitTime: undefined,\n                createdAt: new Date()\n            };\n            entries.unshift(newEntry);\n            this.saveEntriesToLocal(entries);\n            return newEntry;\n        }\n    }\n    async deleteEntry(id) {\n        if (this.isSupabaseAvailable() && _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase) {\n            // Use Supabase\n            const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"entry_logs\").delete().eq(\"id\", id);\n            if (error) {\n                throw new Error(`Failed to delete entry: ${error.message}`);\n            }\n        } else {\n            // Use localStorage\n            const entries = this.loadEntriesFromLocal();\n            const filteredEntries = entries.filter((e)=>e.id !== id);\n            this.saveEntriesToLocal(filteredEntries);\n        }\n    }\n    // Admin Authentication\n    async authenticateAdmin(username, password) {\n        if (this.isSupabaseAvailable() && _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase) {\n            // Use Supabase\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"admin_users\").select(\"*\").eq(\"username\", username).single();\n            if (error || !data) {\n                return false;\n            }\n            // Simple password check (in production, use proper hashing)\n            return password === \"admin123\";\n        } else {\n            // Fallback authentication for demo\n            return username === \"admin\" && password === \"admin123\";\n        }\n    }\n    // Utility functions\n    generateApplicationNumber() {\n        const year = new Date().getFullYear();\n        const random = Math.floor(Math.random() * 10000).toString().padStart(4, \"0\");\n        return `APP${year}${random}`;\n    }\n    convertStudentDates(student) {\n        return {\n            ...student,\n            createdAt: new Date(student.created_at),\n            updatedAt: new Date(student.updated_at)\n        };\n    }\n    convertEntryLogDates(entry) {\n        return {\n            ...entry,\n            entryTime: new Date(entry.entry_time),\n            exitTime: entry.exit_time ? new Date(entry.exit_time) : undefined,\n            createdAt: new Date(entry.created_at)\n        };\n    }\n    // Clear all local data (for testing)\n    clearLocalData() {\n        if (this.isLocalStorageAvailable()) {\n            localStorage.removeItem(STUDENTS_KEY);\n            localStorage.removeItem(ENTRIES_KEY);\n        }\n    }\n    // Get storage info\n    async getStorageInfo() {\n        // Try API first\n        try {\n            const studentsRes = await fetch(\"/api/students\");\n            const entriesRes = await fetch(\"/api/entries\");\n            if (studentsRes.ok && entriesRes.ok) {\n                const students = await studentsRes.json();\n                const entries = await entriesRes.json();\n                return {\n                    mode: \"Cloud\",\n                    studentsCount: students.length,\n                    entriesCount: entries.length\n                };\n            }\n        } catch (e) {\n        // ignore\n        }\n        // Fallback to localStorage\n        const localStudents = this.loadStudentsFromLocal ? this.loadStudentsFromLocal() : [];\n        const localEntries = this.loadEntriesFromLocal ? this.loadEntriesFromLocal() : [];\n        return {\n            mode: \"Local\",\n            studentsCount: localStudents.length,\n            entriesCount: localEntries.length\n        };\n    }\n}\nconst dbStore = new DatabaseStore();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/database-store.ts\n");

/***/ }),

/***/ "(ssr)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Check if we're in a browser environment and have the required env vars\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || \"\";\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || \"\";\n// Create a fallback client that won't break during build\nconst supabase = supabaseUrl && supabaseAnonKey ? (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey) : null;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJEOlxcaWRjYXJkXFxzbWFydGlkY2FyZFxcbGliXFx1dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Capp%5C%5Cadmin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Capp%5C%5Cadmin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/admin/page.tsx */ \"(ssr)/./app/admin/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNpZGNhcmQlNUMlNUNzbWFydGlkY2FyZCU1QyU1Q2FwcCU1QyU1Q2FkbWluJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9KQUFrRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcaWRjYXJkXFxcXHNtYXJ0aWRjYXJkXFxcXGFwcFxcXFxhZG1pblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Capp%5C%5Cadmin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cidcard%5C%5Csmartidcard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/react-remove-scroll","vendor-chunks/@floating-ui","vendor-chunks/react-style-singleton","vendor-chunks/react-remove-scroll-bar","vendor-chunks/use-callback-ref","vendor-chunks/use-sidecar","vendor-chunks/tslib","vendor-chunks/get-nonce","vendor-chunks/aria-hidden"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=D%3A%5Cidcard%5Csmartidcard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Csmartidcard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();