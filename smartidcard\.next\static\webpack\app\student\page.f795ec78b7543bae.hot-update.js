"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/student/page",{

/***/ "(app-pages-browser)/./app/student/page.tsx":
/*!******************************!*\
  !*** ./app/student/page.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentApp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _lib_database_store__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/database-store */ \"(app-pages-browser)/./lib/database-store.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction StudentApp() {\n    _s();\n    const [currentStudent, setCurrentStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [studentEntries, setStudentEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [copiedQR, setCopiedQR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const entriesPerPage = 5;\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentApp.useEffect\": ()=>{\n            if (true) {\n                const studentLoggedIn = localStorage.getItem(\"studentLoggedIn\");\n                const studentId = localStorage.getItem(\"studentId\");\n                if (!studentLoggedIn || !studentId) {\n                    router.push(\"/\");\n                    return;\n                }\n                loadStudentData(studentId);\n            }\n        }\n    }[\"StudentApp.useEffect\"], [\n        router\n    ]);\n    // Auto-refresh student entries every 3 seconds for real-time updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentApp.useEffect\": ()=>{\n            if (!isAuthenticated || !currentStudent) return;\n            const interval = setInterval({\n                \"StudentApp.useEffect.interval\": ()=>{\n                    console.log(\"🔄 Auto-refreshing student entries...\");\n                    loadStudentEntries();\n                }\n            }[\"StudentApp.useEffect.interval\"], 3000) // 3 seconds for faster updates\n            ;\n            return ({\n                \"StudentApp.useEffect\": ()=>clearInterval(interval)\n            })[\"StudentApp.useEffect\"];\n        }\n    }[\"StudentApp.useEffect\"], [\n        isAuthenticated,\n        currentStudent\n    ]);\n    const loadStudentEntries = async ()=>{\n        if (!currentStudent) return;\n        try {\n            console.log(\"\\uD83D\\uDD0D Fetching entries for student: \".concat(currentStudent.name, \" (\").concat(currentStudent.application_number, \")\"));\n            // Try to get all entries and filter for this student\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 10000) // 10 second timeout\n            ;\n            const entriesRes = await fetch('/api/entries', {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (entriesRes.ok) {\n                const allEntries = await entriesRes.json();\n                console.log(\"\\uD83D\\uDCCA Total entries in database: \".concat(allEntries.length));\n                // Filter entries for this student by both student_id and application_number\n                const studentEntries = allEntries.filter((entry)=>{\n                    const matchesId = entry.student_id === currentStudent.id;\n                    const matchesAppNumber = entry.application_number === currentStudent.application_number;\n                    const matchesName = entry.student_name === currentStudent.name;\n                    return matchesId || matchesAppNumber || matchesName;\n                });\n                // Sort by entry time (newest first)\n                studentEntries.sort((a, b)=>{\n                    const dateA = new Date(a.entry_time || a.entryTime || a.timestamp);\n                    const dateB = new Date(b.entry_time || b.entryTime || b.timestamp);\n                    return dateB.getTime() - dateA.getTime();\n                });\n                setStudentEntries(studentEntries);\n                console.log(\"✅ Found \".concat(studentEntries.length, \" entries for \").concat(currentStudent.name, \":\"), studentEntries);\n                // Debug: Check entry data structure\n                if (studentEntries.length > 0) {\n                    console.log(\"📊 Sample entry structure:\", studentEntries[0]);\n                    console.log(\"📊 Entry properties:\", Object.keys(studentEntries[0]));\n                }\n            } else {\n                console.error(\"❌ API error: \".concat(entriesRes.status, \" - \").concat(entriesRes.statusText));\n                // Try fallback - use empty array but don't crash\n                setStudentEntries([]);\n            }\n        } catch (error) {\n            console.error(\"❌ Error refreshing entries:\", error);\n            // Handle specific error types\n            if (error instanceof Error) {\n                if (error.name === 'AbortError') {\n                    console.error(\"❌ Request timeout - API took too long to respond\");\n                } else if (error.message.includes('Failed to fetch')) {\n                    console.error(\"❌ Network error - Check if server is running on port 3001\");\n                } else {\n                    console.error(\"❌ Unexpected error:\", error.message);\n                }\n            }\n            // Set empty entries array as fallback\n            setStudentEntries([]);\n        }\n    };\n    const loadStudentData = async (studentId)=>{\n        try {\n            setLoading(true);\n            setIsAuthenticated(true);\n            // Get student data from shared MongoDB via API\n            const studentsRes = await fetch('/api/students');\n            if (!studentsRes.ok) throw new Error('Failed to fetch students');\n            const students = await studentsRes.json();\n            const student = students.find((s)=>s.id === studentId);\n            if (student) {\n                setCurrentStudent(student);\n                // Get student's entry history from shared MongoDB\n                try {\n                    const entriesRes = await fetch(\"/api/entries?studentId=\".concat(student.id));\n                    if (entriesRes.ok) {\n                        const allEntries = await entriesRes.json();\n                        // Filter entries for this student\n                        const studentEntries = allEntries.filter((entry)=>entry.student_id === student.id || entry.application_number === student.application_number);\n                        setStudentEntries(studentEntries);\n                        console.log(\"✅ Loaded \".concat(studentEntries.length, \" entries for student \").concat(student.name));\n                    } else {\n                        console.log(\"⚠️ Could not fetch entries from API, using fallback\");\n                        const entries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_8__.dbStore.getStudentEntries(student.id);\n                        setStudentEntries(entries);\n                    }\n                } catch (entriesError) {\n                    console.log(\"⚠️ API error, using database fallback for entries\");\n                    const entries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_8__.dbStore.getStudentEntries(student.id);\n                    setStudentEntries(entries);\n                }\n            } else {\n                handleLogout();\n            }\n        } catch (error) {\n            console.error(\"Error loading student data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleLogout = ()=>{\n        if (true) {\n            localStorage.removeItem(\"studentLoggedIn\");\n            localStorage.removeItem(\"studentId\");\n            localStorage.removeItem(\"studentAppNumber\");\n        }\n        router.push(\"/\");\n    };\n    const handleRefresh = ()=>{\n        if (currentStudent) {\n            loadStudentData(currentStudent.id);\n        }\n    };\n    const generateSimpleQRCode = ()=>{\n        if (!currentStudent) return \"\";\n        return currentStudent.application_number;\n    };\n    const copyQRData = async ()=>{\n        try {\n            const qrData = generateSimpleQRCode();\n            await navigator.clipboard.writeText(qrData);\n            setCopiedQR(true);\n            setTimeout(()=>setCopiedQR(false), 2000);\n        } catch (error) {\n            alert(\"Failed to copy QR data\");\n        }\n    };\n    const formatTime = (date)=>{\n        if (!date) return \"N/A\";\n        const dateObj = typeof date === 'string' ? new Date(date) : date;\n        if (isNaN(dateObj.getTime())) return \"Invalid Date\";\n        return dateObj.toLocaleString(\"en-IN\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const formatDate = (date)=>{\n        if (!date) return \"N/A\";\n        const dateObj = typeof date === 'string' ? new Date(date) : date;\n        if (isNaN(dateObj.getTime())) return \"Invalid Date\";\n        return dateObj.toLocaleString(\"en-IN\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\"\n        });\n    };\n    const calculateDuration = (entryTime, exitTime)=>{\n        if (!entryTime || !exitTime) return null;\n        const entryDate = typeof entryTime === 'string' ? new Date(entryTime) : entryTime;\n        const exitDate = typeof exitTime === 'string' ? new Date(exitTime) : exitTime;\n        if (isNaN(entryDate.getTime()) || isNaN(exitDate.getTime())) return null;\n        const diffMs = exitDate.getTime() - entryDate.getTime();\n        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n        const diffMinutes = Math.floor(diffMs % (1000 * 60 * 60) / (1000 * 60));\n        if (diffHours > 0) {\n            return \"\".concat(diffHours, \"h \").concat(diffMinutes, \"m\");\n        } else {\n            return \"\".concat(diffMinutes, \"m\");\n        }\n    };\n    const toggleSection = (section)=>{\n        setActiveSection(activeSection === section ? null : section);\n    };\n    if (!isAuthenticated || !currentStudent) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-blue-50 p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-700\",\n                        children: \"Loading student data...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                lineNumber: 252,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n            lineNumber: 251,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-green-50 to-blue-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"shadow-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: (currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.image_url) || \"/placeholder.svg?height=50&width=50\",\n                                            alt: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name,\n                                            className: \"w-10 h-10 rounded-full border-2 border-green-200 object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: [\n                                                        \"Welcome, \",\n                                                        currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"App No: \",\n                                                        currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.application_number\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 w-full sm:w-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleRefresh,\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            disabled: loading,\n                                            className: \"flex-1 sm:flex-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"mr-1 h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only sm:not-sr-only\",\n                                                    children: \"Refresh\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleLogout,\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"flex-1 sm:flex-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-1 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only sm:not-sr-only\",\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"border border-blue-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>toggleSection(\"idCard\"),\n                                    className: \"w-full p-4 flex items-center justify-between text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-100 p-2 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-5 w-5 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Digital ID Card\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Show your QR code at security stations\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this),\n                                        activeSection === \"idCard\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, this),\n                                activeSection === \"idCard\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"pt-0 px-4 pb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl p-5 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-xl font-bold\",\n                                                                children: \"College Identity Card\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-blue-100 text-sm\",\n                                                                children: \"Official Identification Document\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-blue-200\",\n                                                                children: \"Valid Until\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold\",\n                                                                children: \"31/12/2025\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col md:flex-row gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white border-2 border-white rounded-lg overflow-hidden\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: (currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.image_url) || \"/placeholder.svg?height=100&width=80\",\n                                                                            alt: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name,\n                                                                            className: \"w-20 h-24 object-cover\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                            lineNumber: 347,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 346,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                                className: \"text-lg font-bold\",\n                                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 355,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2 space-y-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-xs text-blue-200\",\n                                                                                                children: \"Application Number\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                                lineNumber: 358,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"font-mono font-bold\",\n                                                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.application_number\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                                lineNumber: 359,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 357,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-xs text-blue-200\",\n                                                                                                children: \"Department\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                                lineNumber: 362,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"font-bold\",\n                                                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.department\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                                lineNumber: 363,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 361,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 356,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 354,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 grid grid-cols-2 gap-3 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-blue-200\",\n                                                                                children: \"Class\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 371,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-bold\",\n                                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.class\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 372,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 370,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-blue-200\",\n                                                                                children: \"Phone\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 375,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-bold\",\n                                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.phone\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 376,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 374,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-white p-2 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: \"https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=\".concat(encodeURIComponent(generateSimpleQRCode())),\n                                                                    alt: \"Student QR Code\",\n                                                                    className: \"w-32 h-32\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2 text-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-mono text-xs bg-blue-400/20 px-2 py-1 rounded\",\n                                                                    children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.application_number\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 390,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                value: generateSimpleQRCode(),\n                                                                readOnly: true,\n                                                                className: \"bg-white/10 border-white/20 text-white placeholder-white/50 text-center font-mono text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 399,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                onClick: copyQRData,\n                                                                size: \"sm\",\n                                                                className: \"absolute top-1 right-1 h-6 px-2 bg-white/20 hover:bg-white/30\",\n                                                                children: copiedQR ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 409,\n                                                                    columnNumber: 37\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 409,\n                                                                    columnNumber: 69\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 398,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-blue-200 mt-1 text-center\",\n                                                        children: \"Copy application number for manual entry at station\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 308,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"border border-green-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>toggleSection(\"details\"),\n                                    className: \"w-full p-4 flex items-center justify-between text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-100 p-2 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-5 w-5 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Personal Details\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"View your registration information\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 15\n                                        }, this),\n                                        activeSection === \"details\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 13\n                                }, this),\n                                activeSection === \"details\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"pt-0 px-4 pb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col md:flex-row gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: (currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.image_url) || \"/placeholder.svg?height=120&width=120\",\n                                                        alt: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name,\n                                                        className: \"w-24 h-24 rounded-full border-4 border-green-200 object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"mt-2 text-lg font-semibold\",\n                                                        children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"mt-1\",\n                                                        children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.class\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-gray-500 text-sm\",\n                                                                        children: \"Phone\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 459,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.phone\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 460,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-gray-500 text-sm\",\n                                                                        children: \"Email\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 463,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: (currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.email) || \"Not provided\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 464,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-gray-500 text-sm\",\n                                                                        children: \"Department\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 467,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.department\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 468,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-gray-500 text-sm\",\n                                                                        children: \"Schedule\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 471,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: (currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.schedule) || \"Not assigned\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 472,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                className: \"text-gray-500 text-sm\",\n                                                                children: \"Application Number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                className: \"font-mono mt-1\",\n                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.application_number\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 480,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 444,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"border border-amber-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>toggleSection(\"history\"),\n                                    className: \"w-full p-4 flex items-center justify-between text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-amber-100 p-2 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-5 w-5 text-amber-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Entry/Exit History\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"View your campus access records\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 15\n                                        }, this),\n                                        activeSection === \"history\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 13\n                                }, this),\n                                activeSection === \"history\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"pt-0 px-4 pb-4\",\n                                    children: studentEntries.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-8 w-8 mx-auto text-gray-300 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 text-sm\",\n                                                children: \"No entries recorded yet\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 518,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: studentEntries.slice((currentPage - 1) * entriesPerPage, currentPage * entriesPerPage).map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg border-l-4 border-l-blue-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 rounded-full \".concat(entry.status === \"entry\" ? \"bg-green-500\" : \"bg-red-500\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 529,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-2 mb-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm font-medium\",\n                                                                                        children: entry.status === \"entry\" ? \"Entry\" : \"Exit\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 532,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                        variant: \"outline\",\n                                                                                        className: \"text-xs px-1 py-0\",\n                                                                                        children: entry.verified ? \"✓\" : \"⚠\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 535,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 531,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-gray-600 mb-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: \"Entry:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 542,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    \" \",\n                                                                                    formatDate(entry.entry_time || entry.entryTime),\n                                                                                    \" • \",\n                                                                                    formatTime(entry.entry_time || entry.entryTime)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 541,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            entry.status === \"exit\" && entry.exit_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs text-gray-600\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"font-medium\",\n                                                                                                children: \"Exit:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                                lineNumber: 549,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            \" \",\n                                                                                            formatDate(entry.exit_time),\n                                                                                            \" • \",\n                                                                                            formatTime(entry.exit_time)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 548,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    calculateDuration(entry.entry_time || entry.entryTime, entry.exit_time) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs text-blue-600 font-medium mt-1\",\n                                                                                        children: [\n                                                                                            \"Duration: \",\n                                                                                            calculateDuration(entry.entry_time || entry.entryTime, entry.exit_time)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 553,\n                                                                                        columnNumber: 39\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 530,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 528,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(entry.status === \"entry\" ? \"bg-green-100 text-green-700\" : \"bg-red-100 text-red-700\"),\n                                                                    children: entry.status === \"entry\" ? \"In\" : \"Out\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 562,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 561,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, entry.id, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 27\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 21\n                                            }, this),\n                                            studentEntries.length > entriesPerPage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mt-4 pt-3 border-t\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            \"Showing \",\n                                                            (currentPage - 1) * entriesPerPage + 1,\n                                                            \"-\",\n                                                            Math.min(currentPage * entriesPerPage, studentEntries.length),\n                                                            \" of \",\n                                                            studentEntries.length\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>setCurrentPage((prev)=>Math.max(1, prev - 1)),\n                                                                disabled: currentPage === 1,\n                                                                className: \"h-7 w-7 p-0\",\n                                                                children: \"←\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 581,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>setCurrentPage((prev)=>Math.min(Math.ceil(studentEntries.length / entriesPerPage), prev + 1)),\n                                                                disabled: currentPage >= Math.ceil(studentEntries.length / entriesPerPage),\n                                                                className: \"h-7 w-7 p-0\",\n                                                                children: \"→\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 580,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 576,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-3 pt-3 border-t bg-blue-50 rounded-lg p-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: [\n                                                                \"Total Entries: \",\n                                                                studentEntries.length\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 606,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: [\n                                                                \"Last Updated: \",\n                                                                new Date().toLocaleTimeString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 607,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 604,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 491,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"border border-blue-100\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            className: \"pb-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-5 w-5 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"How to Use Your Digital ID Card\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                lineNumber: 622,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 621,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-50 p-4 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-blue-700 mb-2 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 631,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"QR Code Scanning\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 630,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                    className: \"list-decimal list-inside space-y-1 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Show your QR code to station operator\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 635,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Operator will scan with the camera\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 636,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Hold QR code steady in front of camera\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 637,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"System retrieves your details automatically\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 638,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Proceed to face verification\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 639,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 634,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 629,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-50 p-4 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-green-700 mb-2 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 644,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Manual Input Option\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 643,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"list-disc list-inside space-y-1 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Copy your Application Number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 648,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: 'Go to station\\'s \"Manual Entry\" section'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 649,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Paste your Application Number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 650,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: 'Click \"Validate\" to retrieve details'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 651,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Continue with face verification\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 652,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 647,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 628,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.Alert, {\n                                    className: \"mt-4 bg-yellow-50 border-yellow-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.AlertDescription, {\n                                        className: \"text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold\",\n                                                children: \"Important:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 659,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" Your digital ID card is for official use only. Do not share it with unauthorized persons. Report lost cards immediately.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 658,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 657,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 627,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                    lineNumber: 620,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n            lineNumber: 262,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n        lineNumber: 261,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentApp, \"oWzI3S3hIeSS/z40AxYZOhRxnqU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = StudentApp;\nvar _c;\n$RefreshReg$(_c, \"StudentApp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/student/page.tsx\n"));

/***/ })

});