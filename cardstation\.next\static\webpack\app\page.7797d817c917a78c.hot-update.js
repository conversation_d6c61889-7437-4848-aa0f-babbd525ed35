"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/database-store.ts":
/*!*******************************!*\
  !*** ./lib/database-store.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dbStore: () => (/* binding */ dbStore)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ dbStore auto */ \n// Local storage keys\nconst STUDENTS_KEY = \"smart_id_students\";\nconst ENTRIES_KEY = \"smart_id_entries\";\nclass DatabaseStore {\n    isSupabaseAvailable() {\n        return _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase !== null && \"object\" !== \"undefined\";\n    }\n    isLocalStorageAvailable() {\n        return  true && typeof window.localStorage !== \"undefined\";\n    }\n    // Local Storage Methods\n    saveStudentsToLocal(students) {\n        if (this.isLocalStorageAvailable()) {\n            localStorage.setItem(STUDENTS_KEY, JSON.stringify(students));\n        }\n    }\n    loadStudentsFromLocal() {\n        if (!this.isLocalStorageAvailable()) return [];\n        try {\n            const data = localStorage.getItem(STUDENTS_KEY);\n            if (!data) {\n                // Return sample student for testing\n                const sampleStudent = {\n                    id: \"STU_001\",\n                    application_number: \"APP20254105\",\n                    name: \"Test Student\",\n                    phone: \"9772348371\",\n                    email: \"<EMAIL>\",\n                    class: \"12th\",\n                    department: \"Science\",\n                    schedule: \"Morning\",\n                    image_url: \"/placeholder-user.jpg\",\n                    createdAt: new Date(),\n                    updatedAt: new Date()\n                };\n                console.log(\"📝 Using sample student for testing:\", sampleStudent.name);\n                return [\n                    sampleStudent\n                ];\n            }\n            const students = JSON.parse(data);\n            return students.map((s)=>({\n                    ...s,\n                    createdAt: new Date(s.createdAt),\n                    updatedAt: new Date(s.updatedAt)\n                }));\n        } catch (error) {\n            console.error(\"Error loading students from localStorage:\", error);\n            return [];\n        }\n    }\n    saveEntriesToLocal(entries) {\n        if (this.isLocalStorageAvailable()) {\n            localStorage.setItem(ENTRIES_KEY, JSON.stringify(entries));\n        }\n    }\n    loadEntriesFromLocal() {\n        if (!this.isLocalStorageAvailable()) return [];\n        try {\n            const data = localStorage.getItem(ENTRIES_KEY);\n            if (!data) return [];\n            const entries = JSON.parse(data);\n            return entries.map((e)=>({\n                    ...e,\n                    entryTime: new Date(e.entryTime),\n                    exitTime: e.exitTime ? new Date(e.exitTime) : undefined,\n                    createdAt: new Date(e.createdAt)\n                }));\n        } catch (error) {\n            console.error(\"Error loading entries from localStorage:\", error);\n            return [];\n        }\n    }\n    // Student Management\n    async addStudent(student) {\n        const res = await fetch(\"/api/students\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(student)\n        });\n        if (!res.ok) throw new Error(\"Failed to add student\");\n        const data = await res.json();\n        return {\n            ...data,\n            createdAt: new Date(data.createdAt),\n            updatedAt: new Date(data.updatedAt)\n        };\n    }\n    async getStudents() {\n        try {\n            // Try API first\n            const res = await fetch(\"/api/students\");\n            if (res.ok) {\n                const data = await res.json();\n                console.log(\"✅ Students loaded from API:\", data.length);\n                return data.map((s)=>({\n                        ...s,\n                        createdAt: new Date(s.createdAt || s.created_at || new Date()),\n                        updatedAt: new Date(s.updatedAt || s.updated_at || new Date())\n                    }));\n            } else {\n                throw new Error(\"API failed\");\n            }\n        } catch (error) {\n            console.warn(\"⚠️ Students API not available, using localStorage fallback\");\n            // Fallback to localStorage\n            const localStudents = this.loadStudentsFromLocal();\n            console.log(\"✅ Students loaded from localStorage:\", localStudents.length);\n            return localStudents;\n        }\n    }\n    async getStudentByAppNumber(appNumber) {\n        try {\n            // Try API first\n            const res = await fetch(\"/api/students?application_number=\".concat(encodeURIComponent(appNumber)));\n            if (res.ok) {\n                const data = await res.json();\n                if (!data || data.length === 0) return null;\n                const s = data[0];\n                console.log(\"✅ Student found via API:\", s.name);\n                return {\n                    ...s,\n                    createdAt: new Date(s.createdAt || s.created_at || new Date()),\n                    updatedAt: new Date(s.updatedAt || s.updated_at || new Date())\n                };\n            } else {\n                throw new Error(\"API failed\");\n            }\n        } catch (error) {\n            console.warn(\"⚠️ Student API not available, using localStorage fallback\");\n            // Fallback to localStorage\n            const localStudents = this.loadStudentsFromLocal();\n            const student = localStudents.find((s)=>s.application_number === appNumber);\n            if (student) {\n                console.log(\"✅ Student found via localStorage:\", student.name);\n            } else {\n                console.log(\"❌ Student not found in localStorage\");\n            }\n            return student || null;\n        }\n    }\n    async getStudentByAppAndPhone(appNumber, phone) {\n        const url = \"/api/students?application_number=\".concat(encodeURIComponent(appNumber), \"&phone=\").concat(encodeURIComponent(phone));\n        const res = await fetch(url);\n        if (!res.ok) return null;\n        const data = await res.json();\n        if (!data || data.length === 0) return null;\n        const s = data[0];\n        return {\n            ...s,\n            createdAt: new Date(s.createdAt),\n            updatedAt: new Date(s.updatedAt)\n        };\n    }\n    async updateStudent(id, updates) {\n        const res = await fetch(\"/api/students\", {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                id,\n                ...updates\n            })\n        });\n        if (!res.ok) throw new Error(\"Failed to update student\");\n        const data = await res.json();\n        return {\n            ...data,\n            createdAt: new Date(data.createdAt),\n            updatedAt: new Date(data.updatedAt)\n        };\n    }\n    async deleteStudent(id) {\n        const res = await fetch(\"/api/students\", {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                id\n            })\n        });\n        if (!res.ok) throw new Error(\"Failed to delete student\");\n        return true;\n    }\n    // Entry Log Management - Using API route for better reliability\n    async addEntry(studentId, applicationNumber, studentName) {\n        try {\n            const entryData = {\n                student_id: studentId,\n                application_number: applicationNumber,\n                student_name: studentName,\n                verification_method: \"qr_and_face\",\n                qr_validated: true,\n                verification_timestamp: new Date().toISOString(),\n                station_id: \"main_entrance\"\n            };\n            console.log(\"Sending entry data to API:\", entryData);\n            try {\n                // Try API first\n                const res = await fetch('/api/entries', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(entryData)\n                });\n                if (res.ok) {\n                    const data = await res.json();\n                    console.log(\"✅ Entry recorded via API:\", data);\n                    return {\n                        ...data,\n                        entryTime: new Date(data.entry_time),\n                        exitTime: data.exit_time ? new Date(data.exit_time) : undefined,\n                        createdAt: new Date(data.created_at || data.entry_time),\n                        updatedAt: new Date(data.updated_at || data.entry_time)\n                    };\n                } else {\n                    throw new Error(\"API failed\");\n                }\n            } catch (apiError) {\n                console.warn(\"⚠️ API not available, using localStorage fallback\");\n                // Fallback to localStorage\n                const existingEntries = this.loadEntriesFromLocal();\n                // Check if student already has entry today without exit\n                const today = new Date();\n                today.setHours(0, 0, 0, 0);\n                const tomorrow = new Date(today);\n                tomorrow.setDate(tomorrow.getDate() + 1);\n                const todayEntry = existingEntries.find((entry)=>entry.student_id === studentId && entry.entryTime >= today && entry.entryTime < tomorrow && !entry.exitTime);\n                const now = new Date();\n                const entryId = \"entry_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substring(2, 9));\n                let newEntry;\n                if (todayEntry) {\n                    // This is an exit\n                    todayEntry.exitTime = now;\n                    todayEntry.status = \"exit\";\n                    // Update existing entry\n                    const updatedEntries = existingEntries.map((entry)=>entry.id === todayEntry.id ? todayEntry : entry);\n                    this.saveEntriesToLocal(updatedEntries);\n                    newEntry = todayEntry;\n                    console.log(\"✅ EXIT recorded via localStorage:\", newEntry);\n                } else {\n                    // This is an entry\n                    newEntry = {\n                        id: entryId,\n                        student_id: studentId,\n                        application_number: applicationNumber,\n                        student_name: studentName,\n                        entryTime: now,\n                        exitTime: undefined,\n                        status: \"entry\",\n                        verified: true,\n                        createdAt: now,\n                        updatedAt: now\n                    };\n                    existingEntries.push(newEntry);\n                    this.saveEntriesToLocal(existingEntries);\n                    console.log(\"✅ ENTRY recorded via localStorage:\", newEntry);\n                }\n                return newEntry;\n            }\n        } catch (error) {\n            console.error(\"❌ Error adding entry:\", error);\n            throw error;\n        }\n    }\n    async getStudentEntries(studentId) {\n        try {\n            // Use API route which handles both MongoDB and fallback\n            const res = await fetch(\"/api/entries?student_id=\".concat(encodeURIComponent(studentId)));\n            if (!res.ok) {\n                console.error(\"Failed to fetch entries from API\");\n                return [];\n            }\n            const data = await res.json();\n            return data.map((e)=>({\n                    ...e,\n                    entryTime: new Date(e.entry_time),\n                    exitTime: e.exit_time ? new Date(e.exit_time) : undefined,\n                    createdAt: new Date(e.created_at || e.entry_time),\n                    updatedAt: new Date(e.updated_at || e.entry_time)\n                }));\n        } catch (error) {\n            console.error(\"Error fetching student entries:\", error);\n            return [];\n        }\n    }\n    async getAllEntries() {\n        try {\n            // Try API first\n            const res = await fetch('/api/entries');\n            if (res.ok) {\n                const data = await res.json();\n                console.log(\"✅ Entries loaded from API:\", data.length);\n                return data.map((e)=>({\n                        ...e,\n                        entryTime: new Date(e.entry_time),\n                        exitTime: e.exit_time ? new Date(e.exit_time) : undefined,\n                        createdAt: new Date(e.created_at || e.entry_time),\n                        updatedAt: new Date(e.updated_at || e.entry_time)\n                    }));\n            } else {\n                throw new Error(\"API failed\");\n            }\n        } catch (error) {\n            console.warn(\"⚠️ API not available, using localStorage fallback\");\n            // Fallback to localStorage\n            const localEntries = this.loadEntriesFromLocal();\n            console.log(\"✅ Entries loaded from localStorage:\", localEntries.length);\n            return localEntries;\n        }\n    }\n    async getTodayEntries() {\n        try {\n            // Get all entries and filter for today\n            const allEntries = await this.getAllEntries();\n            const today = new Date().toDateString();\n            return allEntries.filter((e)=>e.entryTime.toDateString() === today);\n        } catch (error) {\n            console.error(\"Error fetching today entries:\", error);\n            return [];\n        }\n    }\n    // Admin Authentication\n    async authenticateAdmin(username, password) {\n        if (this.isSupabaseAvailable() && _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase) {\n            // Use Supabase\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"admin_users\").select(\"*\").eq(\"username\", username).single();\n            if (error || !data) {\n                return false;\n            }\n            // Simple password check (in production, use proper hashing)\n            return password === \"admin123\";\n        } else {\n            // Fallback authentication for demo\n            return username === \"admin\" && password === \"admin123\";\n        }\n    }\n    // Utility functions\n    generateApplicationNumber() {\n        const year = new Date().getFullYear();\n        const random = Math.floor(Math.random() * 10000).toString().padStart(4, \"0\");\n        return \"APP\".concat(year).concat(random);\n    }\n    convertStudentDates(student) {\n        return {\n            ...student,\n            createdAt: new Date(student.created_at),\n            updatedAt: new Date(student.updated_at)\n        };\n    }\n    convertEntryLogDates(entry) {\n        return {\n            ...entry,\n            entryTime: new Date(entry.entry_time),\n            exitTime: entry.exit_time ? new Date(entry.exit_time) : undefined,\n            createdAt: new Date(entry.created_at)\n        };\n    }\n    // Clear all local data (for testing)\n    clearLocalData() {\n        if (this.isLocalStorageAvailable()) {\n            localStorage.removeItem(STUDENTS_KEY);\n            localStorage.removeItem(ENTRIES_KEY);\n        }\n    }\n    // Get storage info\n    getStorageInfo() {\n        return {\n            mode: \"Cloud\",\n            studentsCount: 0,\n            entriesCount: 0\n        };\n    }\n}\nconst dbStore = new DatabaseStore();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/database-store.ts\n"));

/***/ })

});