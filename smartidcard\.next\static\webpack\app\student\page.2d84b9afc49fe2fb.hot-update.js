"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/student/page",{

/***/ "(app-pages-browser)/./app/student/page.tsx":
/*!******************************!*\
  !*** ./app/student/page.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentApp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _lib_database_store__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/database-store */ \"(app-pages-browser)/./lib/database-store.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction StudentApp() {\n    _s();\n    const [currentStudent, setCurrentStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [studentEntries, setStudentEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [copiedQR, setCopiedQR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const entriesPerPage = 5;\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentApp.useEffect\": ()=>{\n            if (true) {\n                const studentLoggedIn = localStorage.getItem(\"studentLoggedIn\");\n                const studentId = localStorage.getItem(\"studentId\");\n                if (!studentLoggedIn || !studentId) {\n                    router.push(\"/\");\n                    return;\n                }\n                loadStudentData(studentId);\n            }\n        }\n    }[\"StudentApp.useEffect\"], [\n        router\n    ]);\n    // Auto-refresh student entries every 3 seconds for real-time updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentApp.useEffect\": ()=>{\n            if (!isAuthenticated || !currentStudent) return;\n            const interval = setInterval({\n                \"StudentApp.useEffect.interval\": ()=>{\n                    console.log(\"🔄 Auto-refreshing student entries...\");\n                    loadStudentEntries();\n                }\n            }[\"StudentApp.useEffect.interval\"], 3000) // 3 seconds for faster updates\n            ;\n            return ({\n                \"StudentApp.useEffect\": ()=>clearInterval(interval)\n            })[\"StudentApp.useEffect\"];\n        }\n    }[\"StudentApp.useEffect\"], [\n        isAuthenticated,\n        currentStudent\n    ]);\n    const loadStudentEntries = async ()=>{\n        if (!currentStudent) return;\n        try {\n            console.log(\"\\uD83D\\uDD0D Fetching entries for student: \".concat(currentStudent.name, \" (\").concat(currentStudent.application_number, \")\"));\n            // Try to get all entries and filter for this student\n            const entriesRes = await fetch('/api/entries');\n            if (entriesRes.ok) {\n                const allEntries = await entriesRes.json();\n                console.log(\"\\uD83D\\uDCCA Total entries in database: \".concat(allEntries.length));\n                // Filter entries for this student by both student_id and application_number\n                const studentEntries = allEntries.filter((entry)=>{\n                    const matchesId = entry.student_id === currentStudent.id;\n                    const matchesAppNumber = entry.application_number === currentStudent.application_number;\n                    const matchesName = entry.student_name === currentStudent.name;\n                    return matchesId || matchesAppNumber || matchesName;\n                });\n                // Sort by entry time (newest first)\n                studentEntries.sort((a, b)=>{\n                    const dateA = new Date(a.entry_time || a.entryTime || a.timestamp);\n                    const dateB = new Date(b.entry_time || b.entryTime || b.timestamp);\n                    return dateB.getTime() - dateA.getTime();\n                });\n                setStudentEntries(studentEntries);\n                console.log(\"✅ Found \".concat(studentEntries.length, \" entries for \").concat(currentStudent.name, \":\"), studentEntries);\n                // Debug: Check entry data structure\n                if (studentEntries.length > 0) {\n                    console.log(\"📊 Sample entry structure:\", studentEntries[0]);\n                    console.log(\"📊 Entry properties:\", Object.keys(studentEntries[0]));\n                }\n            } else {\n                console.error(\"❌ API error: \".concat(entriesRes.status));\n            }\n        } catch (error) {\n            console.error(\"❌ Error refreshing entries:\", error);\n        }\n    };\n    const loadStudentData = async (studentId)=>{\n        try {\n            setLoading(true);\n            setIsAuthenticated(true);\n            // Get student data from shared MongoDB via API\n            const studentsRes = await fetch('/api/students');\n            if (!studentsRes.ok) throw new Error('Failed to fetch students');\n            const students = await studentsRes.json();\n            const student = students.find((s)=>s.id === studentId);\n            if (student) {\n                setCurrentStudent(student);\n                // Get student's entry history from shared MongoDB\n                try {\n                    const entriesRes = await fetch(\"/api/entries?studentId=\".concat(student.id));\n                    if (entriesRes.ok) {\n                        const allEntries = await entriesRes.json();\n                        // Filter entries for this student\n                        const studentEntries = allEntries.filter((entry)=>entry.student_id === student.id || entry.application_number === student.application_number);\n                        setStudentEntries(studentEntries);\n                        console.log(\"✅ Loaded \".concat(studentEntries.length, \" entries for student \").concat(student.name));\n                    } else {\n                        console.log(\"⚠️ Could not fetch entries from API, using fallback\");\n                        const entries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_8__.dbStore.getStudentEntries(student.id);\n                        setStudentEntries(entries);\n                    }\n                } catch (entriesError) {\n                    console.log(\"⚠️ API error, using database fallback for entries\");\n                    const entries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_8__.dbStore.getStudentEntries(student.id);\n                    setStudentEntries(entries);\n                }\n            } else {\n                handleLogout();\n            }\n        } catch (error) {\n            console.error(\"Error loading student data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleLogout = ()=>{\n        if (true) {\n            localStorage.removeItem(\"studentLoggedIn\");\n            localStorage.removeItem(\"studentId\");\n            localStorage.removeItem(\"studentAppNumber\");\n        }\n        router.push(\"/\");\n    };\n    const handleRefresh = ()=>{\n        if (currentStudent) {\n            loadStudentData(currentStudent.id);\n        }\n    };\n    const generateSimpleQRCode = ()=>{\n        if (!currentStudent) return \"\";\n        return currentStudent.application_number;\n    };\n    const copyQRData = async ()=>{\n        try {\n            const qrData = generateSimpleQRCode();\n            await navigator.clipboard.writeText(qrData);\n            setCopiedQR(true);\n            setTimeout(()=>setCopiedQR(false), 2000);\n        } catch (error) {\n            alert(\"Failed to copy QR data\");\n        }\n    };\n    const formatTime = (date)=>{\n        if (!date) return \"N/A\";\n        const dateObj = typeof date === 'string' ? new Date(date) : date;\n        if (isNaN(dateObj.getTime())) return \"Invalid Date\";\n        return dateObj.toLocaleString(\"en-IN\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const formatDate = (date)=>{\n        if (!date) return \"N/A\";\n        const dateObj = typeof date === 'string' ? new Date(date) : date;\n        if (isNaN(dateObj.getTime())) return \"Invalid Date\";\n        return dateObj.toLocaleString(\"en-IN\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\"\n        });\n    };\n    const calculateDuration = (entryTime, exitTime)=>{\n        if (!entryTime || !exitTime) return null;\n        const entryDate = typeof entryTime === 'string' ? new Date(entryTime) : entryTime;\n        const exitDate = typeof exitTime === 'string' ? new Date(exitTime) : exitTime;\n        if (isNaN(entryDate.getTime()) || isNaN(exitDate.getTime())) return null;\n        const diffMs = exitDate.getTime() - entryDate.getTime();\n        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n        const diffMinutes = Math.floor(diffMs % (1000 * 60 * 60) / (1000 * 60));\n        if (diffHours > 0) {\n            return \"\".concat(diffHours, \"h \").concat(diffMinutes, \"m\");\n        } else {\n            return \"\".concat(diffMinutes, \"m\");\n        }\n    };\n    const toggleSection = (section)=>{\n        setActiveSection(activeSection === section ? null : section);\n    };\n    if (!isAuthenticated || !currentStudent) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-blue-50 p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-700\",\n                        children: \"Loading student data...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                lineNumber: 224,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n            lineNumber: 223,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-green-50 to-blue-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"shadow-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: (currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.image_url) || \"/placeholder.svg?height=50&width=50\",\n                                            alt: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name,\n                                            className: \"w-10 h-10 rounded-full border-2 border-green-200 object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: [\n                                                        \"Welcome, \",\n                                                        currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"App No: \",\n                                                        currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.application_number\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 w-full sm:w-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleRefresh,\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            disabled: loading,\n                                            className: \"flex-1 sm:flex-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"mr-1 h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only sm:not-sr-only\",\n                                                    children: \"Refresh\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleLogout,\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"flex-1 sm:flex-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-1 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only sm:not-sr-only\",\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: ()=>router.push(\"/\"),\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"flex-1 sm:flex-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"mr-1 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only sm:not-sr-only\",\n                                                    children: \"Home\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"border border-blue-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>toggleSection(\"idCard\"),\n                                    className: \"w-full p-4 flex items-center justify-between text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-100 p-2 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-5 w-5 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Digital ID Card\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Show your QR code at security stations\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 15\n                                        }, this),\n                                        activeSection === \"idCard\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this),\n                                activeSection === \"idCard\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"pt-0 px-4 pb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl p-5 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-xl font-bold\",\n                                                                children: \"College Identity Card\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-blue-100 text-sm\",\n                                                                children: \"Official Identification Document\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-blue-200\",\n                                                                children: \"Valid Until\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold\",\n                                                                children: \"31/12/2025\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col md:flex-row gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white border-2 border-white rounded-lg overflow-hidden\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: (currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.image_url) || \"/placeholder.svg?height=100&width=80\",\n                                                                            alt: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name,\n                                                                            className: \"w-20 h-24 object-cover\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                            lineNumber: 329,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 328,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                                className: \"text-lg font-bold\",\n                                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 337,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2 space-y-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-xs text-blue-200\",\n                                                                                                children: \"Application Number\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                                lineNumber: 340,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"font-mono font-bold\",\n                                                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.application_number\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                                lineNumber: 341,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 339,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-xs text-blue-200\",\n                                                                                                children: \"Department\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                                lineNumber: 344,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"font-bold\",\n                                                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.department\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                                lineNumber: 345,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 343,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 338,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 336,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 grid grid-cols-2 gap-3 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-blue-200\",\n                                                                                children: \"Class\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 353,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-bold\",\n                                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.class\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 354,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 352,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-blue-200\",\n                                                                                children: \"Phone\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 357,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-bold\",\n                                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.phone\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 358,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 356,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-white p-2 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: \"https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=\".concat(encodeURIComponent(generateSimpleQRCode())),\n                                                                    alt: \"Student QR Code\",\n                                                                    className: \"w-32 h-32\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2 text-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-mono text-xs bg-blue-400/20 px-2 py-1 rounded\",\n                                                                    children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.application_number\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                value: generateSimpleQRCode(),\n                                                                readOnly: true,\n                                                                className: \"bg-white/10 border-white/20 text-white placeholder-white/50 text-center font-mono text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                onClick: copyQRData,\n                                                                size: \"sm\",\n                                                                className: \"absolute top-1 right-1 h-6 px-2 bg-white/20 hover:bg-white/30\",\n                                                                children: copiedQR ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 37\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 69\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-blue-200 mt-1 text-center\",\n                                                        children: \"Copy application number for manual entry at station\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"border border-green-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>toggleSection(\"details\"),\n                                    className: \"w-full p-4 flex items-center justify-between text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-100 p-2 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-5 w-5 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Personal Details\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"View your registration information\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 15\n                                        }, this),\n                                        activeSection === \"details\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 13\n                                }, this),\n                                activeSection === \"details\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"pt-0 px-4 pb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col md:flex-row gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: (currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.image_url) || \"/placeholder.svg?height=120&width=120\",\n                                                        alt: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name,\n                                                        className: \"w-24 h-24 rounded-full border-4 border-green-200 object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"mt-2 text-lg font-semibold\",\n                                                        children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"mt-1\",\n                                                        children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.class\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-gray-500 text-sm\",\n                                                                        children: \"Phone\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 441,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.phone\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 442,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-gray-500 text-sm\",\n                                                                        children: \"Email\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 445,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: (currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.email) || \"Not provided\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 446,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 444,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-gray-500 text-sm\",\n                                                                        children: \"Department\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 449,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.department\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 450,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-gray-500 text-sm\",\n                                                                        children: \"Schedule\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 453,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: (currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.schedule) || \"Not assigned\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 454,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 452,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                className: \"text-gray-500 text-sm\",\n                                                                children: \"Application Number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                className: \"font-mono mt-1\",\n                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.application_number\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"border border-amber-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>toggleSection(\"history\"),\n                                    className: \"w-full p-4 flex items-center justify-between text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-amber-100 p-2 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5 text-amber-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Entry/Exit History\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 483,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"View your campus access records\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 15\n                                        }, this),\n                                        activeSection === \"history\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 13\n                                }, this),\n                                activeSection === \"history\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"pt-0 px-4 pb-4\",\n                                    children: studentEntries.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-8 w-8 mx-auto text-gray-300 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 499,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 text-sm\",\n                                                children: \"No entries recorded yet\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: studentEntries.slice((currentPage - 1) * entriesPerPage, currentPage * entriesPerPage).map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg border-l-4 border-l-blue-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-3 h-3 rounded-full \".concat(entry.status === \"entry\" ? \"bg-green-500\" : \"bg-red-500\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 511,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-2 mb-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm font-medium\",\n                                                                                        children: entry.status === \"entry\" ? \"Entry\" : \"Exit\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 514,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                        variant: \"outline\",\n                                                                                        className: \"text-xs px-1 py-0\",\n                                                                                        children: entry.verified ? \"✓\" : \"⚠\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 517,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 513,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-gray-600 mb-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: \"Entry:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 524,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    \" \",\n                                                                                    formatDate(entry.entry_time || entry.entryTime),\n                                                                                    \" • \",\n                                                                                    formatTime(entry.entry_time || entry.entryTime)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 523,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            entry.status === \"exit\" && entry.exit_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs text-gray-600\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"font-medium\",\n                                                                                                children: \"Exit:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                                lineNumber: 531,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            \" \",\n                                                                                            formatDate(entry.exit_time),\n                                                                                            \" • \",\n                                                                                            formatTime(entry.exit_time)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 530,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    calculateDuration(entry.entry_time || entry.entryTime, entry.exit_time) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs text-blue-600 font-medium mt-1\",\n                                                                                        children: [\n                                                                                            \"Duration: \",\n                                                                                            calculateDuration(entry.entry_time || entry.entryTime, entry.exit_time)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 535,\n                                                                                        columnNumber: 39\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 512,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(entry.status === \"entry\" ? \"bg-green-100 text-green-700\" : \"bg-red-100 text-red-700\"),\n                                                                    children: entry.status === \"entry\" ? \"In\" : \"Out\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 544,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 543,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, entry.id, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 27\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 21\n                                            }, this),\n                                            studentEntries.length > entriesPerPage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mt-4 pt-3 border-t\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            \"Showing \",\n                                                            (currentPage - 1) * entriesPerPage + 1,\n                                                            \"-\",\n                                                            Math.min(currentPage * entriesPerPage, studentEntries.length),\n                                                            \" of \",\n                                                            studentEntries.length\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>setCurrentPage((prev)=>Math.max(1, prev - 1)),\n                                                                disabled: currentPage === 1,\n                                                                className: \"h-7 w-7 p-0\",\n                                                                children: \"←\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 563,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>setCurrentPage((prev)=>Math.min(Math.ceil(studentEntries.length / entriesPerPage), prev + 1)),\n                                                                disabled: currentPage >= Math.ceil(studentEntries.length / entriesPerPage),\n                                                                className: \"h-7 w-7 p-0\",\n                                                                children: \"→\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-3 pt-3 border-t bg-blue-50 rounded-lg p-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: [\n                                                                \"Total Entries: \",\n                                                                studentEntries.length\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 588,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: [\n                                                                \"Last Updated: \",\n                                                                new Date().toLocaleTimeString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 589,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 587,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"border border-blue-100\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            className: \"pb-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-5 w-5 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"How to Use Your Digital ID Card\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                lineNumber: 604,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 603,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-50 p-4 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-blue-700 mb-2 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 613,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"QR Code Scanning\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 612,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                    className: \"list-decimal list-inside space-y-1 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Show your QR code to station operator\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 617,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Operator will scan with the camera\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 618,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Hold QR code steady in front of camera\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 619,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"System retrieves your details automatically\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 620,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Proceed to face verification\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 621,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 616,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 611,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-50 p-4 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-green-700 mb-2 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 626,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Manual Input Option\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"list-disc list-inside space-y-1 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Copy your Application Number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: 'Go to station\\'s \"Manual Entry\" section'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 631,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Paste your Application Number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 632,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: 'Click \"Validate\" to retrieve details'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 633,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Continue with face verification\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 634,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 629,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 624,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.Alert, {\n                                    className: \"mt-4 bg-yellow-50 border-yellow-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.AlertDescription, {\n                                        className: \"text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold\",\n                                                children: \"Important:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 641,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" Your digital ID card is for official use only. Do not share it with unauthorized persons. Report lost cards immediately.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 639,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 609,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                    lineNumber: 602,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n            lineNumber: 234,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n        lineNumber: 233,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentApp, \"oWzI3S3hIeSS/z40AxYZOhRxnqU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = StudentApp;\nvar _c;\n$RefreshReg$(_c, \"StudentApp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/student/page.tsx\n"));

/***/ })

});