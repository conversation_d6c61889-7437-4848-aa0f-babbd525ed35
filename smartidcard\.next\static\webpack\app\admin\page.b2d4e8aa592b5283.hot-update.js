"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./app/admin/page.tsx":
/*!****************************!*\
  !*** ./app/admin/page.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _lib_database_store__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/database-store */ \"(app-pages-browser)/./lib/database-store.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AdminPanel() {\n    _s();\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingStudent, setEditingStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [copiedText, setCopiedText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastUpdated, setLastUpdated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dataSource, setDataSource] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"local\");\n    const [databaseConnected, setDatabaseConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [storageInfo, setStorageInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        mode: \"Local\",\n        studentsCount: 0,\n        entriesCount: 0\n    });\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalStudents: 0,\n        todayEntries: 0,\n        todayExits: 0,\n        totalEntries: 0\n    });\n    const [newStudent, setNewStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        phone: \"\",\n        email: \"\",\n        class: \"\",\n        department: \"\",\n        schedule: \"\",\n        image: \"\"\n    });\n    const [imagePreview, setImagePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageFile, setImageFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPanel.useEffect\": ()=>{\n            // Check if admin is logged in\n            if (true) {\n                const adminLoggedIn = localStorage.getItem(\"adminLoggedIn\");\n                if (!adminLoggedIn) {\n                    router.push(\"/\");\n                    return;\n                }\n            }\n            setIsAuthenticated(true);\n            checkDatabaseConnection();\n            loadData();\n        }\n    }[\"AdminPanel.useEffect\"], [\n        router\n    ]);\n    // Separate function to refresh only stats (not full page)\n    const refreshStats = async ()=>{\n        try {\n            console.log(\"🔄 Refreshing stats from shared MongoDB database...\");\n            let allEntries = [];\n            let todayEntries = [];\n            let studentsData = [];\n            let dataSource = \"mongodb\";\n            try {\n                // Use local API which connects to shared MongoDB\n                console.log(\"🔍 Fetching from shared MongoDB via local API...\");\n                const [localStudentsRes, localEntriesRes] = await Promise.all([\n                    fetch('/api/students'),\n                    fetch('/api/entries')\n                ]);\n                if (localStudentsRes.ok && localEntriesRes.ok) {\n                    studentsData = await localStudentsRes.json();\n                    allEntries = await localEntriesRes.json();\n                    dataSource = \"mongodb\";\n                    console.log(\"✅ Data fetched from shared MongoDB database\");\n                } else {\n                    throw new Error(\"MongoDB API not available\");\n                }\n            } catch (apiError) {\n                console.log(\"⚠️ API not available, using database store fallback...\");\n                // Fallback to database store\n                allEntries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getAllEntries();\n                todayEntries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getTodayEntries();\n                studentsData = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getStudents();\n                dataSource = \"fallback\";\n            }\n            // Filter today's entries if we got data from API\n            if (dataSource !== \"fallback\") {\n                const today = new Date().toDateString();\n                todayEntries = allEntries.filter((entry)=>{\n                    const entryDate = new Date(entry.entryTime || entry.entry_time).toDateString();\n                    return entryDate === today;\n                });\n            }\n            console.log(\"📊 Raw data:\", {\n                source: dataSource,\n                allEntries: allEntries.length,\n                todayEntries: todayEntries.length,\n                todayEntriesData: todayEntries,\n                students: studentsData.length\n            });\n            // Debug: Show sample entry data\n            if (allEntries.length > 0) {\n                console.log(\"📝 Sample entry:\", allEntries[0]);\n            }\n            if (todayEntries.length > 0) {\n                console.log(\"📅 Sample today entry:\", todayEntries[0]);\n            }\n            const entryCount = todayEntries.filter((e)=>e.status === 'entry').length;\n            const exitCount = todayEntries.filter((e)=>e.status === 'exit').length;\n            setStats({\n                totalStudents: studentsData.length,\n                todayEntries: entryCount,\n                todayExits: exitCount,\n                totalEntries: allEntries.length\n            });\n            setDataSource(dataSource);\n            setLastUpdated(new Date());\n            console.log(\"✅ Stats refreshed:\", {\n                source: dataSource,\n                totalStudents: studentsData.length,\n                todayEntries: entryCount,\n                todayExits: exitCount,\n                totalActivity: entryCount + exitCount,\n                allEntries: allEntries.length\n            });\n        } catch (error) {\n            console.error(\"❌ Error refreshing stats:\", error);\n        }\n    };\n    // Auto-reload only stats every 5 seconds (not full page)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPanel.useEffect\": ()=>{\n            if (!isAuthenticated) return;\n            const interval = setInterval({\n                \"AdminPanel.useEffect.interval\": ()=>{\n                    refreshStats() // Only refresh stats, not full page\n                    ;\n                }\n            }[\"AdminPanel.useEffect.interval\"], 5000) // 5 seconds\n            ;\n            return ({\n                \"AdminPanel.useEffect\": ()=>clearInterval(interval)\n            })[\"AdminPanel.useEffect\"];\n        }\n    }[\"AdminPanel.useEffect\"], [\n        isAuthenticated\n    ]);\n    const checkDatabaseConnection = async ()=>{\n        try {\n            // Check if we can connect to MongoDB via API\n            const studentsRes = await fetch('/api/students');\n            const entriesRes = await fetch('/api/entries');\n            if (studentsRes.ok && entriesRes.ok) {\n                const students = await studentsRes.json();\n                const entries = await entriesRes.json();\n                setDatabaseConnected(true);\n                setStorageInfo({\n                    mode: \"MongoDB Cloud\",\n                    studentsCount: students.length,\n                    entriesCount: entries.length\n                });\n                console.log(\"✅ MongoDB connection verified\");\n            } else {\n                throw new Error(\"API not responding\");\n            }\n        } catch (error) {\n            console.log(\"⚠️ MongoDB not available, using local storage\");\n            setDatabaseConnected(false);\n            const storageInfo = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getStorageInfo();\n            setStorageInfo(storageInfo);\n        }\n    };\n    const loadData = async ()=>{\n        try {\n            setLoading(true);\n            // Load students from local database (for admin management)\n            const studentsData = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getStudents();\n            setStudents(studentsData);\n            // Load stats from cardstation if available\n            await refreshStats();\n            const storageInfo = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getStorageInfo();\n            setStorageInfo(storageInfo);\n        } catch (error) {\n            console.error(\"Error loading data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleRefresh = ()=>{\n        loadData() // Full page refresh\n        ;\n    };\n    const handleStatsRefresh = ()=>{\n        refreshStats() // Only stats refresh\n        ;\n    };\n    const createTestEntry = async ()=>{\n        try {\n            console.log(\"🧪 Creating test entry in shared MongoDB...\");\n            const testEntry = {\n                student_id: \"test_student_1\",\n                application_number: \"APP20254105\",\n                student_name: \"Test Student\",\n                verification_method: \"manual_test\",\n                qr_validated: true,\n                verification_timestamp: new Date().toISOString(),\n                station_id: \"admin_panel\"\n            };\n            // Use local API which connects to shared MongoDB\n            const response = await fetch('/api/entries', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(testEntry)\n            });\n            if (response.ok) {\n                const result = await response.json();\n                console.log(\"✅ Test entry created in shared MongoDB:\", result);\n                alert(\"Test entry created successfully in shared database!\");\n                refreshStats() // Refresh stats to show new entry\n                ;\n            } else {\n                console.error(\"❌ Failed to create test entry:\", response.status);\n                alert(\"Failed to create test entry\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error creating test entry:\", error);\n            alert(\"Error creating test entry\");\n        }\n    };\n    const handleLogout = ()=>{\n        if (true) {\n            localStorage.removeItem(\"adminLoggedIn\");\n            localStorage.removeItem(\"adminUsername\");\n        }\n        router.push(\"/\");\n    };\n    // Handle image file selection\n    const handleImageSelect = (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (!file) return;\n        // Validate file type\n        if (!file.type.startsWith(\"image/\")) {\n            alert(\"Please select a valid image file (JPG, PNG, GIF, etc.)\");\n            return;\n        }\n        // Validate file size (max 5MB)\n        if (file.size > 5 * 1024 * 1024) {\n            alert(\"Image size should be less than 5MB\");\n            return;\n        }\n        setImageFile(file);\n        // Create preview\n        const reader = new FileReader();\n        reader.onload = (e)=>{\n            var _e_target;\n            const result = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n            setImagePreview(result);\n            setNewStudent({\n                ...newStudent,\n                image: result\n            });\n        };\n        reader.readAsDataURL(file);\n    };\n    // Remove selected image\n    const removeImage = ()=>{\n        setImageFile(null);\n        setImagePreview(null);\n        setNewStudent({\n            ...newStudent,\n            image: \"\"\n        });\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    // Take photo using camera\n    const takePhoto = async ()=>{\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({\n                video: true\n            });\n            // Create a video element to capture the stream\n            const video = document.createElement(\"video\");\n            video.srcObject = stream;\n            video.autoplay = true;\n            // Create a modal or popup to show camera feed\n            const modal = document.createElement(\"div\");\n            modal.style.cssText = \"\\n        position: fixed;\\n        top: 0;\\n        left: 0;\\n        width: 100%;\\n        height: 100%;\\n        background: rgba(0,0,0,0.8);\\n        display: flex;\\n        justify-content: center;\\n        align-items: center;\\n        z-index: 1000;\\n      \";\n            const container = document.createElement(\"div\");\n            container.style.cssText = \"\\n        background: white;\\n        padding: 20px;\\n        border-radius: 10px;\\n        text-align: center;\\n      \";\n            const canvas = document.createElement(\"canvas\");\n            const captureBtn = document.createElement(\"button\");\n            captureBtn.textContent = \"Capture Photo\";\n            captureBtn.style.cssText = \"\\n        background: #3b82f6;\\n        color: white;\\n        padding: 10px 20px;\\n        border: none;\\n        border-radius: 5px;\\n        margin: 10px;\\n        cursor: pointer;\\n      \";\n            const cancelBtn = document.createElement(\"button\");\n            cancelBtn.textContent = \"Cancel\";\n            cancelBtn.style.cssText = \"\\n        background: #6b7280;\\n        color: white;\\n        padding: 10px 20px;\\n        border: none;\\n        border-radius: 5px;\\n        margin: 10px;\\n        cursor: pointer;\\n      \";\n            container.appendChild(video);\n            container.appendChild(document.createElement(\"br\"));\n            container.appendChild(captureBtn);\n            container.appendChild(cancelBtn);\n            modal.appendChild(container);\n            document.body.appendChild(modal);\n            // Capture photo\n            captureBtn.onclick = ()=>{\n                canvas.width = video.videoWidth;\n                canvas.height = video.videoHeight;\n                const ctx = canvas.getContext(\"2d\");\n                ctx === null || ctx === void 0 ? void 0 : ctx.drawImage(video, 0, 0);\n                const imageData = canvas.toDataURL(\"image/jpeg\", 0.8);\n                setImagePreview(imageData);\n                setNewStudent({\n                    ...newStudent,\n                    image: imageData\n                });\n                // Stop camera and close modal\n                stream.getTracks().forEach((track)=>track.stop());\n                document.body.removeChild(modal);\n            };\n            // Cancel\n            cancelBtn.onclick = ()=>{\n                stream.getTracks().forEach((track)=>track.stop());\n                document.body.removeChild(modal);\n            };\n        } catch (error) {\n            alert(\"Camera access denied or not available\");\n        }\n    };\n    const validateForm = ()=>{\n        if (!newStudent.name.trim()) {\n            alert(\"Student name is required\");\n            return false;\n        }\n        if (!newStudent.phone.trim()) {\n            alert(\"Phone number is required\");\n            return false;\n        }\n        if (newStudent.phone.length !== 10 || !/^\\d+$/.test(newStudent.phone)) {\n            alert(\"Phone number must be exactly 10 digits\");\n            return false;\n        }\n        if (!newStudent.class) {\n            alert(\"Class selection is required\");\n            return false;\n        }\n        if (!newStudent.image) {\n            alert(\"Student photo is required. Please upload an image or take a photo.\");\n            return false;\n        }\n        return true;\n    };\n    const handleAddStudent = async ()=>{\n        if (!validateForm()) return;\n        // Check if phone number already exists\n        const existingStudent = students.find((s)=>s.phone === newStudent.phone);\n        if (existingStudent) {\n            alert(\"Phone number already exists!\");\n            return;\n        }\n        setLoading(true);\n        try {\n            const applicationNumber = _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.generateApplicationNumber();\n            const student = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.addStudent({\n                ...newStudent,\n                application_number: applicationNumber,\n                image_url: newStudent.image\n            });\n            await loadData();\n            resetForm();\n            alert(\"Student Added Successfully!\\n\\nName: \".concat(student.name, \"\\nApplication Number: \").concat(applicationNumber, \"\\nPhone: \").concat(student.phone, \"\\n\\nPlease provide Application Number and Phone Number to the student for login.\\n\\nData saved in \").concat(storageInfo.mode, \" storage.\"));\n        } catch (error) {\n            alert(\"Error adding student. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleEditStudent = (student)=>{\n        setEditingStudent(student);\n        setNewStudent({\n            name: student.name,\n            phone: student.phone,\n            email: student.email || \"\",\n            class: student.class,\n            department: student.department || \"\",\n            schedule: student.schedule || \"\",\n            image: student.image_url || \"\"\n        });\n        setImagePreview(student.image_url || null);\n        setShowAddForm(false);\n    };\n    const handleUpdateStudent = async ()=>{\n        if (!validateForm() || !editingStudent) return;\n        // Check if phone number already exists (excluding current student)\n        const existingStudent = students.find((s)=>s.phone === newStudent.phone && s.id !== editingStudent.id);\n        if (existingStudent) {\n            alert(\"Phone number already exists!\");\n            return;\n        }\n        setLoading(true);\n        try {\n            await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.updateStudent(editingStudent.id, {\n                name: newStudent.name,\n                phone: newStudent.phone,\n                email: newStudent.email || null,\n                class: newStudent.class,\n                department: newStudent.department || null,\n                schedule: newStudent.schedule || null,\n                image_url: newStudent.image\n            });\n            await loadData();\n            resetForm();\n            alert(\"Student updated successfully!\\n\\nData saved in \".concat(storageInfo.mode, \" storage.\"));\n        } catch (error) {\n            alert(\"Error updating student. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDeleteStudent = async (student)=>{\n        if (confirm(\"Are you sure you want to delete \".concat(student.name, \"?\\n\\nThis action cannot be undone.\"))) {\n            try {\n                setLoading(true);\n                await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.deleteStudent(student.id);\n                await loadData();\n                alert(\"Student deleted successfully!\\n\\nData updated in \".concat(storageInfo.mode, \" storage.\"));\n            } catch (error) {\n                alert(\"Error deleting student. Please try again.\");\n            } finally{\n                setLoading(false);\n            }\n        }\n    };\n    const copyToClipboard = async (text, type)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n            setCopiedText(\"\".concat(type, \"-\").concat(text));\n            setTimeout(()=>setCopiedText(null), 2000);\n        } catch (error) {\n            alert(\"Failed to copy to clipboard\");\n        }\n    };\n    const resetForm = ()=>{\n        setNewStudent({\n            name: \"\",\n            phone: \"\",\n            email: \"\",\n            class: \"\",\n            department: \"\",\n            schedule: \"\",\n            image: \"\"\n        });\n        setImagePreview(null);\n        setImageFile(null);\n        setShowAddForm(false);\n        setEditingStudent(null);\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    const today = new Date().toISOString().slice(0, 10);\n    const totalStudents = students.length;\n    // Replace the following with your actual attendance/logs array if available\n    // For demonstration, using an empty array as placeholder\n    const logs = [] // Replace with actual logs source\n    ;\n    const todaysEntries = logs.filter((e)=>e.type === \"entry\" && e.timestamp.slice(0, 10) === today).length;\n    const todaysExits = logs.filter((e)=>e.type === \"exit\" && e.timestamp.slice(0, 10) === today).length;\n    const totalEntries = logs.filter((e)=>e.type === \"entry\").length;\n    const remainingStudents = totalStudents - todaysExits;\n    if (!isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 574,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-3xl\",\n                                            children: \"Admin Panel\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 585,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            className: \"text-lg\",\n                                            children: [\n                                                \"Student Management System - \",\n                                                storageInfo.mode,\n                                                \" Storage\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 584,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleRefresh,\n                                            variant: \"outline\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Refresh\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 591,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleLogout,\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 596,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Logout\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 583,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 582,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 581,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.Alert, {\n                    className: databaseConnected ? \"border-green-200 bg-green-50\" : \"border-yellow-200 bg-yellow-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"h-4 w-4 \".concat(databaseConnected ? \"text-green-600\" : \"text-yellow-600\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 606,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.AlertDescription, {\n                            className: databaseConnected ? \"text-green-800\" : \"text-yellow-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: [\n                                        storageInfo.mode,\n                                        \" Storage Active:\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 13\n                                }, this),\n                                \" \",\n                                databaseConnected ? \"Data syncs across all devices automatically\" : \"Data saved locally on this device (\".concat(storageInfo.studentsCount, \" students, \").concat(storageInfo.entriesCount, \" entries)\")\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 607,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 605,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"bg-blue-50 border-blue-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 sm:p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl sm:text-4xl font-bold text-blue-600 mb-2\",\n                                        children: stats.totalStudents\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 620,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm sm:text-base font-medium text-blue-700\",\n                                        children: \"Total Students\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-500 mt-1\",\n                                        children: \"Registered\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 619,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 618,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"bg-green-50 border-green-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 sm:p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl sm:text-4xl font-bold text-green-600 mb-2\",\n                                        children: stats.todayEntries\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 635,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm sm:text-base font-medium text-green-700\",\n                                        children: \"Total Entries\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 638,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-green-500 mt-1\",\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 634,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 633,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"bg-red-50 border-red-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 sm:p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl sm:text-4xl font-bold text-red-600 mb-2\",\n                                        children: stats.todayExits\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm sm:text-base font-medium text-red-700\",\n                                        children: \"Total Exits\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 653,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-red-500 mt-1\",\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 649,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 648,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"bg-purple-50 border-purple-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 sm:p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl sm:text-4xl font-bold text-purple-600 mb-2\",\n                                        children: stats.todayEntries + stats.todayExits\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 665,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm sm:text-base font-medium text-purple-700\",\n                                        children: \"Total Activity\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 668,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-purple-500 mt-1\",\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 671,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 664,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 663,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 616,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center gap-2 text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 681,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Auto-refreshing every 5 seconds\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 682,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs px-2 py-1 rounded \".concat(dataSource === 'mongodb' ? 'bg-green-100 text-green-700' : 'bg-yellow-100 text-yellow-700'),\n                                    children: dataSource === 'mongodb' ? '�️ Shared MongoDB' : '💾 Local Fallback'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 683,\n                                    columnNumber: 13\n                                }, this),\n                                lastUpdated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: [\n                                        \"• \",\n                                        lastUpdated.toLocaleTimeString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 691,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 680,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: handleStatsRefresh,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"text-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"mr-1 h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 703,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Refresh Stats\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 697,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: createTestEntry,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"text-xs bg-green-50 hover:bg-green-100\",\n                                    children: \"\\uD83E\\uDDEA Test Entry\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 706,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 696,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 679,\n                    columnNumber: 9\n                }, this),\n                !showAddForm && !editingStudent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>setShowAddForm(true),\n                            className: \"w-full h-16 text-lg\",\n                            disabled: loading,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"mr-2 h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 722,\n                                    columnNumber: 17\n                                }, this),\n                                \"Add New Student\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 721,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 720,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 719,\n                    columnNumber: 11\n                }, this),\n                (showAddForm || editingStudent) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: editingStudent ? \"Edit Student\" : \"Add New Student\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 733,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: [\n                                        editingStudent ? \"Update student information\" : \"Fill required fields to register a new student\",\n                                        \" - Data will be saved in \",\n                                        storageInfo.mode,\n                                        \" storage\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 734,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 732,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            className: \"text-base font-semibold\",\n                                            children: \"Student Photo *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 742,\n                                            columnNumber: 17\n                                        }, this),\n                                        imagePreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: imagePreview || \"/placeholder.svg\",\n                                                            alt: \"Student preview\",\n                                                            className: \"w-32 h-32 rounded-full border-4 border-blue-200 object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 748,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: removeImage,\n                                                            size: \"sm\",\n                                                            variant: \"destructive\",\n                                                            className: \"absolute -top-2 -right-2 h-6 w-6 rounded-full p-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 759,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 753,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 747,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-green-600\",\n                                                            children: \"✅ Photo uploaded successfully\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 763,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: ()=>{\n                                                                var _fileInputRef_current;\n                                                                return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                            },\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 765,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Change Photo\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 764,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 762,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 746,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-16 w-16 mx-auto text-gray-400 mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 772,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4\",\n                                                    children: \"Upload student photo (Required)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 773,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: ()=>{\n                                                                var _fileInputRef_current;\n                                                                return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                            },\n                                                            variant: \"outline\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 776,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Upload Photo\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 775,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: takePhoto,\n                                                            variant: \"outline\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 780,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Take Photo\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 779,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 774,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-2\",\n                                                    children: \"Supported formats: JPG, PNG, GIF (Max 5MB)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 784,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 771,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            ref: fileInputRef,\n                                            type: \"file\",\n                                            accept: \"image/*\",\n                                            onChange: handleImageSelect,\n                                            className: \"hidden\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 789,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 741,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_9__.Separator, {}, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 798,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"name\",\n                                                    children: \"Student Name *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 803,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"name\",\n                                                    value: newStudent.name,\n                                                    onChange: (e)=>setNewStudent({\n                                                            ...newStudent,\n                                                            name: e.target.value\n                                                        }),\n                                                    placeholder: \"Enter full name\",\n                                                    disabled: loading\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 804,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 802,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"phone\",\n                                                    children: \"Phone Number *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 813,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"phone\",\n                                                    value: newStudent.phone,\n                                                    onChange: (e)=>setNewStudent({\n                                                            ...newStudent,\n                                                            phone: e.target.value\n                                                        }),\n                                                    placeholder: \"10-digit phone number\",\n                                                    maxLength: 10,\n                                                    disabled: loading\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 814,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 812,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"email\",\n                                                    children: \"Email Address\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 824,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"email\",\n                                                    type: \"email\",\n                                                    value: newStudent.email,\n                                                    onChange: (e)=>setNewStudent({\n                                                            ...newStudent,\n                                                            email: e.target.value\n                                                        }),\n                                                    placeholder: \"<EMAIL>\",\n                                                    disabled: loading\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 825,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 823,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"class\",\n                                                    children: \"Class *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 835,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: newStudent.class,\n                                                    onValueChange: (value)=>setNewStudent({\n                                                            ...newStudent,\n                                                            class: value\n                                                        }),\n                                                    disabled: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select class\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 842,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 841,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"10th-A\",\n                                                                    children: \"10th A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 845,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"10th-B\",\n                                                                    children: \"10th B\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 846,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"10th-C\",\n                                                                    children: \"10th C\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 847,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"11th-A\",\n                                                                    children: \"11th A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 848,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"11th-B\",\n                                                                    children: \"11th B\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 849,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"11th-C\",\n                                                                    children: \"11th C\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 850,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"12th-A\",\n                                                                    children: \"12th A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 851,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"12th-B\",\n                                                                    children: \"12th B\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 852,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"12th-C\",\n                                                                    children: \"12th C\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 853,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 844,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 836,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 834,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"department\",\n                                                    children: \"Department\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 858,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: newStudent.department,\n                                                    onValueChange: (value)=>setNewStudent({\n                                                            ...newStudent,\n                                                            department: value\n                                                        }),\n                                                    disabled: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select department\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 865,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 864,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Science\",\n                                                                    children: \"Science\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 868,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Commerce\",\n                                                                    children: \"Commerce\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 869,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Arts\",\n                                                                    children: \"Arts\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 870,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Computer Science\",\n                                                                    children: \"Computer Science\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 871,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 867,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 859,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 857,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"schedule\",\n                                                    children: \"Time Schedule\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 876,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: newStudent.schedule,\n                                                    onValueChange: (value)=>setNewStudent({\n                                                            ...newStudent,\n                                                            schedule: value\n                                                        }),\n                                                    disabled: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select schedule\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 883,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 882,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Morning Shift (8:00 AM - 2:00 PM)\",\n                                                                    children: \"Morning Shift (8:00 AM - 2:00 PM)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 886,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Afternoon Shift (2:00 PM - 8:00 PM)\",\n                                                                    children: \"Afternoon Shift (2:00 PM - 8:00 PM)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 889,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Full Day (8:00 AM - 4:00 PM)\",\n                                                                    children: \"Full Day (8:00 AM - 4:00 PM)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 892,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 885,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 877,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 875,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 801,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_9__.Separator, {}, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 898,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        editingStudent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleUpdateStudent,\n                                            className: \"flex-1\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 903,\n                                                    columnNumber: 21\n                                                }, this),\n                                                loading ? \"Updating...\" : \"Update Student\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 902,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleAddStudent,\n                                            className: \"flex-1\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 908,\n                                                    columnNumber: 21\n                                                }, this),\n                                                loading ? \"Adding...\" : \"Add Student\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 907,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: resetForm,\n                                            variant: \"outline\",\n                                            className: \"flex-1 bg-transparent\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 913,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Cancel\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 912,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 900,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 739,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 731,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: [\n                                        \"Registered Students (\",\n                                        students.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 924,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: [\n                                        \"All registered students with their login credentials - Stored in \",\n                                        storageInfo.mode,\n                                        \" storage\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 925,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 923,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: students.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-16 w-16 mx-auto text-gray-300 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 932,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-500 mb-2\",\n                                        children: \"No students registered yet\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 933,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: 'Click \"Add New Student\" to get started'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 934,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 931,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: students.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: student.image_url || \"/placeholder.svg?height=60&width=60\",\n                                                        alt: student.name,\n                                                        className: \"w-12 h-12 rounded-full border-2 border-gray-200 object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 941,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-lg\",\n                                                                children: student.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 947,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: [\n                                                                    student.class,\n                                                                    \" \",\n                                                                    student.department && \"- \".concat(student.department)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 948,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: student.phone\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 951,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            student.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: student.email\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 952,\n                                                                columnNumber: 43\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 946,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 940,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"font-mono text-xs\",\n                                                                        children: [\n                                                                            \"App: \",\n                                                                            student.application_number\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 960,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"ghost\",\n                                                                        onClick: ()=>copyToClipboard(student.application_number, \"app\"),\n                                                                        className: \"h-6 w-6 p-0\",\n                                                                        children: copiedText === \"app-\".concat(student.application_number) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 970,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 972,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 963,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 959,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                        variant: \"secondary\",\n                                                                        className: \"text-xs\",\n                                                                        children: [\n                                                                            \"Phone: \",\n                                                                            student.phone\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 977,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"ghost\",\n                                                                        onClick: ()=>copyToClipboard(student.phone, \"phone\"),\n                                                                        className: \"h-6 w-6 p-0\",\n                                                                        children: copiedText === \"phone-\".concat(student.phone) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 987,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 989,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 980,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 976,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 958,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleEditStudent(student),\n                                                                disabled: loading,\n                                                                className: \"h-8 w-8 p-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 1004,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 997,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"destructive\",\n                                                                onClick: ()=>handleDeleteStudent(student),\n                                                                disabled: loading,\n                                                                className: \"h-8 w-8 p-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 1013,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 1006,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 996,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 956,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, student.id, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 939,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 937,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 929,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 922,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: [\n                                    \"Admin Instructions - \",\n                                    storageInfo.mode,\n                                    \" Storage\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 1027,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 1026,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-blue-700 mb-2\",\n                                                children: \"Required Fields:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 1032,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Student Name (Full name required)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1034,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Phone Number (10 digits, unique)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1035,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Class Selection (from dropdown)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1036,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Student Photo (Upload or camera)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1037,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCDD Email (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1038,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCDD Department (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1039,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCDD Schedule (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1040,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 1033,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 1031,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-green-700 mb-2\",\n                                                children: \"Photo Requirements:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 1044,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Clear face photo required\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1046,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 JPG, PNG, GIF formats supported\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1047,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Maximum file size: 5MB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1048,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Upload from device or take with camera\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1049,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Used for face verification at station\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1050,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Can be changed during editing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1051,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 1045,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 1043,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 1030,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 1029,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 1025,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 579,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 578,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPanel, \"gDv0hcFIWUxGhFUhf6ke2DYOoWQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AdminPanel;\nfunction StatCard(param) {\n    let { icon, value, label, color } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow p-6 flex items-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-\".concat(color, \"-500 text-3xl mr-4\"),\n                children: icon\n            }, void 0, false, {\n                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 1065,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-2xl font-bold\",\n                        children: value\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 1067,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600\",\n                        children: label\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 1068,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 1066,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 1064,\n        columnNumber: 5\n    }, this);\n}\n_c1 = StatCard;\nconst UserIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n        className: \"h-6 w-6 text-blue-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 1074,\n        columnNumber: 24\n    }, undefined);\n_c2 = UserIcon;\nconst EntryIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n        className: \"h-6 w-6 text-green-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 1075,\n        columnNumber: 25\n    }, undefined);\n_c3 = EntryIcon;\nconst ExitIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n        className: \"h-6 w-6 text-red-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 1076,\n        columnNumber: 24\n    }, undefined);\n_c4 = ExitIcon;\nconst TotalIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n        className: \"h-6 w-6 text-purple-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 1077,\n        columnNumber: 25\n    }, undefined);\n_c5 = TotalIcon;\nconst RemainIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n        className: \"h-6 w-6 text-orange-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 1078,\n        columnNumber: 26\n    }, undefined);\n_c6 = RemainIcon;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"AdminPanel\");\n$RefreshReg$(_c1, \"StatCard\");\n$RefreshReg$(_c2, \"UserIcon\");\n$RefreshReg$(_c3, \"EntryIcon\");\n$RefreshReg$(_c4, \"ExitIcon\");\n$RefreshReg$(_c5, \"TotalIcon\");\n$RefreshReg$(_c6, \"RemainIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/page.tsx\n"));

/***/ })

});