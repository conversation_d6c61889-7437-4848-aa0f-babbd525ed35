"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_pages-dir-browser_node_modules_next_dist_pages__error_js"],{

/***/ "(pages-dir-browser)/./node_modules/next/dist/pages/_error.js":
/*!************************************************!*\
  !*** ./node_modules/next/dist/pages/_error.js ***!
  \************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return Error;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst _head = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../shared/lib/head */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/head.js\"));\nconst statusCodes = {\n    400: 'Bad Request',\n    404: 'This page could not be found',\n    405: 'Method Not Allowed',\n    500: 'Internal Server Error'\n};\nfunction _getInitialProps(param) {\n    let { req, res, err } = param;\n    const statusCode = res && res.statusCode ? res.statusCode : err ? err.statusCode : 404;\n    let hostname;\n    if (true) {\n        hostname = window.location.hostname;\n    } else {}\n    return {\n        statusCode,\n        hostname\n    };\n}\nconst styles = {\n    error: {\n        // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n        fontFamily: 'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n        height: '100vh',\n        textAlign: 'center',\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center'\n    },\n    desc: {\n        lineHeight: '48px'\n    },\n    h1: {\n        display: 'inline-block',\n        margin: '0 20px 0 0',\n        paddingRight: 23,\n        fontSize: 24,\n        fontWeight: 500,\n        verticalAlign: 'top'\n    },\n    h2: {\n        fontSize: 14,\n        fontWeight: 400,\n        lineHeight: '28px'\n    },\n    wrap: {\n        display: 'inline-block'\n    }\n};\nclass Error extends _react.default.Component {\n    render() {\n        const { statusCode, withDarkMode = true } = this.props;\n        const title = this.props.title || statusCodes[statusCode] || 'An unexpected error has occurred';\n        return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n            style: styles.error,\n            children: [\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(_head.default, {\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"title\", {\n                        children: statusCode ? statusCode + \": \" + title : 'Application error: a client-side exception has occurred'\n                    })\n                }),\n                /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                    style: styles.desc,\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n                            dangerouslySetInnerHTML: {\n                                /* CSS minified from\n                body { margin: 0; color: #000; background: #fff; }\n                .next-error-h1 {\n                  border-right: 1px solid rgba(0, 0, 0, .3);\n                }\n\n                ${\n                  withDarkMode\n                    ? `@media (prefers-color-scheme: dark) {\n                  body { color: #fff; background: #000; }\n                  .next-error-h1 {\n                    border-right: 1px solid rgba(255, 255, 255, .3);\n                  }\n                }`\n                    : ''\n                }\n               */ __html: \"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}\" + (withDarkMode ? '@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}' : '')\n                            }\n                        }),\n                        statusCode ? /*#__PURE__*/ (0, _jsxruntime.jsx)(\"h1\", {\n                            className: \"next-error-h1\",\n                            style: styles.h1,\n                            children: statusCode\n                        }) : null,\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                            style: styles.wrap,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"h2\", {\n                                style: styles.h2,\n                                children: [\n                                    this.props.title || statusCode ? title : /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                                        children: [\n                                            \"Application error: a client-side exception has occurred\",\n                                            ' ',\n                                            Boolean(this.props.hostname) && /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                                                children: [\n                                                    \"while loading \",\n                                                    this.props.hostname\n                                                ]\n                                            }),\n                                            ' ',\n                                            \"(see the browser console for more information)\"\n                                        ]\n                                    }),\n                                    \".\"\n                                ]\n                            })\n                        })\n                    ]\n                })\n            ]\n        });\n    }\n}\nError.displayName = 'ErrorPage';\nError.getInitialProps = _getInitialProps;\nError.origGetInitialProps = _getInitialProps;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=_error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/pages/_error.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js ***!
  \*************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"AmpStateContext\", ({\n    enumerable: true,\n    get: function() {\n        return AmpStateContext;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst AmpStateContext = _react.default.createContext({});\nif (true) {\n    AmpStateContext.displayName = 'AmpStateContext';\n} //# sourceMappingURL=amp-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9hbXAtY29udGV4dC5zaGFyZWQtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiOzs7O21EQUVhQTs7O2VBQUFBOzs7OzRFQUZLO0FBRVgsTUFBTUEsa0JBQXNDQyxPQUFBQSxPQUFLLENBQUNDLGFBQWEsQ0FBQyxDQUFDO0FBRXhFLElBQUlDLElBQW9CLEVBQW1CO0lBQ3pDSCxnQkFBZ0JNLFdBQVcsR0FBRztBQUNoQyIsInNvdXJjZXMiOlsiRDpcXHNyY1xcc2hhcmVkXFxsaWJcXGFtcC1jb250ZXh0LnNoYXJlZC1ydW50aW1lLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCdcblxuZXhwb3J0IGNvbnN0IEFtcFN0YXRlQ29udGV4dDogUmVhY3QuQ29udGV4dDxhbnk+ID0gUmVhY3QuY3JlYXRlQ29udGV4dCh7fSlcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgQW1wU3RhdGVDb250ZXh0LmRpc3BsYXlOYW1lID0gJ0FtcFN0YXRlQ29udGV4dCdcbn1cbiJdLCJuYW1lcyI6WyJBbXBTdGF0ZUNvbnRleHQiLCJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJwcm9jZXNzIiwiZW52IiwiTk9ERV9FTlYiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-mode.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/amp-mode.js ***!
  \*******************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"isInAmpMode\", ({\n    enumerable: true,\n    get: function() {\n        return isInAmpMode;\n    }\n}));\nfunction isInAmpMode(param) {\n    let { ampFirst = false, hybrid = false, hasQuery = false } = param === void 0 ? {} : param;\n    return ampFirst || hybrid && hasQuery;\n} //# sourceMappingURL=amp-mode.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9hbXAtbW9kZS5qcyIsIm1hcHBpbmdzIjoiOzs7OytDQUFnQkE7OztlQUFBQTs7O0FBQVQsU0FBU0EsWUFBWTtJQUFBLE1BQzFCQyxXQUFXLEtBQUssRUFDaEJDLFNBQVMsS0FBSyxFQUNkQyxXQUFXLEtBQUssRUFDakIsR0FKMkIsbUJBSXhCLENBQUMsSUFKdUI7SUFLMUIsT0FBT0YsWUFBYUMsVUFBVUM7QUFDaEMiLCJzb3VyY2VzIjpbIkQ6XFxzcmNcXHNoYXJlZFxcbGliXFxhbXAtbW9kZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gaXNJbkFtcE1vZGUoe1xuICBhbXBGaXJzdCA9IGZhbHNlLFxuICBoeWJyaWQgPSBmYWxzZSxcbiAgaGFzUXVlcnkgPSBmYWxzZSxcbn0gPSB7fSk6IGJvb2xlYW4ge1xuICByZXR1cm4gYW1wRmlyc3QgfHwgKGh5YnJpZCAmJiBoYXNRdWVyeSlcbn1cbiJdLCJuYW1lcyI6WyJpc0luQW1wTW9kZSIsImFtcEZpcnN0IiwiaHlicmlkIiwiaGFzUXVlcnkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-mode.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/head.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/head.js ***!
  \***************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return _default;\n    },\n    defaultHead: function() {\n        return defaultHead;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst _sideeffect = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./side-effect */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/side-effect.js\"));\nconst _ampcontextsharedruntime = __webpack_require__(/*! ./amp-context.shared-runtime */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js\");\nconst _headmanagercontextsharedruntime = __webpack_require__(/*! ./head-manager-context.shared-runtime */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\");\nconst _ampmode = __webpack_require__(/*! ./amp-mode */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/amp-mode.js\");\nconst _warnonce = __webpack_require__(/*! ./utils/warn-once */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nfunction defaultHead(inAmpMode) {\n    if (inAmpMode === void 0) inAmpMode = false;\n    const head = [\n        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n            charSet: \"utf-8\"\n        }, \"charset\")\n    ];\n    if (!inAmpMode) {\n        head.push(/*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n            name: \"viewport\",\n            content: \"width=device-width\"\n        }, \"viewport\"));\n    }\n    return head;\n}\nfunction onlyReactElement(list, child) {\n    // React children can be \"string\" or \"number\" in this case we ignore them for backwards compat\n    if (typeof child === 'string' || typeof child === 'number') {\n        return list;\n    }\n    // Adds support for React.Fragment\n    if (child.type === _react.default.Fragment) {\n        return list.concat(_react.default.Children.toArray(child.props.children).reduce((fragmentList, fragmentChild)=>{\n            if (typeof fragmentChild === 'string' || typeof fragmentChild === 'number') {\n                return fragmentList;\n            }\n            return fragmentList.concat(fragmentChild);\n        }, []));\n    }\n    return list.concat(child);\n}\nconst METATYPES = [\n    'name',\n    'httpEquiv',\n    'charSet',\n    'itemProp'\n];\n/*\n returns a function for filtering head child elements\n which shouldn't be duplicated, like <title/>\n Also adds support for deduplicated `key` properties\n*/ function unique() {\n    const keys = new Set();\n    const tags = new Set();\n    const metaTypes = new Set();\n    const metaCategories = {};\n    return (h)=>{\n        let isUnique = true;\n        let hasKey = false;\n        if (h.key && typeof h.key !== 'number' && h.key.indexOf('$') > 0) {\n            hasKey = true;\n            const key = h.key.slice(h.key.indexOf('$') + 1);\n            if (keys.has(key)) {\n                isUnique = false;\n            } else {\n                keys.add(key);\n            }\n        }\n        // eslint-disable-next-line default-case\n        switch(h.type){\n            case 'title':\n            case 'base':\n                if (tags.has(h.type)) {\n                    isUnique = false;\n                } else {\n                    tags.add(h.type);\n                }\n                break;\n            case 'meta':\n                for(let i = 0, len = METATYPES.length; i < len; i++){\n                    const metatype = METATYPES[i];\n                    if (!h.props.hasOwnProperty(metatype)) continue;\n                    if (metatype === 'charSet') {\n                        if (metaTypes.has(metatype)) {\n                            isUnique = false;\n                        } else {\n                            metaTypes.add(metatype);\n                        }\n                    } else {\n                        const category = h.props[metatype];\n                        const categories = metaCategories[metatype] || new Set();\n                        if ((metatype !== 'name' || !hasKey) && categories.has(category)) {\n                            isUnique = false;\n                        } else {\n                            categories.add(category);\n                            metaCategories[metatype] = categories;\n                        }\n                    }\n                }\n                break;\n        }\n        return isUnique;\n    };\n}\n/**\n *\n * @param headChildrenElements List of children of <Head>\n */ function reduceComponents(headChildrenElements, props) {\n    const { inAmpMode } = props;\n    return headChildrenElements.reduce(onlyReactElement, []).reverse().concat(defaultHead(inAmpMode).reverse()).filter(unique()).reverse().map((c, i)=>{\n        const key = c.key || i;\n        if (false) {}\n        if (true) {\n            // omit JSON-LD structured data snippets from the warning\n            if (c.type === 'script' && c.props['type'] !== 'application/ld+json') {\n                const srcMessage = c.props['src'] ? '<script> tag with src=\"' + c.props['src'] + '\"' : \"inline <script>\";\n                (0, _warnonce.warnOnce)(\"Do not add <script> tags using next/head (see \" + srcMessage + \"). Use next/script instead. \\nSee more info here: https://nextjs.org/docs/messages/no-script-tags-in-head-component\");\n            } else if (c.type === 'link' && c.props['rel'] === 'stylesheet') {\n                (0, _warnonce.warnOnce)('Do not add stylesheets using next/head (see <link rel=\"stylesheet\"> tag with href=\"' + c.props['href'] + '\"). Use Document instead. \\nSee more info here: https://nextjs.org/docs/messages/no-stylesheets-in-head-component');\n            }\n        }\n        return /*#__PURE__*/ _react.default.cloneElement(c, {\n            key\n        });\n    });\n}\n/**\n * This component injects elements to `<head>` of your page.\n * To avoid duplicated `tags` in `<head>` you can use the `key` property, which will make sure every tag is only rendered once.\n */ function Head(param) {\n    let { children } = param;\n    const ampState = (0, _react.useContext)(_ampcontextsharedruntime.AmpStateContext);\n    const headManager = (0, _react.useContext)(_headmanagercontextsharedruntime.HeadManagerContext);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_sideeffect.default, {\n        reduceComponentsToState: reduceComponents,\n        headManager: headManager,\n        inAmpMode: (0, _ampmode.isInAmpMode)(ampState),\n        children: children\n    });\n}\n_c = Head;\nconst _default = Head;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=head.js.map\nvar _c;\n$RefreshReg$(_c, \"Head\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/head.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/side-effect.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/side-effect.js ***!
  \**********************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return SideEffect;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nconst isServer = \"object\" === 'undefined';\nconst useClientOnlyLayoutEffect = isServer ? ()=>{} : _react.useLayoutEffect;\nconst useClientOnlyEffect = isServer ? ()=>{} : _react.useEffect;\nfunction SideEffect(props) {\n    _s();\n    const { headManager, reduceComponentsToState } = props;\n    function emitChange() {\n        if (headManager && headManager.mountedInstances) {\n            const headElements = _react.Children.toArray(Array.from(headManager.mountedInstances).filter(Boolean));\n            headManager.updateHead(reduceComponentsToState(headElements, props));\n        }\n    }\n    if (isServer) {\n        var _headManager_mountedInstances;\n        headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.add(props.children);\n        emitChange();\n    }\n    useClientOnlyLayoutEffect({\n        \"SideEffect.useClientOnlyLayoutEffect\": ()=>{\n            var _headManager_mountedInstances;\n            headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.add(props.children);\n            return ({\n                \"SideEffect.useClientOnlyLayoutEffect\": ()=>{\n                    var _headManager_mountedInstances;\n                    headManager == null ? void 0 : (_headManager_mountedInstances = headManager.mountedInstances) == null ? void 0 : _headManager_mountedInstances.delete(props.children);\n                }\n            })[\"SideEffect.useClientOnlyLayoutEffect\"];\n        }\n    }[\"SideEffect.useClientOnlyLayoutEffect\"]);\n    // We need to call `updateHead` method whenever the `SideEffect` is trigger in all\n    // life-cycles: mount, update, unmount. However, if there are multiple `SideEffect`s\n    // being rendered, we only trigger the method from the last one.\n    // This is ensured by keeping the last unflushed `updateHead` in the `_pendingUpdate`\n    // singleton in the layout effect pass, and actually trigger it in the effect pass.\n    useClientOnlyLayoutEffect({\n        \"SideEffect.useClientOnlyLayoutEffect\": ()=>{\n            if (headManager) {\n                headManager._pendingUpdate = emitChange;\n            }\n            return ({\n                \"SideEffect.useClientOnlyLayoutEffect\": ()=>{\n                    if (headManager) {\n                        headManager._pendingUpdate = emitChange;\n                    }\n                }\n            })[\"SideEffect.useClientOnlyLayoutEffect\"];\n        }\n    }[\"SideEffect.useClientOnlyLayoutEffect\"]);\n    useClientOnlyEffect({\n        \"SideEffect.useClientOnlyEffect\": ()=>{\n            if (headManager && headManager._pendingUpdate) {\n                headManager._pendingUpdate();\n                headManager._pendingUpdate = null;\n            }\n            return ({\n                \"SideEffect.useClientOnlyEffect\": ()=>{\n                    if (headManager && headManager._pendingUpdate) {\n                        headManager._pendingUpdate();\n                        headManager._pendingUpdate = null;\n                    }\n                }\n            })[\"SideEffect.useClientOnlyEffect\"];\n        }\n    }[\"SideEffect.useClientOnlyEffect\"]);\n    return null;\n} //# sourceMappingURL=side-effect.js.map\n_s(SideEffect, \"gHVkikNHNxjVdD11eJBzaqkCiPY=\", false, function() {\n    return [\n        useClientOnlyLayoutEffect,\n        useClientOnlyLayoutEffect,\n        useClientOnlyEffect\n    ];\n});\n_c = SideEffect;\nvar _c;\n$RefreshReg$(_c, \"SideEffect\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/side-effect.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js":
/*!**************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils/warn-once.js ***!
  \**************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"warnOnce\", ({\n    enumerable: true,\n    get: function() {\n        return warnOnce;\n    }\n}));\nlet warnOnce = (_)=>{};\nif (true) {\n    const warnings = new Set();\n    warnOnce = (msg)=>{\n        if (!warnings.has(msg)) {\n            console.warn(msg);\n        }\n        warnings.add(msg);\n    };\n} //# sourceMappingURL=warn-once.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi91dGlscy93YXJuLW9uY2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs0Q0FXU0E7OztlQUFBQTs7O0FBWFQsSUFBSUEsV0FBVyxDQUFDQyxLQUFlO0FBQy9CLElBQUlDLElBQW9CLEVBQW1CO0lBQ3pDLE1BQU1HLFdBQVcsSUFBSUM7SUFDckJOLFdBQVcsQ0FBQ087UUFDVixJQUFJLENBQUNGLFNBQVNHLEdBQUcsQ0FBQ0QsTUFBTTtZQUN0QkUsUUFBUUMsSUFBSSxDQUFDSDtRQUNmO1FBQ0FGLFNBQVNNLEdBQUcsQ0FBQ0o7SUFDZjtBQUNGIiwic291cmNlcyI6WyJEOlxcc3JjXFxzaGFyZWRcXGxpYlxcdXRpbHNcXHdhcm4tb25jZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJsZXQgd2Fybk9uY2UgPSAoXzogc3RyaW5nKSA9PiB7fVxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgY29uc3Qgd2FybmluZ3MgPSBuZXcgU2V0PHN0cmluZz4oKVxuICB3YXJuT25jZSA9IChtc2c6IHN0cmluZykgPT4ge1xuICAgIGlmICghd2FybmluZ3MuaGFzKG1zZykpIHtcbiAgICAgIGNvbnNvbGUud2Fybihtc2cpXG4gICAgfVxuICAgIHdhcm5pbmdzLmFkZChtc2cpXG4gIH1cbn1cblxuZXhwb3J0IHsgd2Fybk9uY2UgfVxuIl0sIm5hbWVzIjpbIndhcm5PbmNlIiwiXyIsInByb2Nlc3MiLCJlbnYiLCJOT0RFX0VOViIsIndhcm5pbmdzIiwiU2V0IiwibXNnIiwiaGFzIiwiY29uc29sZSIsIndhcm4iLCJhZGQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\n"));

/***/ })

}]);