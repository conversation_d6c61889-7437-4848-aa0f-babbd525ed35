"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { LogOut, User, Clock, QrCode, Home, RefreshCw, Shield, Copy, Check, CheckCircle } from "lucide-react"
import { dbStore, type Student, type EntryLog } from "@/lib/database-store"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export default function StudentApp() {
  const [currentStudent, setCurrentStudent] = useState<Student | null>(null)
  const [studentEntries, setStudentEntries] = useState<EntryLog[]>([])
  const [showQRCard, setShowQRCard] = useState(false)
  const [loading, setLoading] = useState(false)
  const [copiedQR, setCopiedQR] = useState(false)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const router = useRouter()

  useEffect(() => {
    // Check if student is logged in
    if (typeof window !== "undefined") {
      const studentLoggedIn = localStorage.getItem("studentLoggedIn")
      const studentId = localStorage.getItem("studentId")

      if (!studentLoggedIn || !studentId) {
        router.push("/")
        return
      }

      // Clear all entry data on student app start
      localStorage.removeItem("entries")
      console.log("🧹 Student App: Cleared all previous entry data")

      loadStudentData(studentId)

      // Set up real-time polling for entry updates
      const interval = setInterval(() => {
        loadStudentData(studentId)
        console.log("🔄 Student App: Auto-refreshing entry history...")
      }, 5000) // Refresh every 5 seconds

      return () => clearInterval(interval)
    }
  }, [router])

  const loadStudentData = async (studentId: string) => {
    try {
      setLoading(true)
      setIsAuthenticated(true)

      // Get all students and find the current one
      const students = await dbStore.getStudents()
      const student = students.find((s) => s.id === studentId)

      if (student) {
        setCurrentStudent(student)
        const entries = await dbStore.getStudentEntries(student.id)
        setStudentEntries(entries)
      } else {
        // Student not found, logout
        handleLogout()
      }
    } catch (error) {
      console.error("Error loading student data:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleLogout = () => {
    if (typeof window !== "undefined") {
      localStorage.removeItem("studentLoggedIn")
      localStorage.removeItem("studentId")
      localStorage.removeItem("studentAppNumber")
    }
    router.push("/")
  }

  const handleRefresh = () => {
    if (currentStudent) {
      loadStudentData(currentStudent.id)
    }
  }

  // Simple QR Code - Only Application Number
  const generateSimpleQRCode = () => {
    if (!currentStudent) return ""
    return currentStudent.application_number
  }

  const copyQRData = async () => {
    try {
      const qrData = generateSimpleQRCode()
      await navigator.clipboard.writeText(qrData)
      setCopiedQR(true)
      setTimeout(() => setCopiedQR(false), 2000)
    } catch (error) {
      alert("Failed to copy QR data")
    }
  }

  const formatDateTime = (date: Date) => {
    return date.toLocaleString("en-IN", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const formatTime = (date: Date) => {
    return date.toLocaleString("en-IN", {
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const formatDate = (date: Date) => {
    return date.toLocaleString("en-IN", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  if (!isAuthenticated || !currentStudent) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading student data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <img
                  src={currentStudent?.image_url || "/placeholder.svg?height=50&width=50"}
                  alt={currentStudent?.name}
                  className="w-12 h-12 rounded-full border-2 border-green-200 object-cover"
                />
                <div>
                  <CardTitle>Welcome, {currentStudent?.name}</CardTitle>
                  <CardDescription>App No: {currentStudent?.application_number}</CardDescription>
                </div>
              </div>
              <div className="flex gap-2">
                <div className="flex items-center gap-2 px-3 py-2 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                  <span className="text-xs text-blue-700 font-medium">Live Updates (5s)</span>
                </div>
                <Button onClick={handleRefresh} variant="outline" disabled={loading}>
                  <RefreshCw className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`} />
                  Refresh
                </Button>
                <Button onClick={handleLogout} variant="outline">
                  <LogOut className="mr-2 h-4 w-4" />
                  Logout
                </Button>
                <Button onClick={() => router.push("/")} variant="outline">
                  <Home className="mr-2 h-4 w-4" />
                  Home
                </Button>
              </div>
            </div>
          </CardHeader>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Student Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Personal Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-4">
                <img
                  src={currentStudent?.image_url || "/placeholder.svg?height=80&width=80"}
                  alt={currentStudent?.name}
                  className="w-20 h-20 rounded-full border-4 border-green-200 object-cover"
                />
                <div className="space-y-1">
                  <h3 className="text-xl font-semibold">{currentStudent?.name}</h3>
                  <Badge variant="secondary">{currentStudent?.class}</Badge>
                  <p className="text-sm text-gray-600">{currentStudent?.department}</p>
                </div>
              </div>

              <Separator />

              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="font-medium text-gray-700">Phone:</p>
                    <p className="text-gray-600">{currentStudent?.phone}</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-700">Email:</p>
                    <p className="text-gray-600">{currentStudent?.email || "Not provided"}</p>
                  </div>
                </div>

                <div>
                  <p className="font-medium text-gray-700">Time Schedule:</p>
                  <p className="text-gray-600">{currentStudent?.schedule || "Not assigned"}</p>
                </div>

                <div>
                  <p className="font-medium text-gray-700">Application Number:</p>
                  <Badge variant="outline" className="font-mono">
                    {currentStudent?.application_number}
                  </Badge>
                </div>
              </div>

              <Separator />

              <Button
                onClick={() => setShowQRCard(!showQRCard)}
                className="w-full"
                variant={showQRCard ? "secondary" : "default"}
              >
                <QrCode className="mr-2 h-4 w-4" />
                {showQRCard ? "Hide QR ID Card" : "Show QR ID Card"}
              </Button>
            </CardContent>
          </Card>

          {/* Enhanced Entry History */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Entry/Exit History
              </CardTitle>
              <CardDescription>
                Complete verification history with QR scan and face verification details
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {studentEntries.length === 0 ? (
                  <div className="text-center py-8">
                    <Clock className="h-12 w-12 mx-auto text-gray-300 mb-2" />
                    <p className="text-gray-500">No entries recorded yet</p>
                    <p className="text-sm text-gray-400">Go to ID Card Station to scan your QR code</p>
                    <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                      <p className="text-sm text-blue-700">
                        <strong>How it works:</strong>
                      </p>
                      <ul className="text-xs text-blue-600 mt-1 space-y-1">
                        <li>• Scan your QR code at the station</li>
                        <li>• Complete face verification</li>
                        <li>• Entry/exit will be recorded automatically</li>
                        <li>• View your complete history here</li>
                      </ul>
                    </div>
                  </div>
                ) : (
                  studentEntries.map((entry) => (
                    <div key={entry.id} className="border border-gray-200 rounded-lg p-4 bg-white shadow-sm">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <span className="text-lg">
                              {entry.status === "entry" ? "🟢" : "🔴"}
                            </span>
                            <p className="font-semibold capitalize text-lg">
                              {entry.status === "entry" ? "Entry" : "Exit"}
                            </p>
                            <Badge variant={entry.status === "entry" ? "default" : "secondary"}>
                              {entry.verified ? "✅ Verified" : "⚠️ Pending"}
                            </Badge>
                          </div>

                          <div className="grid grid-cols-2 gap-3 text-sm">
                            <div>
                              <p className="text-gray-500">Date</p>
                              <p className="font-medium">{formatDate(entry.entryTime)}</p>
                            </div>
                            <div>
                              <p className="text-gray-500">Time</p>
                              <p className="font-medium">{formatTime(entry.entryTime)}</p>
                            </div>
                          </div>

                          {entry.exitTime && (
                            <div className="mt-2 pt-2 border-t border-gray-100">
                              <div className="grid grid-cols-2 gap-3 text-sm">
                                <div>
                                  <p className="text-gray-500">Exit Date</p>
                                  <p className="font-medium">{formatDate(entry.exitTime)}</p>
                                </div>
                                <div>
                                  <p className="text-gray-500">Exit Time</p>
                                  <p className="font-medium">{formatTime(entry.exitTime)}</p>
                                </div>
                              </div>
                            </div>
                          )}

                          <div className="mt-3 pt-2 border-t border-gray-100">
                            <div className="flex items-center gap-4 text-xs text-gray-500">
                              <span className="flex items-center gap-1">
                                <QrCode className="h-3 w-3" />
                                QR Verified
                              </span>
                              <span className="flex items-center gap-1">
                                <User className="h-3 w-3" />
                                Face Verified
                              </span>
                              <span className="flex items-center gap-1">
                                <CheckCircle className="h-3 w-3" />
                                Auto Recorded
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>

              {studentEntries.length > 0 && (
                <div className="mt-4 pt-3 border-t border-gray-200">
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <span>Total Entries: {studentEntries.length}</span>
                    <span>Last Updated: {new Date().toLocaleTimeString()}</span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Simple QR Code ID Card */}
        {showQRCard && (
          <Card className="border-2 border-blue-300 bg-gradient-to-r from-blue-500 to-purple-600 text-white">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-white">
                <Shield className="h-5 w-5" />
                Digital QR ID Card
              </CardTitle>
              <CardDescription className="text-blue-100">
                Simple QR code with Application Number - Scan at ID Card Station
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-4">
                <img
                  src={currentStudent?.image_url || "/placeholder.svg?height=64&width=64"}
                  alt={currentStudent?.name}
                  className="w-16 h-16 rounded-full border-4 border-white object-cover"
                />
                <div className="space-y-1">
                  <h3 className="text-xl font-bold text-white">{currentStudent?.name}</h3>
                  <p className="text-blue-100">
                    {currentStudent?.class} - {currentStudent?.department}
                  </p>
                  <Badge variant="secondary" className="text-xs">
                    Application Number Based
                  </Badge>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="font-medium text-blue-100">Application No:</p>
                  <p className="text-white font-mono text-lg">{currentStudent?.application_number}</p>
                </div>
                <div>
                  <p className="font-medium text-blue-100">Phone:</p>
                  <p className="text-white">{currentStudent?.phone}</p>
                </div>
              </div>

              {/* Simple QR Code Display */}
              <div className="text-center py-4">
                <div className="inline-block bg-white p-4 rounded-lg">
                  <img
                    src={`https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(generateSimpleQRCode())}`}
                    alt="Student Application Number QR Code"
                    className="w-48 h-48 mx-auto"
                  />
                </div>
                <div className="mt-2">
                  <Badge variant="secondary" className="text-xs">
                    Application Number: {currentStudent?.application_number}
                  </Badge>
                </div>
              </div>

              {/* QR Data for Manual Input */}
              <div className="space-y-2">
                <Label className="text-blue-100">QR Code Data (Application Number):</Label>
                <div className="relative">
                  <Input
                    value={generateSimpleQRCode()}
                    readOnly
                    className="bg-white/10 border-white/20 text-white placeholder-white/50 text-center font-mono text-lg"
                    placeholder="Application number will appear here..."
                  />
                  <Button
                    onClick={copyQRData}
                    size="sm"
                    variant="outline"
                    className="absolute top-2 right-2 h-6 w-6 p-0 bg-white/20 border-white/30 text-white hover:bg-white/30"
                  >
                    {copiedQR ? <Check className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
                  </Button>
                </div>
                <p className="text-xs text-blue-200">
                  Copy this Application Number and paste in "Manual QR Input" at the station if camera scanning doesn't
                  work
                </p>
              </div>

              <Alert className="border-yellow-200 bg-yellow-50/10 border-opacity-30">
                <Shield className="h-4 w-4 text-yellow-200" />
                <AlertDescription className="text-yellow-100">
                  <strong>How it works:</strong>
                  <ul className="list-disc list-inside text-xs mt-1 space-y-1">
                    <li>QR code contains your Application Number</li>
                    <li>Station scans and finds your details from admin database</li>
                    <li>Face verification with your stored photo</li>
                    <li>Entry/exit automatically recorded</li>
                  </ul>
                </AlertDescription>
              </Alert>

              <div className="text-center">
                <p className="text-xs text-blue-100">📱 Show this QR code at ID Card Station</p>
                <p className="text-xs text-blue-100 mt-1">Station will find your details using Application Number</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>How to Use QR Code at Station</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-green-700 mb-2">QR Code Scanning:</h3>
                <ol className="list-decimal list-inside space-y-1 text-sm">
                  <li>Show your QR code to station operator</li>
                  <li>Operator will start QR scanner camera</li>
                  <li>Hold QR code steady in front of camera</li>
                  <li>Scanner reads your Application Number</li>
                  <li>Station finds your details from admin database</li>
                  <li>Proceed to face verification</li>
                </ol>
              </div>
              <div>
                <h3 className="font-semibold text-blue-700 mb-2">Manual Input Option:</h3>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Copy your Application Number from above</li>
                  <li>Go to station's "Manual QR Input"</li>
                  <li>Paste your Application Number</li>
                  <li>Click "Validate" button</li>
                  <li>Station will find your details</li>
                  <li>Continue with face verification</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
