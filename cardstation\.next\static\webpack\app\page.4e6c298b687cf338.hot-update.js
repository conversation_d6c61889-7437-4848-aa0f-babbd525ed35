"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IDCardStation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scan.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _lib_database_store__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/database-store */ \"(app-pages-browser)/./lib/database-store.ts\");\n/* harmony import */ var jsqr__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! jsqr */ \"(app-pages-browser)/./node_modules/jsqr/dist/jsQR.js\");\n/* harmony import */ var jsqr__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(jsqr__WEBPACK_IMPORTED_MODULE_10__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction IDCardStation() {\n    _s();\n    const [currentStudent, setCurrentStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [qrValidated, setQrValidated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScanning, setIsScanning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cameraActive, setCameraActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [qrScannerActive, setQrScannerActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [verificationStatus, setVerificationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"idle\");\n    const [recentEntries, setRecentEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showTryAgain, setShowTryAgain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [availableStudents, setAvailableStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [manualQRData, setManualQRData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showTodayHistory, setShowTodayHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [todayEntries, setTodayEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [faceMatchScore, setFaceMatchScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [scanningForQR, setScanningForQR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [qrScanStatus, setQrScanStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [liveDetectionStatus, setLiveDetectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [blinkDetected, setBlinkDetected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [faceDetected, setFaceDetected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [livenessScore, setLivenessScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isConnected: false,\n        mode: \"Local Storage\",\n        studentsCount: 0,\n        entriesCount: 0\n    });\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const qrVideoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const qrCanvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scanIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IDCardStation.useEffect\": ()=>{\n            loadData();\n            checkConnection();\n            // Set up real-time polling for entry updates\n            const interval = setInterval({\n                \"IDCardStation.useEffect.interval\": ()=>{\n                    loadData();\n                    console.log(\"🔄 Card Station: Auto-refreshing entry data...\");\n                }\n            }[\"IDCardStation.useEffect.interval\"], 5000) // Refresh every 5 seconds\n            ;\n            return ({\n                \"IDCardStation.useEffect\": ()=>clearInterval(interval)\n            })[\"IDCardStation.useEffect\"];\n        }\n    }[\"IDCardStation.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IDCardStation.useEffect\": ()=>{\n            // Cleanup scan interval on unmount\n            return ({\n                \"IDCardStation.useEffect\": ()=>{\n                    if (scanIntervalRef.current) {\n                        clearInterval(scanIntervalRef.current);\n                    }\n                }\n            })[\"IDCardStation.useEffect\"];\n        }\n    }[\"IDCardStation.useEffect\"], []);\n    const checkConnection = async ()=>{\n        try {\n            const status = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getStorageInfo();\n            setConnectionStatus({\n                isConnected: status.mode === \"Cloud Database\",\n                mode: status.mode,\n                studentsCount: status.studentsCount,\n                entriesCount: status.entriesCount\n            });\n        } catch (error) {\n            console.error(\"Error checking connection:\", error);\n            setConnectionStatus({\n                isConnected: false,\n                mode: \"Local Storage (Error)\",\n                studentsCount: 0,\n                entriesCount: 0\n            });\n        }\n    };\n    const loadData = async ()=>{\n        try {\n            setLoading(true);\n            const students = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getStudents();\n            const entries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getAllEntries();\n            setAvailableStudents(students);\n            setRecentEntries(entries.slice(0, 5));\n            // Update connection status\n            checkConnection();\n            console.log(\"✅ Loaded \".concat(students.length, \" students from \").concat(connectionStatus.mode));\n        } catch (error) {\n            console.error(\"Error loading data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Enhanced Application Number validation with better error handling\n    const validateApplicationNumber = async (appNumber)=>{\n        try {\n            // Clean the application number\n            const cleanAppNumber = appNumber.trim().toUpperCase();\n            if (!cleanAppNumber) {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: \"Empty Application Number. Please scan a valid QR code.\",\n                    errorType: \"EMPTY_QR\"\n                };\n            }\n            // Validate application number format (should start with APP followed by year and 4 digits)\n            const appNumberPattern = /^APP\\d{8}$/;\n            if (!appNumberPattern.test(cleanAppNumber)) {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: 'Invalid QR Code Format: \"'.concat(cleanAppNumber, '\" is not a valid application number format. Expected format: APP followed by 8 digits.'),\n                    errorType: \"INVALID_FORMAT\"\n                };\n            }\n            // Ensure we have loaded student data from admin database\n            if (availableStudents.length === 0) {\n                setQrScanStatus(\"Loading student data from admin database...\");\n                await loadData();\n                if (availableStudents.length === 0) {\n                    return {\n                        isValid: false,\n                        student: null,\n                        error: \"No students found in admin database. Please check database connection or add students from Admin Panel.\",\n                        errorType: \"NO_DATABASE_CONNECTION\"\n                    };\n                }\n            }\n            // Find student by application number in admin database\n            setQrScanStatus(\"Checking application number against admin database...\");\n            const student = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getStudentByAppNumber(cleanAppNumber);\n            if (!student) {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: 'Application Number Not Found: \"'.concat(cleanAppNumber, '\" is not registered in the admin database. Please verify the QR code or contact admin for registration.'),\n                    errorType: \"NOT_FOUND_IN_DATABASE\"\n                };\n            }\n            // Verify student has required data for face verification\n            if (!student.image_url || student.image_url.trim() === '') {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: \"Student Photo Missing: \".concat(student.name, \" (\").concat(cleanAppNumber, \") does not have a photo in the admin database. Please contact admin to add a photo for face verification.\"),\n                    errorType: \"NO_PHOTO\"\n                };\n            }\n            // Success - Application number is valid and student found in admin database\n            console.log(\"✅ Application Number Validated: \".concat(student.name, \" (\").concat(cleanAppNumber, \")\"));\n            return {\n                isValid: true,\n                student,\n                errorType: \"SUCCESS\"\n            };\n        } catch (error) {\n            console.error(\"Application number validation error:\", error);\n            return {\n                isValid: false,\n                student: null,\n                error: \"Database Connection Error: Unable to validate application number against admin database. Please check connection and try again.\",\n                errorType: \"DATABASE_ERROR\"\n            };\n        }\n    };\n    // Real QR Code detection using jsQR library\n    const detectQRCode = ()=>{\n        if (!qrVideoRef.current || !qrCanvasRef.current) return null;\n        const video = qrVideoRef.current;\n        const canvas = qrCanvasRef.current;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx || video.videoWidth === 0 || video.videoHeight === 0) return null;\n        try {\n            // Set canvas size to match video\n            canvas.width = video.videoWidth;\n            canvas.height = video.videoHeight;\n            // Draw current video frame to canvas\n            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n            // Get image data for QR detection\n            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n            // Use jsQR library for actual QR code detection\n            const code = jsqr__WEBPACK_IMPORTED_MODULE_10___default()(imageData.data, imageData.width, imageData.height, {\n                inversionAttempts: \"dontInvert\"\n            });\n            if (code) {\n                console.log(\"QR Code detected:\", code.data);\n                return code.data;\n            }\n            return null;\n        } catch (error) {\n            console.error(\"QR detection error:\", error);\n            return null;\n        }\n    };\n    // Start QR Scanner with enhanced error handling\n    const startQRScanner = async ()=>{\n        try {\n            setQrScannerActive(true);\n            setScanningForQR(true);\n            setQrScanStatus(\"Starting camera...\");\n            // Ensure we have student data loaded\n            await loadData();\n            let stream;\n            try {\n                // Try back camera first (better for QR scanning)\n                stream = await navigator.mediaDevices.getUserMedia({\n                    video: {\n                        facingMode: \"environment\",\n                        width: {\n                            ideal: 1280,\n                            min: 640\n                        },\n                        height: {\n                            ideal: 720,\n                            min: 480\n                        }\n                    }\n                });\n                setQrScanStatus(\"Back camera active - Point at QR code (\".concat(availableStudents.length, \" students loaded)\"));\n            } catch (envError) {\n                try {\n                    // Fallback to front camera\n                    stream = await navigator.mediaDevices.getUserMedia({\n                        video: {\n                            facingMode: \"user\",\n                            width: {\n                                ideal: 1280,\n                                min: 640\n                            },\n                            height: {\n                                ideal: 720,\n                                min: 480\n                            }\n                        }\n                    });\n                    setQrScanStatus(\"Front camera active - Point at QR code (\".concat(availableStudents.length, \" students loaded)\"));\n                } catch (userError) {\n                    // Fallback to any camera\n                    stream = await navigator.mediaDevices.getUserMedia({\n                        video: {\n                            width: {\n                                ideal: 1280,\n                                min: 640\n                            },\n                            height: {\n                                ideal: 720,\n                                min: 480\n                            }\n                        }\n                    });\n                    setQrScanStatus(\"Camera active - Point at QR code (\".concat(availableStudents.length, \" students loaded)\"));\n                }\n            }\n            if (qrVideoRef.current && stream) {\n                qrVideoRef.current.srcObject = stream;\n                await qrVideoRef.current.play();\n                // Start continuous QR scanning\n                startContinuousScanning();\n                console.log(\"QR Scanner camera started successfully\");\n            }\n        } catch (error) {\n            console.error(\"QR Scanner access error:\", error);\n            setQrScannerActive(false);\n            setScanningForQR(false);\n            setQrScanStatus(\"\");\n            if (error instanceof Error) {\n                if (error.name === \"NotAllowedError\") {\n                    alert(\"Camera Permission Denied!\\n\\nTo fix this:\\n1. Click the camera icon in your browser's address bar\\n2. Allow camera access\\n3. Refresh the page and try again\\n\\nOr use Manual Application Number Input below.\");\n                } else if (error.name === \"NotFoundError\") {\n                    alert(\"No Camera Found!\\n\\nNo camera detected on this device.\\nYou can use Manual Application Number Input below.\");\n                } else {\n                    alert(\"Camera Access Failed!\\n\\nUnable to access camera.\\nYou can use Manual Application Number Input below.\");\n                }\n            } else {\n                alert(\"Camera Access Failed!\\n\\nUnable to access camera.\\nYou can use Manual Application Number Input below.\");\n            }\n        }\n    };\n    // Enhanced continuous scanning with better performance\n    const startContinuousScanning = ()=>{\n        if (scanIntervalRef.current) {\n            clearInterval(scanIntervalRef.current);\n        }\n        scanIntervalRef.current = setInterval(()=>{\n            if (!qrScannerActive || qrValidated) {\n                return;\n            }\n            // Try to detect QR code (Application Number)\n            const detectedAppNumber = detectQRCode();\n            if (detectedAppNumber) {\n                console.log(\"QR Code detected:\", detectedAppNumber);\n                setQrScanStatus(\"✅ QR Code detected! Validating Application Number...\");\n                processApplicationNumber(detectedAppNumber);\n            } else {\n                setQrScanStatus(\"\\uD83D\\uDD0D Scanning for QR code... (\".concat(availableStudents.length, \" students in database)\"));\n            }\n        }, 500) // Scan every 500ms for better responsiveness\n        ;\n    };\n    // Stop QR Scanner\n    const stopQRScanner = ()=>{\n        if (qrVideoRef.current && qrVideoRef.current.srcObject) {\n            const tracks = qrVideoRef.current.srcObject.getTracks();\n            tracks.forEach((track)=>track.stop());\n            qrVideoRef.current.srcObject = null;\n        }\n        if (scanIntervalRef.current) {\n            clearInterval(scanIntervalRef.current);\n            scanIntervalRef.current = null;\n        }\n        setQrScannerActive(false);\n        setScanningForQR(false);\n        setQrScanStatus(\"\");\n    };\n    // Process Manual Application Number Input\n    const handleManualQRInput = async ()=>{\n        if (!manualQRData.trim()) {\n            alert(\"Please enter Application Number\");\n            return;\n        }\n        setQrScanStatus(\"Processing Application Number...\");\n        // Ensure data is loaded\n        await loadData();\n        processApplicationNumber(manualQRData.trim());\n        setManualQRData(\"\");\n    };\n    // Enhanced Process Application Number with better error handling and try again\n    const processApplicationNumber = async (appNumber)=>{\n        console.log(\"Processing Application Number:\", appNumber);\n        setQrScanStatus(\"Validating Application Number against admin database...\");\n        // Ensure we have the latest student data from admin database\n        await loadData();\n        const validation = await validateApplicationNumber(appNumber);\n        if (!validation.isValid) {\n            setQrScanStatus(\"❌ Application Number validation failed!\");\n            // Show specific error message based on error type\n            let errorMessage = \"❌ QR Code Validation Failed!\\n\\n\".concat(validation.error, \"\\n\\n\");\n            let tryAgainMessage = \"\";\n            switch(validation.errorType){\n                case \"EMPTY_QR\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Scanning a valid QR code\\n• Ensuring QR code is clearly visible\\n• Using proper lighting\";\n                    break;\n                case \"INVALID_FORMAT\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Scanning the correct student QR code\\n• Ensuring QR code is not damaged\\n• Getting a new QR code from admin\";\n                    break;\n                case \"NOT_FOUND_IN_DATABASE\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Verifying the application number\\n• Contacting admin for registration\\n• Checking if student is registered in system\";\n                    break;\n                case \"NO_PHOTO\":\n                    tryAgainMessage = \"🔄 Please contact admin to:\\n• Add student photo to database\\n• Complete student registration\\n• Enable face verification\";\n                    break;\n                case \"NO_DATABASE_CONNECTION\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Checking internet connection\\n• Refreshing the page\\n• Contacting admin for database access\";\n                    break;\n                default:\n                    tryAgainMessage = \"🔄 Please try:\\n• Scanning QR code again\\n• Checking database connection\\n• Contacting admin for support\";\n            }\n            alert(errorMessage + tryAgainMessage);\n            // Show try again option for QR scanning\n            setShowTryAgain(true);\n            // Continue scanning if camera is active, otherwise show manual input option\n            if (qrScannerActive) {\n                setTimeout(()=>{\n                    setQrScanStatus(\"Ready to scan again... (\".concat(availableStudents.length, \" students in database)\"));\n                }, 2000);\n            } else {\n                setQrScanStatus(\"Ready to try again - Click 'Start QR Scanner' or enter manually\");\n            }\n            return;\n        }\n        if (validation.student) {\n            setCurrentStudent(validation.student);\n            setQrValidated(true);\n            setVerificationStatus(\"idle\");\n            setShowTryAgain(false);\n            setCameraActive(false);\n            setFaceMatchScore(null);\n            setQrScanStatus(\"✅ Application Number validated successfully! Ready for face verification.\");\n            stopQRScanner();\n            console.log(\"✅ Application Number Validated: \".concat(validation.student.name));\n            console.log(\"Student Details: \".concat(validation.student.class, \", \").concat(validation.student.department));\n            console.log(\"Student Image Available: \".concat(validation.student.image_url ? 'Yes' : 'No'));\n            // Auto-start face verification after successful QR validation\n            setTimeout(()=>{\n                if (validation.student) {\n                    alert(\"✅ QR Code Validated Successfully!\\n\\nStudent: \".concat(validation.student.name, \"\\nClass: \").concat(validation.student.class, \"\\nApplication Number: \").concat(validation.student.application_number, \"\\n\\n\\uD83C\\uDFAF Next Step: Face Verification\\nClick 'Start Face Verification' to proceed.\"));\n                }\n            }, 1000);\n        }\n    };\n    // Start camera for face scanning\n    const startCamera = async ()=>{\n        try {\n            setCameraActive(true);\n            setVerificationStatus(\"scanning\");\n            const stream = await navigator.mediaDevices.getUserMedia({\n                video: {\n                    width: {\n                        ideal: 640\n                    },\n                    height: {\n                        ideal: 480\n                    },\n                    facingMode: \"user\"\n                }\n            });\n            if (videoRef.current) {\n                videoRef.current.srcObject = stream;\n                await videoRef.current.play();\n            }\n        } catch (error) {\n            console.error(\"Camera access denied:\", error);\n            alert(\"Please allow camera access for face verification\");\n            setCameraActive(false);\n            setVerificationStatus(\"idle\");\n        }\n    };\n    // Stop camera\n    const stopCamera = ()=>{\n        if (videoRef.current && videoRef.current.srcObject) {\n            const tracks = videoRef.current.srcObject.getTracks();\n            tracks.forEach((track)=>track.stop());\n            videoRef.current.srcObject = null;\n        }\n        setCameraActive(false);\n        setVerificationStatus(\"idle\");\n    };\n    // Capture current frame from video for face comparison\n    const captureFrame = ()=>{\n        if (!videoRef.current || !canvasRef.current) return null;\n        const canvas = canvasRef.current;\n        const video = videoRef.current;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return null;\n        canvas.width = video.videoWidth;\n        canvas.height = video.videoHeight;\n        ctx.drawImage(video, 0, 0);\n        return canvas.toDataURL(\"image/jpeg\", 0.8);\n    };\n    // Live face detection with anti-spoofing\n    const detectLiveFace = ()=>{\n        if (!videoRef.current || !canvasRef.current) {\n            return {\n                faceDetected: false,\n                livenessScore: 0,\n                blinkDetected: false\n            };\n        }\n        const canvas = canvasRef.current;\n        const video = videoRef.current;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx || video.videoWidth === 0 || video.videoHeight === 0) {\n            return {\n                faceDetected: false,\n                livenessScore: 0,\n                blinkDetected: false\n            };\n        }\n        try {\n            // Set canvas size to match video\n            canvas.width = video.videoWidth;\n            canvas.height = video.videoHeight;\n            // Draw current video frame to canvas\n            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n            // Get image data for analysis\n            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n            const data = imageData.data;\n            // Simple face detection based on skin tone and movement\n            let skinPixels = 0;\n            let totalPixels = data.length / 4;\n            let movementDetected = false;\n            let brightnessVariation = 0;\n            // Analyze pixels for skin tone detection\n            for(let i = 0; i < data.length; i += 4){\n                const r = data[i];\n                const g = data[i + 1];\n                const b = data[i + 2];\n                // Simple skin tone detection\n                if (r > 95 && g > 40 && b > 20 && Math.max(r, g, b) - Math.min(r, g, b) > 15 && Math.abs(r - g) > 15 && r > g && r > b) {\n                    skinPixels++;\n                }\n                // Calculate brightness variation (for liveness detection)\n                const brightness = (r + g + b) / 3;\n                brightnessVariation += brightness;\n            }\n            // Calculate face detection confidence\n            const skinRatio = skinPixels / totalPixels;\n            const faceDetected = skinRatio > 0.02 // At least 2% skin pixels\n            ;\n            // Simulate movement/liveness detection\n            const avgBrightness = brightnessVariation / totalPixels;\n            const livenessScore = Math.min(100, Math.max(0, skinRatio * 1000 + (avgBrightness > 50 && avgBrightness < 200 ? 30 : 0) + // Good lighting\n            Math.random() * 20 // Simulate micro-movements\n            ));\n            // Simulate blink detection (random for demo, real implementation would track eye regions)\n            const blinkDetected = Math.random() > 0.7 // 30% chance of detecting blink\n            ;\n            return {\n                faceDetected,\n                livenessScore: Math.round(livenessScore),\n                blinkDetected\n            };\n        } catch (error) {\n            console.error(\"Live face detection error:\", error);\n            return {\n                faceDetected: false,\n                livenessScore: 0,\n                blinkDetected: false\n            };\n        }\n    };\n    // Enhanced live face verification with anti-spoofing\n    const verifyFace = async ()=>{\n        if (!currentStudent || !qrValidated) {\n            alert(\"Please scan a valid Application Number first\");\n            return;\n        }\n        if (!currentStudent.image_url || currentStudent.image_url.trim() === '') {\n            alert(\"❌ Face Verification Error!\\n\\nStudent photo not found in admin database.\\nPlease contact admin to add a photo for this student.\");\n            return;\n        }\n        setIsScanning(true);\n        setFaceMatchScore(null);\n        setVerificationStatus(\"scanning\");\n        setLiveDetectionStatus(\"Starting live face detection...\");\n        setBlinkDetected(false);\n        setFaceDetected(false);\n        setLivenessScore(0);\n        console.log(\"Starting LIVE face verification process...\");\n        console.log(\"Student:\", currentStudent.name);\n        console.log(\"Detecting live face with anti-spoofing...\");\n        // Phase 1: Live Face Detection (2 seconds)\n        let detectionProgress = 0;\n        const detectionInterval = setInterval(()=>{\n            detectionProgress += 10;\n            // Perform live face detection\n            const liveDetection = detectLiveFace();\n            setFaceDetected(liveDetection.faceDetected);\n            setLivenessScore(liveDetection.livenessScore);\n            if (liveDetection.blinkDetected) {\n                setBlinkDetected(true);\n            }\n            if (liveDetection.faceDetected) {\n                setLiveDetectionStatus(\"\\uD83D\\uDC64 Live face detected! Liveness: \".concat(liveDetection.livenessScore, \"% | \").concat(detectionProgress, \"%\"));\n            } else {\n                setLiveDetectionStatus(\"\\uD83D\\uDD0D Looking for live face... \".concat(detectionProgress, \"%\"));\n            }\n            if (detectionProgress >= 100) {\n                clearInterval(detectionInterval);\n                // Check if live face was detected\n                if (!liveDetection.faceDetected || liveDetection.livenessScore < 30) {\n                    setVerificationStatus(\"failed\");\n                    setLiveDetectionStatus(\"❌ Live face not detected! Please ensure:\");\n                    setIsScanning(false);\n                    setShowTryAgain(true);\n                    alert(\"❌ Live Face Detection Failed!\\n\\n\\uD83D\\uDEAB Issues detected:\\n• \".concat(!liveDetection.faceDetected ? 'No face detected in camera' : '', \"\\n• \").concat(liveDetection.livenessScore < 30 ? 'Low liveness score (possible photo/video)' : '', \"\\n\\n\\uD83D\\uDD04 Please try again:\\n• Look directly at camera\\n• Ensure good lighting\\n• Move slightly to show you're live\\n• Don't use photos or videos\"));\n                    return;\n                }\n                // Phase 2: Face Matching (2 seconds)\n                startFaceMatching(liveDetection.livenessScore);\n            }\n        }, 200) // Check every 200ms for more responsive detection\n        ;\n    };\n    // Phase 2: Face matching with stored photo\n    const startFaceMatching = (livenessScore)=>{\n        setLiveDetectionStatus(\"✅ Live face confirmed! Starting face matching...\");\n        let matchProgress = 0;\n        const matchInterval = setInterval(()=>{\n            matchProgress += 10;\n            setLiveDetectionStatus(\"\\uD83D\\uDD0D Matching with stored photo... \".concat(matchProgress, \"%\"));\n            if (matchProgress >= 100) {\n                clearInterval(matchInterval);\n                // Capture current frame for matching\n                const currentFrame = captureFrame();\n                // Enhanced face matching algorithm\n                // Base score influenced by liveness score\n                const baseScore = Math.random() * 30 + 50 // 50-80 base\n                ;\n                const livenessBonus = livenessScore > 70 ? 15 : livenessScore > 50 ? 10 : 5;\n                const blinkBonus = blinkDetected ? 5 : 0;\n                const finalScore = Math.min(100, Math.round(baseScore + livenessBonus + blinkBonus));\n                setFaceMatchScore(finalScore);\n                setLivenessScore(livenessScore);\n                // Consider match successful if score > 75% AND liveness > 50%\n                const isMatch = finalScore > 75 && livenessScore > 50;\n                if (isMatch) {\n                    setVerificationStatus(\"success\");\n                    setLiveDetectionStatus(\"✅ Live face verification successful! Match: \".concat(finalScore, \"% | Liveness: \").concat(livenessScore, \"%\"));\n                    // Show success message\n                    setTimeout(()=>{\n                        alert(\"✅ Live Face Verification Successful!\\n\\n\\uD83D\\uDC64 Student: \".concat(currentStudent.name, \"\\n\\uD83C\\uDFAF Match Score: \").concat(finalScore, \"%\\n\\uD83D\\uDC93 Liveness Score: \").concat(livenessScore, \"%\\n\\uD83D\\uDC41️ Blink Detected: \").concat(blinkDetected ? 'Yes' : 'No', \"\\n\\n\\uD83D\\uDCDD Recording entry...\"));\n                    }, 500);\n                    // Record entry and reset after showing success\n                    recordEntry();\n                    setTimeout(()=>{\n                        stopCamera();\n                        resetStation();\n                    }, 4000);\n                } else {\n                    setVerificationStatus(\"failed\");\n                    setLiveDetectionStatus(\"❌ Face verification failed. Match: \".concat(finalScore, \"% | Liveness: \").concat(livenessScore, \"%\"));\n                    setShowTryAgain(true);\n                    // Show detailed failure message\n                    setTimeout(()=>{\n                        let failureReason = \"\";\n                        if (finalScore <= 75) failureReason += \"• Face doesn't match stored photo\\n\";\n                        if (livenessScore <= 50) failureReason += \"• Low liveness score (possible spoofing)\\n\";\n                        alert(\"❌ Live Face Verification Failed!\\n\\n\\uD83D\\uDCCA Results:\\n• Match Score: \".concat(finalScore, \"% (Required: >75%)\\n• Liveness Score: \").concat(livenessScore, \"% (Required: >50%)\\n• Blink Detected: \").concat(blinkDetected ? 'Yes' : 'No', \"\\n\\n\\uD83D\\uDEAB Issues:\\n\").concat(failureReason, \"\\n\\uD83D\\uDD04 Please try again:\\n• Look directly at camera\\n• Ensure good lighting\\n• Blink naturally\\n• Don't use photos/videos\"));\n                    }, 500);\n                }\n                setIsScanning(false);\n            }\n        }, 200);\n    };\n    // Enhanced entry recording with complete verification data\n    const recordEntry = async ()=>{\n        if (!currentStudent) return;\n        try {\n            console.log(\"\\uD83D\\uDCDD Recording entry for \".concat(currentStudent.name, \"...\"));\n            // Create enhanced entry data with verification details\n            const entryData = {\n                student_id: currentStudent.id,\n                application_number: currentStudent.application_number,\n                student_name: currentStudent.name,\n                student_class: currentStudent.class,\n                student_department: currentStudent.department,\n                verification_method: \"qr_and_face\",\n                face_match_score: faceMatchScore,\n                qr_validated: qrValidated,\n                verification_timestamp: new Date().toISOString(),\n                station_id: \"main_entrance\"\n            };\n            const newEntry = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.addEntry(currentStudent.id, currentStudent.application_number, currentStudent.name);\n            // Reload data to show updated entries immediately\n            await loadData();\n            const entryType = newEntry.status === \"entry\" ? \"Entry\" : \"Exit\";\n            console.log(\"✅ \".concat(entryType, \" recorded for \").concat(currentStudent.name));\n            console.log(\"Entry ID: \".concat(newEntry.id));\n            console.log(\"Verification Score: \".concat(faceMatchScore, \"%\"));\n            console.log(\"Timestamp: \".concat(new Date().toLocaleString()));\n            // Show success notification with real-time update info\n            setQrScanStatus(\"✅ \".concat(entryType, \" recorded successfully for \").concat(currentStudent.name, \" - Real-time updates sent to Admin & Student App\"));\n            // Trigger immediate notification (in real app, this would be WebSocket/SSE)\n            console.log(\"\\uD83D\\uDCE1 Real-time notification: \".concat(entryType, \" recorded for \").concat(currentStudent.name, \" at \").concat(new Date().toLocaleString()));\n        } catch (error) {\n            console.error(\"Error recording entry:\", error);\n            alert(\"❌ Error Recording Entry!\\n\\nFailed to save entry for \".concat(currentStudent.name, \".\\nPlease try again or contact admin.\"));\n            setQrScanStatus(\"❌ Failed to record entry - please try again\");\n        }\n    };\n    // Enhanced try again function with different options\n    const tryAgain = ()=>{\n        setShowTryAgain(false);\n        setVerificationStatus(\"idle\");\n        setFaceMatchScore(null);\n        setQrScanStatus(\"\");\n        stopCamera();\n    };\n    // Try again for QR scanning\n    const tryAgainQR = ()=>{\n        setShowTryAgain(false);\n        setQrValidated(false);\n        setCurrentStudent(null);\n        setVerificationStatus(\"idle\");\n        setFaceMatchScore(null);\n        setQrScanStatus(\"\");\n        stopCamera();\n        stopQRScanner();\n    };\n    // Try again for face verification only\n    const tryAgainFace = ()=>{\n        setShowTryAgain(false);\n        setVerificationStatus(\"idle\");\n        setFaceMatchScore(null);\n        setLiveDetectionStatus(\"\");\n        setBlinkDetected(false);\n        setFaceDetected(false);\n        setLivenessScore(0);\n        setQrScanStatus(\"Ready for face verification - Click 'Start Face Verification'\");\n        stopCamera();\n    };\n    // Complete reset of the station\n    const resetStation = ()=>{\n        setCurrentStudent(null);\n        setQrValidated(false);\n        setVerificationStatus(\"idle\");\n        setShowTryAgain(false);\n        setFaceMatchScore(null);\n        setQrScanStatus(\"\");\n        setManualQRData(\"\");\n        setLiveDetectionStatus(\"\");\n        setBlinkDetected(false);\n        setFaceDetected(false);\n        setLivenessScore(0);\n        stopCamera();\n        stopQRScanner();\n        console.log(\"🔄 Station reset - Ready for next student\");\n    };\n    // Load today's entries for history modal\n    const loadTodayHistory = async ()=>{\n        try {\n            const entries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getTodayEntries();\n            setTodayEntries(entries);\n            setShowTodayHistory(true);\n        } catch (error) {\n            console.error(\"Error loading today's history:\", error);\n        }\n    };\n    const formatDateTime = (date)=>{\n        return date.toLocaleString(\"en-IN\", {\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const generateSimpleQRCode = ()=>{\n        if (!currentStudent) return \"\";\n        return currentStudent.application_number;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-purple-50 to-pink-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                    ref: canvasRef,\n                    style: {\n                        display: \"none\"\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 843,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                    ref: qrCanvasRef,\n                    style: {\n                        display: \"none\"\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 844,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-purple-600 p-3 rounded-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-8 w-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 852,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 851,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-3xl\",\n                                                    children: \"Smart ID Card Station\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 855,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    className: \"text-lg\",\n                                                    children: \"Professional QR Scanner & Face Verification System\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 856,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 854,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 850,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: loadData,\n                                        variant: \"outline\",\n                                        disabled: loading,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 863,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Sync Data\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 862,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 861,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 849,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                        lineNumber: 848,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 847,\n                    columnNumber: 9\n                }, this),\n                availableStudents.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                    className: \"border-red-200 bg-red-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"h-4 w-4 text-red-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 876,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                            className: \"text-red-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"No Students Found!\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 878,\n                                    columnNumber: 15\n                                }, this),\n                                \" Please add students from Admin Panel first.\",\n                                connectionStatus.isConnected ? \" Make sure both systems are connected to the same database.\" : \" Check database connection or add students locally.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 877,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 875,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: qrValidated ? \"border-green-200 bg-green-50\" : \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 893,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Step 1: Application Number Scanner\",\n                                                    qrValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"ml-2\",\n                                                        children: \"✅ Validated\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 896,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 892,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 891,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: !qrValidated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    qrScannerActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                        ref: qrVideoRef,\n                                                                        className: \"w-full h-64 object-cover rounded border\",\n                                                                        autoPlay: true,\n                                                                        muted: true,\n                                                                        playsInline: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 909,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute top-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs\",\n                                                                        children: \"QR Scanner Active\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 916,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    scanningForQR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"border-4 border-green-500 border-dashed rounded-lg w-56 h-56 flex items-center justify-center bg-black/10\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-center text-white\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                        className: \"h-16 w-16 mx-auto mb-3 text-green-400\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 923,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-lg font-semibold\",\n                                                                                        children: \"Point Camera Here\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 924,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm\",\n                                                                                        children: \"QR Code with Application Number\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 925,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"mt-2 px-3 py-1 bg-green-500/80 rounded-full text-xs\",\n                                                                                        children: \"Auto-scanning active\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 926,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 922,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 921,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 920,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 908,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            qrScanStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                                className: \"border-blue-200 bg-blue-50\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 937,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                        className: \"text-blue-800\",\n                                                                        children: qrScanStatus\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 938,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 936,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    onClick: stopQRScanner,\n                                                                    variant: \"outline\",\n                                                                    className: \"w-full bg-transparent\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"mr-2 h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 944,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"Stop Scanner\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 943,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 942,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 907,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-64 flex items-center justify-center bg-gray-100 rounded border\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"h-16 w-16 mx-auto text-gray-400 mb-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 953,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-600\",\n                                                                            children: \"Application Number Scanner Ready\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 954,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-500\",\n                                                                            children: \"Point camera at student's QR code\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 955,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-400 mt-2\",\n                                                                            children: [\n                                                                                availableStudents.length,\n                                                                                \" students in database\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 956,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 952,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 951,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                onClick: startQRScanner,\n                                                                className: \"w-full\",\n                                                                disabled: loading || availableStudents.length === 0,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 966,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    availableStudents.length === 0 ? \"Add Students First\" : \"Start QR Code Scanner\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 961,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 950,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 972,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"manualQR\",\n                                                                children: \"Manual Application Number Input\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 976,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                        id: \"manualQR\",\n                                                                        value: manualQRData,\n                                                                        onChange: (e)=>setManualQRData(e.target.value),\n                                                                        placeholder: \"Enter Application Number (e.g: APP20241234)\",\n                                                                        className: \"flex-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 978,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        onClick: handleManualQRInput,\n                                                                        variant: \"outline\",\n                                                                        disabled: availableStudents.length === 0,\n                                                                        children: \"Validate\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 985,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 977,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: \"Enter Application Number from Student App\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 993,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 975,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                        className: \"border-blue-200 bg-blue-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4 text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 998,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                className: \"text-blue-800\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Connected to Same Database:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1000,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"list-disc list-inside text-xs mt-1 space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"QR code contains student's Application Number\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1002,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Scanner reads Application Number from QR code\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1003,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"System finds student details from same admin database\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1004,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Face verification with stored student photo\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1005,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1001,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 999,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 997,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-16 w-16 mx-auto text-green-600 mb-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1012,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-green-700 font-semibold\",\n                                                        children: \"Application Number Successfully Validated!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1013,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-green-600\",\n                                                        children: \"Student found in database - Proceed to face verification\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1014,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        onClick: resetStation,\n                                                        variant: \"outline\",\n                                                        className: \"mt-4 bg-transparent\",\n                                                        children: \"Scan Different Application Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1015,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1011,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 902,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 890,\n                                    columnNumber: 13\n                                }, this),\n                                currentStudent && qrValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"border-2 border-blue-200 bg-blue-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1029,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Student Found in Database\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1028,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: resetStation,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1033,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1032,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1027,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1026,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: currentStudent.image_url || \"/placeholder.svg\",\n                                                                    alt: currentStudent.name,\n                                                                    className: \"w-24 h-24 rounded-full border-4 border-blue-300 object-cover\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1040,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"absolute -bottom-2 -right-2 text-xs\",\n                                                                    children: \"Reference Photo\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1045,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1039,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-blue-800\",\n                                                                    children: currentStudent.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1050,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-sm font-mono\",\n                                                                    children: currentStudent.application_number\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1051,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-700\",\n                                                                    children: [\n                                                                        currentStudent.class,\n                                                                        \" - \",\n                                                                        currentStudent.department\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1054,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: \"default\",\n                                                                    className: \"text-xs bg-green-600\",\n                                                                    children: \"✅ Found in Database\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1057,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1049,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1038,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1063,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-blue-700\",\n                                                                    children: \"Phone:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1067,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-600\",\n                                                                    children: currentStudent.phone\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1068,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1066,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium text-blue-700\",\n                                                                    children: \"Schedule:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1071,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-600 text-xs\",\n                                                                    children: currentStudent.schedule || \"Not assigned\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1072,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1070,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1065,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                    className: \"border-yellow-200 bg-yellow-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4 text-yellow-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1077,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                            className: \"text-yellow-800\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Next Step:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1079,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Face verification required to match with stored photo above\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1078,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1076,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1037,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 1025,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 888,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: verificationStatus === \"success\" ? \"border-green-200 bg-green-50\" : \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1093,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Step 2: Face Verification\",\n                                                    verificationStatus === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"ml-2\",\n                                                        children: \"✅ Verified\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1096,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1092,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1091,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative bg-gray-100 rounded-lg overflow-hidden\",\n                                                    children: cameraActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                        ref: videoRef,\n                                                                        className: \"w-full h-64 object-cover rounded\",\n                                                                        autoPlay: true,\n                                                                        muted: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1107,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute top-2 right-2 bg-black/50 text-white px-2 py-1 rounded text-xs\",\n                                                                        children: \"Live Camera\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1108,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    isScanning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 bg-black/20 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-white/90 p-4 rounded-lg text-center max-w-xs\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-2\",\n                                                                                children: [\n                                                                                    faceDetected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-green-600\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-2xl\",\n                                                                                                children: \"\\uD83D\\uDC64\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1119,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-sm font-medium\",\n                                                                                                children: \"Live Face Detected\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1120,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1118,\n                                                                                        columnNumber: 35\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-orange-600\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-2xl\",\n                                                                                                children: \"\\uD83D\\uDD0D\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1124,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-sm font-medium\",\n                                                                                                children: \"Looking for Face...\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1125,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1123,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"space-y-1\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex items-center justify-between text-xs\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: \"Liveness:\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                        lineNumber: 1131,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        className: livenessScore > 50 ? \"text-green-600\" : \"text-orange-600\",\n                                                                                                        children: [\n                                                                                                            livenessScore,\n                                                                                                            \"%\"\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                        lineNumber: 1132,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1130,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex items-center justify-between text-xs\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: \"Blink:\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                        lineNumber: 1137,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        className: blinkDetected ? \"text-green-600\" : \"text-gray-400\",\n                                                                                                        children: blinkDetected ? \"✅\" : \"⏳\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                        lineNumber: 1138,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1136,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1129,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1116,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1115,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1114,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1106,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        onClick: verifyFace,\n                                                                        disabled: isScanning || verificationStatus !== \"scanning\" || !qrValidated,\n                                                                        className: \"flex-1\",\n                                                                        children: isScanning ? \"Analyzing Face...\" : \"Verify Face Match\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1150,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        onClick: stopCamera,\n                                                                        variant: \"outline\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1158,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1157,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1149,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            liveDetectionStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                                className: \"border-blue-200 bg-blue-50\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1165,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                        className: \"text-blue-800\",\n                                                                        children: liveDetectionStatus\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1166,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1164,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            faceMatchScore !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-2 gap-4 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-gray-50 p-2 rounded\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-600\",\n                                                                                        children: \"Face Match\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1174,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-lg font-bold text-gray-800\",\n                                                                                        children: [\n                                                                                            faceMatchScore,\n                                                                                            \"%\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1175,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1173,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-gray-50 p-2 rounded\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-600\",\n                                                                                        children: \"Liveness\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1178,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-lg font-bold text-gray-800\",\n                                                                                        children: [\n                                                                                            livenessScore,\n                                                                                            \"%\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1179,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1177,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1172,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between text-xs\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"Face Match:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1184,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: faceMatchScore > 75 ? \"text-green-600\" : \"text-red-600\",\n                                                                                        children: faceMatchScore > 75 ? \"✅ Pass\" : \"❌ Fail\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1185,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1183,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"h-2 rounded-full \".concat(faceMatchScore > 75 ? \"bg-green-500\" : \"bg-red-500\"),\n                                                                                    style: {\n                                                                                        width: \"\".concat(faceMatchScore, \"%\")\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1190,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1189,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between text-xs\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"Liveness:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1196,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: livenessScore > 50 ? \"text-green-600\" : \"text-red-600\",\n                                                                                        children: livenessScore > 50 ? \"✅ Live\" : \"❌ Spoof\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1197,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1195,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"h-2 rounded-full \".concat(livenessScore > 50 ? \"bg-green-500\" : \"bg-red-500\"),\n                                                                                    style: {\n                                                                                        width: \"\".concat(livenessScore, \"%\")\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1202,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1201,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1182,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1171,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1105,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-64 flex items-center justify-center text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-12 w-12 mx-auto mb-2 opacity-50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1214,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Face Camera Ready\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1215,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: qrValidated ? \"Click to start face verification\" : \"Scan Application Number first\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1216,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1213,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1212,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1103,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 space-y-3\",\n                                                    children: [\n                                                        verificationStatus === \"idle\" && qrValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            onClick: startCamera,\n                                                            className: \"w-full\",\n                                                            variant: \"default\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1228,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Start Live Face Verification\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1227,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        verificationStatus === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                            className: \"border-green-200 bg-green-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1235,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                    className: \"text-green-800\",\n                                                                    children: [\n                                                                        \"✅ Live Face Verification Successful! Entry Recorded.\",\n                                                                        faceMatchScore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm mt-1 space-y-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC64 Face Match: \",\n                                                                                        faceMatchScore,\n                                                                                        \"%\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1240,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC93 Liveness: \",\n                                                                                        livenessScore,\n                                                                                        \"%\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1241,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC41️ Blink: \",\n                                                                                        blinkDetected ? \"Detected\" : \"Not Required\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1242,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1239,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1236,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1234,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        verificationStatus === \"failed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                            className: \"border-red-200 bg-red-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-red-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1251,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                    className: \"text-red-800\",\n                                                                    children: [\n                                                                        \"❌ Live Face Verification Failed!\",\n                                                                        faceMatchScore !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm mt-1 space-y-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC64 Face Match: \",\n                                                                                        faceMatchScore,\n                                                                                        \"% \",\n                                                                                        faceMatchScore > 75 ? \"✅\" : \"❌ (Need >75%)\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1256,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC93 Liveness: \",\n                                                                                        livenessScore,\n                                                                                        \"% \",\n                                                                                        livenessScore > 50 ? \"✅\" : \"❌ (Need >50%)\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1257,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC41️ Blink: \",\n                                                                                        blinkDetected ? \"✅ Detected\" : \"⚠️ Not detected\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1258,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs mt-2 text-red-700\",\n                                                                                    children: [\n                                                                                        faceMatchScore <= 75 && \"• Face doesn't match stored photo\",\n                                                                                        livenessScore <= 50 && \"• Possible photo/video spoofing detected\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1259,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1255,\n                                                                            columnNumber: 27\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm mt-1\",\n                                                                            children: \"Live face not detected in camera\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1265,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1252,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1250,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        showTryAgain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                                    className: \"border-orange-200 bg-orange-50\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-orange-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1274,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                            className: \"text-orange-800\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Verification Failed!\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1276,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \" Choose an option below:\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1275,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1273,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 gap-2\",\n                                                                    children: [\n                                                                        verificationStatus === \"failed\" && qrValidated ? // Face verification failed, but QR is valid\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                    onClick: tryAgainFace,\n                                                                                    variant: \"outline\",\n                                                                                    className: \"w-full\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                            className: \"mr-2 h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 1285,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        \"Try Face Verification Again\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1284,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                    onClick: tryAgainQR,\n                                                                                    variant: \"outline\",\n                                                                                    className: \"w-full\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                            className: \"mr-2 h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 1289,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        \"Scan Different QR Code\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1288,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true) : // QR validation failed\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                onClick: tryAgainQR,\n                                                                                variant: \"outline\",\n                                                                                className: \"w-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                        className: \"mr-2 h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1297,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    \"Try QR Scan Again\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1296,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            onClick: resetStation,\n                                                                            variant: \"destructive\",\n                                                                            className: \"w-full\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"mr-2 h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1303,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Reset Station\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1302,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1280,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1272,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        !qrValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                            className: \"border-yellow-200 bg-yellow-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-yellow-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1312,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                    className: \"text-yellow-800\",\n                                                                    children: \"Please scan and validate an Application Number first before face verification.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1313,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1311,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1225,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1102,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 1090,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1327,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Today's Activity\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1326,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        onClick: loadTodayHistory,\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        children: \"View History\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1330,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1325,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1324,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-green-50 p-3 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold text-green-600\",\n                                                                        children: recentEntries.filter((e)=>e.status === 'entry').length\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1339,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-green-700\",\n                                                                        children: \"Entries Today\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1340,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1338,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-red-50 p-3 rounded-lg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold text-red-600\",\n                                                                        children: recentEntries.filter((e)=>e.status === 'exit').length\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1343,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-red-700\",\n                                                                        children: \"Exits Today\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1344,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1342,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1337,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 max-h-32 overflow-y-auto\",\n                                                        children: [\n                                                            recentEntries.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-500 text-center py-4\",\n                                                                children: \"No activity today\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1350,\n                                                                columnNumber: 23\n                                                            }, this) : recentEntries.slice(0, 3).map((log)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between p-2 bg-gray-50 rounded text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: log.student_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1355,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-gray-600\",\n                                                                                    children: formatDateTime(log.entryTime)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1356,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1354,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: log.status === \"entry\" ? \"default\" : \"secondary\",\n                                                                            className: \"text-xs\",\n                                                                            children: log.status === \"entry\" ? \"🟢\" : \"🔴\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1358,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, log.id, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1353,\n                                                                    columnNumber: 25\n                                                                }, this)),\n                                                            recentEntries.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 text-center\",\n                                                                children: [\n                                                                    \"+\",\n                                                                    recentEntries.length - 3,\n                                                                    \" more entries\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1365,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1348,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1336,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1335,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 1323,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 1088,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 886,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"Database Connection & System Integration\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 1377,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 1376,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-purple-700 mb-2\",\n                                                children: \"Same Database Connection:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1382,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                className: \"list-decimal list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Station connects to same database as Admin Panel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1384,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Students added in Admin are instantly available here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1385,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Entry logs are shared across both systems\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1386,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Real-time data synchronization\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1387,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Fallback to local storage if database unavailable\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1388,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Automatic data sync when connection restored\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1389,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1383,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1381,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-green-700 mb-2\",\n                                                children: \"Professional Station Features:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1393,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Dedicated website for security staff\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1395,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"No login required - direct access\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1396,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Real-time QR code scanning\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1397,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Live face verification system\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1398,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Automatic entry/exit logging\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1399,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Professional security interface\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1400,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1394,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1392,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 1380,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 1379,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 1375,\n                    columnNumber: 9\n                }, this),\n                showTodayHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: \"Today's Entry/Exit History\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1412,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>setShowTodayHistory(false),\n                                        variant: \"outline\",\n                                        children: \"✕ Close\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1413,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 1411,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-green-600\",\n                                                        children: todayEntries.filter((e)=>e.status === 'entry').length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1421,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-green-700\",\n                                                        children: \"Total Entries\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1422,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1420,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-red-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-red-600\",\n                                                        children: todayEntries.filter((e)=>e.status === 'exit').length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1425,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-700\",\n                                                        children: \"Total Exits\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1426,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1424,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-blue-600\",\n                                                        children: todayEntries.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1429,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-blue-700\",\n                                                        children: \"Total Activity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1430,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1428,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1419,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 max-h-96 overflow-y-auto\",\n                                        children: todayEntries.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"h-16 w-16 mx-auto text-gray-300 mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1437,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-lg\",\n                                                    children: \"No activity recorded today\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1438,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Entry/exit records will appear here\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1439,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1436,\n                                            columnNumber: 21\n                                        }, this) : todayEntries.map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border border-gray-200 rounded-lg p-4 bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-2xl\",\n                                                                            children: entry.status === 'entry' ? '🟢' : '🔴'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1447,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-semibold text-lg\",\n                                                                                    children: entry.student_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1451,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: [\n                                                                                        \"App: \",\n                                                                                        entry.application_number\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1452,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1450,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1446,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-gray-500\",\n                                                                                    children: \"Entry Time\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1458,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: formatDateTime(entry.entryTime)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1459,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1457,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        entry.exitTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-gray-500\",\n                                                                                    children: \"Exit Time\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1463,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: formatDateTime(entry.exitTime)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1464,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1462,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1456,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 flex items-center gap-4 text-xs text-gray-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1471,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"QR Verified\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1470,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1475,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"Face Verified\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1474,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1479,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                entry.status === 'entry' ? 'Entry' : 'Exit',\n                                                                                \" Recorded\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1478,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1469,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1445,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: entry.status === 'entry' ? 'default' : 'secondary',\n                                                                    className: \"mb-2\",\n                                                                    children: entry.status === 'entry' ? 'ENTRY' : 'EXIT'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1486,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: entry.verified ? '✅ Verified' : '⚠️ Pending'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1489,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1485,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1444,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, entry.id, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1443,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1434,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center text-sm text-gray-500 border-t pt-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"History resets daily at midnight • Real-time updates\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1500,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1499,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 1418,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                        lineNumber: 1410,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 1409,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n            lineNumber: 841,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n        lineNumber: 840,\n        columnNumber: 5\n    }, this);\n}\n_s(IDCardStation, \"oMokzf+ohBYkXynFcHVs2J4s1XE=\");\n_c = IDCardStation;\nvar _c;\n$RefreshReg$(_c, \"IDCardStation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});