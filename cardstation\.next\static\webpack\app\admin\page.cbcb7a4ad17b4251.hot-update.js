"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./lib/database-store.ts":
/*!*******************************!*\
  !*** ./lib/database-store.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dbStore: () => (/* binding */ dbStore)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ dbStore auto */ \n// Local storage keys\nconst STUDENTS_KEY = \"smart_id_students\";\nconst ENTRIES_KEY = \"smart_id_entries\";\nclass DatabaseStore {\n    isSupabaseAvailable() {\n        return _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase !== null && \"object\" !== \"undefined\";\n    }\n    isLocalStorageAvailable() {\n        return  true && typeof window.localStorage !== \"undefined\";\n    }\n    // Local Storage Methods\n    saveStudentsToLocal(students) {\n        if (this.isLocalStorageAvailable()) {\n            localStorage.setItem(STUDENTS_KEY, JSON.stringify(students));\n        }\n    }\n    loadStudentsFromLocal() {\n        if (!this.isLocalStorageAvailable()) return [];\n        try {\n            const data = localStorage.getItem(STUDENTS_KEY);\n            if (!data) return [];\n            const students = JSON.parse(data);\n            return students.map((s)=>({\n                    ...s,\n                    createdAt: new Date(s.createdAt),\n                    updatedAt: new Date(s.updatedAt)\n                }));\n        } catch (error) {\n            console.error(\"Error loading students from localStorage:\", error);\n            return [];\n        }\n    }\n    saveEntriesToLocal(entries) {\n        if (this.isLocalStorageAvailable()) {\n            localStorage.setItem(ENTRIES_KEY, JSON.stringify(entries));\n        }\n    }\n    loadEntriesFromLocal() {\n        if (!this.isLocalStorageAvailable()) return [];\n        try {\n            const data = localStorage.getItem(ENTRIES_KEY);\n            if (!data) return [];\n            const entries = JSON.parse(data);\n            return entries.map((e)=>({\n                    ...e,\n                    entryTime: new Date(e.entryTime),\n                    exitTime: e.exitTime ? new Date(e.exitTime) : undefined,\n                    createdAt: new Date(e.createdAt)\n                }));\n        } catch (error) {\n            console.error(\"Error loading entries from localStorage:\", error);\n            return [];\n        }\n    }\n    // Student Management\n    async addStudent(student) {\n        const res = await fetch(\"/api/students\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(student)\n        });\n        if (!res.ok) throw new Error(\"Failed to add student\");\n        const data = await res.json();\n        return {\n            ...data,\n            createdAt: new Date(data.createdAt),\n            updatedAt: new Date(data.updatedAt)\n        };\n    }\n    async getStudents() {\n        const res = await fetch(\"/api/students\");\n        if (!res.ok) throw new Error(\"Failed to fetch students\");\n        const data = await res.json();\n        return data.map((s)=>({\n                ...s,\n                createdAt: new Date(s.createdAt),\n                updatedAt: new Date(s.updatedAt)\n            }));\n    }\n    async getStudentByAppNumber(appNumber) {\n        const res = await fetch(\"/api/students?application_number=\".concat(encodeURIComponent(appNumber)));\n        if (!res.ok) return null;\n        const data = await res.json();\n        if (!data || data.length === 0) return null;\n        const s = data[0];\n        return {\n            ...s,\n            createdAt: new Date(s.createdAt),\n            updatedAt: new Date(s.updatedAt)\n        };\n    }\n    async getStudentByAppAndPhone(appNumber, phone) {\n        const url = \"/api/students?application_number=\".concat(encodeURIComponent(appNumber), \"&phone=\").concat(encodeURIComponent(phone));\n        const res = await fetch(url);\n        if (!res.ok) return null;\n        const data = await res.json();\n        if (!data || data.length === 0) return null;\n        const s = data[0];\n        return {\n            ...s,\n            createdAt: new Date(s.createdAt),\n            updatedAt: new Date(s.updatedAt)\n        };\n    }\n    async updateStudent(id, updates) {\n        const res = await fetch(\"/api/students\", {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                id,\n                ...updates\n            })\n        });\n        if (!res.ok) throw new Error(\"Failed to update student\");\n        const data = await res.json();\n        return {\n            ...data,\n            createdAt: new Date(data.createdAt),\n            updatedAt: new Date(data.updatedAt)\n        };\n    }\n    async deleteStudent(id) {\n        const res = await fetch(\"/api/students\", {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                id\n            })\n        });\n        if (!res.ok) throw new Error(\"Failed to delete student\");\n        return true;\n    }\n    // Entry Log Management - Using API route for better reliability\n    async addEntry(studentId, applicationNumber, studentName) {\n        try {\n            const entryData = {\n                student_id: studentId,\n                application_number: applicationNumber,\n                student_name: studentName,\n                verification_method: \"qr_and_face\",\n                qr_validated: true,\n                verification_timestamp: new Date().toISOString(),\n                station_id: \"main_entrance\"\n            };\n            console.log(\"Sending entry data to API:\", entryData);\n            const res = await fetch('/api/entries', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(entryData)\n            });\n            if (!res.ok) {\n                const errorText = await res.text();\n                console.error(\"API Error:\", errorText);\n                throw new Error(\"Failed to record entry: \".concat(res.status, \" \").concat(errorText));\n            }\n            const data = await res.json();\n            console.log(\"Entry recorded successfully:\", data);\n            return {\n                ...data,\n                entryTime: new Date(data.entry_time),\n                exitTime: data.exit_time ? new Date(data.exit_time) : undefined,\n                createdAt: new Date(data.created_at || data.entry_time),\n                updatedAt: new Date(data.updated_at || data.entry_time)\n            };\n        } catch (error) {\n            console.error(\"Error recording entry:\", error);\n            throw error;\n        }\n    }\n    async getStudentEntries(studentId) {\n        try {\n            // Use API route which handles both MongoDB and fallback\n            const res = await fetch(\"/api/entries?student_id=\".concat(encodeURIComponent(studentId)));\n            if (!res.ok) {\n                console.error(\"Failed to fetch entries from API\");\n                return [];\n            }\n            const data = await res.json();\n            return data.map((e)=>({\n                    ...e,\n                    entryTime: new Date(e.entry_time),\n                    exitTime: e.exit_time ? new Date(e.exit_time) : undefined,\n                    createdAt: new Date(e.created_at || e.entry_time),\n                    updatedAt: new Date(e.updated_at || e.entry_time)\n                }));\n        } catch (error) {\n            console.error(\"Error fetching student entries:\", error);\n            return [];\n        }\n    }\n    async getAllEntries() {\n        try {\n            console.log(\"🔄 Database Store: Fetching all entries from API...\");\n            // Use API route which handles both MongoDB and fallback\n            const res = await fetch('/api/entries');\n            console.log(\"📡 API Response status:\", res.status);\n            if (!res.ok) {\n                console.error(\"❌ Failed to fetch all entries from API, status:\", res.status);\n                return [];\n            }\n            const data = await res.json();\n            console.log(\"📊 Raw API data received:\", data.length, \"entries\");\n            console.log(\"📊 Sample entry:\", data[0]);\n            const processedData = data.map((e)=>({\n                    ...e,\n                    entryTime: new Date(e.entry_time),\n                    exitTime: e.exit_time ? new Date(e.exit_time) : undefined,\n                    createdAt: new Date(e.created_at || e.entry_time),\n                    updatedAt: new Date(e.updated_at || e.entry_time)\n                }));\n            console.log(\"✅ Database Store: Processed\", processedData.length, \"entries\");\n            return processedData;\n        } catch (error) {\n            console.error(\"❌ Error fetching all entries:\", error);\n            return [];\n        }\n    }\n    async getTodayEntries() {\n        try {\n            // Get all entries and filter for today\n            const allEntries = await this.getAllEntries();\n            const today = new Date().toDateString();\n            return allEntries.filter((e)=>e.entryTime.toDateString() === today);\n        } catch (error) {\n            console.error(\"Error fetching today entries:\", error);\n            return [];\n        }\n    }\n    // Admin Authentication\n    async authenticateAdmin(username, password) {\n        if (this.isSupabaseAvailable() && _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase) {\n            // Use Supabase\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"admin_users\").select(\"*\").eq(\"username\", username).single();\n            if (error || !data) {\n                return false;\n            }\n            // Simple password check (in production, use proper hashing)\n            return password === \"admin123\";\n        } else {\n            // Fallback authentication for demo\n            return username === \"admin\" && password === \"admin123\";\n        }\n    }\n    // Utility functions\n    generateApplicationNumber() {\n        const year = new Date().getFullYear();\n        const random = Math.floor(Math.random() * 10000).toString().padStart(4, \"0\");\n        return \"APP\".concat(year).concat(random);\n    }\n    convertStudentDates(student) {\n        return {\n            ...student,\n            createdAt: new Date(student.created_at),\n            updatedAt: new Date(student.updated_at)\n        };\n    }\n    convertEntryLogDates(entry) {\n        return {\n            ...entry,\n            entryTime: new Date(entry.entry_time),\n            exitTime: entry.exit_time ? new Date(entry.exit_time) : undefined,\n            createdAt: new Date(entry.created_at)\n        };\n    }\n    // Clear all local data (for testing)\n    clearLocalData() {\n        if (this.isLocalStorageAvailable()) {\n            localStorage.removeItem(STUDENTS_KEY);\n            localStorage.removeItem(ENTRIES_KEY);\n        }\n    }\n    // Get storage info\n    getStorageInfo() {\n        return {\n            mode: \"Cloud\",\n            studentsCount: 0,\n            entriesCount: 0\n        };\n    }\n}\nconst dbStore = new DatabaseStore();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/database-store.ts\n"));

/***/ })

});