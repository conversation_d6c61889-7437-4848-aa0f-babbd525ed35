import { NextRequest, NextResponse } from "next/server"
import clientPromise from "@/lib/mongodb"
import { ObjectId } from "mongodb"

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url)
    const appNumber = url.searchParams.get("application_number")
    const phone = url.searchParams.get("phone")

    if (!clientPromise) {
      // MongoDB not available, use sample data for testing
      console.log("MongoDB not available, using sample data for testing")

      const sampleStudents = [
        {
          id: "STU_001",
          application_number: "APP20254105",
          name: "Test Student",
          phone: "9772348371",
          email: "<EMAIL>",
          class: "12th",
          department: "Science",
          schedule: "Morning",
          image_url: "/placeholder-user.jpg",
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ]

      // Filter based on query parameters
      let filteredStudents = sampleStudents
      if (appNumber && phone) {
        filteredStudents = sampleStudents.filter(s =>
          s.application_number === appNumber && s.phone === phone
        )
      } else if (appNumber) {
        filteredStudents = sampleStudents.filter(s =>
          s.application_number === appNumber
        )
      }

      return NextResponse.json(filteredStudents)
    }

    const client = await clientPromise
    const db = client.db("idcard")
    const students = db.collection("students")

    const query: any = {}
    if (appNumber) query.application_number = appNumber
    if (phone) query.phone = phone

    const results = await students.find(query).sort({ createdAt: -1 }).toArray()
    const data = results.map((s) => ({
      ...s,
      id: s._id.toString(),
    }))
    return NextResponse.json(data)
  } catch (error) {
    console.error("GET /api/students error:", error)
    // Return sample data for testing instead of empty array
    const sampleStudents = [
      {
        id: "STU_001",
        application_number: "APP20254105",
        name: "Test Student",
        phone: "9772348371",
        email: "<EMAIL>",
        class: "12th",
        department: "Science",
        schedule: "Morning",
        image_url: "/placeholder-user.jpg",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
    return NextResponse.json(sampleStudents)
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    console.log("Received body:", body)

    if (!clientPromise) {
      return NextResponse.json({ error: "Database not available" }, { status: 503 })
    }

    const client = await clientPromise
    if (!client) {
      return NextResponse.json({ error: "Database connection failed" }, { status: 503 })
    }

    const db = client.db("idcard")
    const students = db.collection("students")

    const newStudent = {
      ...body,
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    const result = await students.insertOne(newStudent)
    console.log("Insert result:", result)
    return NextResponse.json({ ...newStudent, id: result.insertedId.toString() }, { status: 201 })
  } catch (error) {
    console.error("POST /api/students error:", error)
    return NextResponse.json({ error: "Failed to add student" }, { status: 500 })
  }
}

export async function PUT(req: NextRequest) {
  try {
    const { id, ...updates } = await req.json()

    if (!clientPromise) {
      return NextResponse.json({ error: "Database not available" }, { status: 503 })
    }

    const client = await clientPromise
    if (!client) {
      return NextResponse.json({ error: "Database connection failed" }, { status: 503 })
    }

    const db = client.db("idcard")
    const students = db.collection("students")

    const result = await students.findOneAndUpdate(
      { _id: new ObjectId(id) },
      { $set: { ...updates, updatedAt: new Date() } },
      { returnDocument: "after" }
    )
    if (!result || !result.value) {
      return NextResponse.json({ error: "Student not found" }, { status: 404 })
    }
    const updated = result.value
    return NextResponse.json({ ...updated, id: updated._id.toString() })
  } catch (error) {
    return NextResponse.json({ error: "Failed to update student" }, { status: 500 })
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const { id } = await req.json()
    if (!id) return NextResponse.json({ error: "Missing id" }, { status: 400 })

    if (!clientPromise) {
      return NextResponse.json({ error: "Database not available" }, { status: 503 })
    }

    const client = await clientPromise
    if (!client) {
      return NextResponse.json({ error: "Database connection failed" }, { status: 503 })
    }

    const db = client.db("idcard")
    const students = db.collection("students")

    const result = await students.deleteOne({ _id: new ObjectId(id) })
    if (result.deletedCount === 0) {
      return NextResponse.json({ error: "Student not found" }, { status: 404 })
    }
    return NextResponse.json({ success: true })
  } catch (error) {
    return NextResponse.json({ error: "Failed to delete student" }, { status: 500 })
  }
} 