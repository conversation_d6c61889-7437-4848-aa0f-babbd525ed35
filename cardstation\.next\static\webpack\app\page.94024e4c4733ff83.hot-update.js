"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IDCardStation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scan.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Camera,CameraOff,CheckCircle,Clock,QrCode,RefreshCw,RotateCcw,Scan,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _lib_database_store__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/database-store */ \"(app-pages-browser)/./lib/database-store.ts\");\n/* harmony import */ var jsqr__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! jsqr */ \"(app-pages-browser)/./node_modules/jsqr/dist/jsQR.js\");\n/* harmony import */ var jsqr__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(jsqr__WEBPACK_IMPORTED_MODULE_10__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction IDCardStation() {\n    _s();\n    const [currentStudent, setCurrentStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [qrValidated, setQrValidated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScanning, setIsScanning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cameraActive, setCameraActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [qrScannerActive, setQrScannerActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [verificationStatus, setVerificationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"idle\");\n    const [recentEntries, setRecentEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showTryAgain, setShowTryAgain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [availableStudents, setAvailableStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [manualQRData, setManualQRData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showTodayHistory, setShowTodayHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [todayEntries, setTodayEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [faceMatchScore, setFaceMatchScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [scanningForQR, setScanningForQR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [qrScanStatus, setQrScanStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [liveDetectionStatus, setLiveDetectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [blinkDetected, setBlinkDetected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [faceDetected, setFaceDetected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [livenessScore, setLivenessScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isConnected: false,\n        mode: \"Local Storage\",\n        studentsCount: 0,\n        entriesCount: 0\n    });\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const qrVideoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const qrCanvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scanIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IDCardStation.useEffect\": ()=>{\n            // Clear all entry data on app start\n            if (true) {\n                // Clear any local storage entries\n                localStorage.removeItem(\"entries\");\n                console.log(\"🧹 Card Station: Cleared all previous entry data\");\n            }\n            loadData();\n            checkConnection();\n            // Auto-refresh today's entries every 5 seconds\n            const interval = setInterval({\n                \"IDCardStation.useEffect.interval\": async ()=>{\n                    try {\n                        const todaysEntries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getTodayEntries();\n                        setTodayEntries(todaysEntries);\n                        console.log(\"🔄 Auto-refreshed today's entries:\", {\n                            count: todaysEntries.length,\n                            entries: todaysEntries.filter({\n                                \"IDCardStation.useEffect.interval\": (e)=>e.status === 'entry'\n                            }[\"IDCardStation.useEffect.interval\"]).length,\n                            exits: todaysEntries.filter({\n                                \"IDCardStation.useEffect.interval\": (e)=>e.status === 'exit'\n                            }[\"IDCardStation.useEffect.interval\"]).length\n                        });\n                    } catch (error) {\n                        console.error(\"Error auto-refreshing today's entries:\", error);\n                    }\n                }\n            }[\"IDCardStation.useEffect.interval\"], 5000);\n            return ({\n                \"IDCardStation.useEffect\": ()=>clearInterval(interval)\n            })[\"IDCardStation.useEffect\"];\n        }\n    }[\"IDCardStation.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IDCardStation.useEffect\": ()=>{\n            // Cleanup scan interval on unmount\n            return ({\n                \"IDCardStation.useEffect\": ()=>{\n                    if (scanIntervalRef.current) {\n                        clearInterval(scanIntervalRef.current);\n                    }\n                }\n            })[\"IDCardStation.useEffect\"];\n        }\n    }[\"IDCardStation.useEffect\"], []);\n    const checkConnection = async ()=>{\n        try {\n            const status = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getStorageInfo();\n            setConnectionStatus({\n                isConnected: status.mode === \"Cloud Database\",\n                mode: status.mode,\n                studentsCount: status.studentsCount,\n                entriesCount: status.entriesCount\n            });\n        } catch (error) {\n            console.error(\"Error checking connection:\", error);\n            setConnectionStatus({\n                isConnected: false,\n                mode: \"Local Storage (Error)\",\n                studentsCount: 0,\n                entriesCount: 0\n            });\n        }\n    };\n    const loadData = async ()=>{\n        try {\n            setLoading(true);\n            const students = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getStudents();\n            const entries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getAllEntries();\n            const todaysEntries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getTodayEntries();\n            setAvailableStudents(students);\n            setRecentEntries(entries.slice(0, 5));\n            setTodayEntries(todaysEntries);\n            console.log(\"📊 Cardstation data loaded:\", {\n                students: students.length,\n                allEntries: entries.length,\n                todayEntries: todaysEntries.length,\n                todayEntriesCount: todaysEntries.filter((e)=>e.status === 'entry').length,\n                todayExitsCount: todaysEntries.filter((e)=>e.status === 'exit').length\n            });\n            // Update connection status\n            checkConnection();\n            console.log(\"✅ Loaded \".concat(students.length, \" students from \").concat(connectionStatus.mode));\n        } catch (error) {\n            console.error(\"Error loading data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Enhanced Application Number validation with better error handling\n    const validateApplicationNumber = async (appNumber)=>{\n        try {\n            // Clean the application number\n            const cleanAppNumber = appNumber.trim().toUpperCase();\n            if (!cleanAppNumber) {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: \"Empty Application Number. Please scan a valid QR code.\",\n                    errorType: \"EMPTY_QR\"\n                };\n            }\n            // Validate application number format (should start with APP followed by year and 4 digits)\n            const appNumberPattern = /^APP\\d{8}$/;\n            if (!appNumberPattern.test(cleanAppNumber)) {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: 'Invalid QR Code Format: \"'.concat(cleanAppNumber, '\" is not a valid application number format. Expected format: APP followed by 8 digits.'),\n                    errorType: \"INVALID_FORMAT\"\n                };\n            }\n            // Ensure we have loaded student data from admin database\n            if (availableStudents.length === 0) {\n                setQrScanStatus(\"Loading student data from admin database...\");\n                await loadData();\n                if (availableStudents.length === 0) {\n                    return {\n                        isValid: false,\n                        student: null,\n                        error: \"No students found in admin database. Please check database connection or add students from Admin Panel.\",\n                        errorType: \"NO_DATABASE_CONNECTION\"\n                    };\n                }\n            }\n            // Find student by application number in admin database\n            setQrScanStatus(\"Checking application number against admin database...\");\n            const student = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getStudentByAppNumber(cleanAppNumber);\n            if (!student) {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: 'Application Number Not Found: \"'.concat(cleanAppNumber, '\" is not registered in the admin database. Please verify the QR code or contact admin for registration.'),\n                    errorType: \"NOT_FOUND_IN_DATABASE\"\n                };\n            }\n            // Verify student has required data for face verification\n            if (!student.image_url || student.image_url.trim() === '') {\n                return {\n                    isValid: false,\n                    student: null,\n                    error: \"Student Photo Missing: \".concat(student.name, \" (\").concat(cleanAppNumber, \") does not have a photo in the admin database. Please contact admin to add a photo for face verification.\"),\n                    errorType: \"NO_PHOTO\"\n                };\n            }\n            // Success - Application number is valid and student found in admin database\n            console.log(\"✅ Application Number Validated: \".concat(student.name, \" (\").concat(cleanAppNumber, \")\"));\n            return {\n                isValid: true,\n                student,\n                errorType: \"SUCCESS\"\n            };\n        } catch (error) {\n            console.error(\"Application number validation error:\", error);\n            return {\n                isValid: false,\n                student: null,\n                error: \"Database Connection Error: Unable to validate application number against admin database. Please check connection and try again.\",\n                errorType: \"DATABASE_ERROR\"\n            };\n        }\n    };\n    // Real QR Code detection using jsQR library\n    const detectQRCode = ()=>{\n        if (!qrVideoRef.current || !qrCanvasRef.current) return null;\n        const video = qrVideoRef.current;\n        const canvas = qrCanvasRef.current;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx || video.videoWidth === 0 || video.videoHeight === 0) return null;\n        try {\n            // Set canvas size to match video\n            canvas.width = video.videoWidth;\n            canvas.height = video.videoHeight;\n            // Draw current video frame to canvas\n            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n            // Get image data for QR detection\n            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n            // Use jsQR library for actual QR code detection\n            const code = jsqr__WEBPACK_IMPORTED_MODULE_10___default()(imageData.data, imageData.width, imageData.height, {\n                inversionAttempts: \"dontInvert\"\n            });\n            if (code) {\n                console.log(\"QR Code detected:\", code.data);\n                return code.data;\n            }\n            return null;\n        } catch (error) {\n            console.error(\"QR detection error:\", error);\n            return null;\n        }\n    };\n    // Start QR Scanner with enhanced error handling\n    const startQRScanner = async ()=>{\n        try {\n            setQrScannerActive(true);\n            setScanningForQR(true);\n            setQrScanStatus(\"Starting camera...\");\n            // Ensure we have student data loaded\n            await loadData();\n            let stream;\n            try {\n                // Try back camera first (better for QR scanning)\n                stream = await navigator.mediaDevices.getUserMedia({\n                    video: {\n                        facingMode: \"environment\",\n                        width: {\n                            ideal: 1280,\n                            min: 640\n                        },\n                        height: {\n                            ideal: 720,\n                            min: 480\n                        }\n                    }\n                });\n                setQrScanStatus(\"Back camera active - Point at QR code (\".concat(availableStudents.length, \" students loaded)\"));\n            } catch (envError) {\n                try {\n                    // Fallback to front camera\n                    stream = await navigator.mediaDevices.getUserMedia({\n                        video: {\n                            facingMode: \"user\",\n                            width: {\n                                ideal: 1280,\n                                min: 640\n                            },\n                            height: {\n                                ideal: 720,\n                                min: 480\n                            }\n                        }\n                    });\n                    setQrScanStatus(\"Front camera active - Point at QR code (\".concat(availableStudents.length, \" students loaded)\"));\n                } catch (userError) {\n                    // Fallback to any camera\n                    stream = await navigator.mediaDevices.getUserMedia({\n                        video: {\n                            width: {\n                                ideal: 1280,\n                                min: 640\n                            },\n                            height: {\n                                ideal: 720,\n                                min: 480\n                            }\n                        }\n                    });\n                    setQrScanStatus(\"Camera active - Point at QR code (\".concat(availableStudents.length, \" students loaded)\"));\n                }\n            }\n            if (qrVideoRef.current && stream) {\n                qrVideoRef.current.srcObject = stream;\n                await qrVideoRef.current.play();\n                // Start continuous QR scanning\n                startContinuousScanning();\n                console.log(\"QR Scanner camera started successfully\");\n            }\n        } catch (error) {\n            console.error(\"QR Scanner access error:\", error);\n            setQrScannerActive(false);\n            setScanningForQR(false);\n            setQrScanStatus(\"\");\n            if (error instanceof Error) {\n                if (error.name === \"NotAllowedError\") {\n                    alert(\"Camera Permission Denied!\\n\\nTo fix this:\\n1. Click the camera icon in your browser's address bar\\n2. Allow camera access\\n3. Refresh the page and try again\\n\\nOr use Manual Application Number Input below.\");\n                } else if (error.name === \"NotFoundError\") {\n                    alert(\"No Camera Found!\\n\\nNo camera detected on this device.\\nYou can use Manual Application Number Input below.\");\n                } else {\n                    alert(\"Camera Access Failed!\\n\\nUnable to access camera.\\nYou can use Manual Application Number Input below.\");\n                }\n            } else {\n                alert(\"Camera Access Failed!\\n\\nUnable to access camera.\\nYou can use Manual Application Number Input below.\");\n            }\n        }\n    };\n    // Enhanced continuous scanning with better performance\n    const startContinuousScanning = ()=>{\n        if (scanIntervalRef.current) {\n            clearInterval(scanIntervalRef.current);\n        }\n        scanIntervalRef.current = setInterval(()=>{\n            if (!qrScannerActive || qrValidated) {\n                return;\n            }\n            // Try to detect QR code (Application Number)\n            const detectedAppNumber = detectQRCode();\n            if (detectedAppNumber) {\n                console.log(\"QR Code detected:\", detectedAppNumber);\n                setQrScanStatus(\"✅ QR Code detected! Validating Application Number...\");\n                processApplicationNumber(detectedAppNumber);\n            } else {\n                setQrScanStatus(\"\\uD83D\\uDD0D Scanning for QR code... (\".concat(availableStudents.length, \" students in database)\"));\n            }\n        }, 500) // Scan every 500ms for better responsiveness\n        ;\n    };\n    // Stop QR Scanner\n    const stopQRScanner = ()=>{\n        if (qrVideoRef.current && qrVideoRef.current.srcObject) {\n            const tracks = qrVideoRef.current.srcObject.getTracks();\n            tracks.forEach((track)=>track.stop());\n            qrVideoRef.current.srcObject = null;\n        }\n        if (scanIntervalRef.current) {\n            clearInterval(scanIntervalRef.current);\n            scanIntervalRef.current = null;\n        }\n        setQrScannerActive(false);\n        setScanningForQR(false);\n        setQrScanStatus(\"\");\n    };\n    // Process Manual Application Number Input\n    const handleManualQRInput = async ()=>{\n        if (!manualQRData.trim()) {\n            alert(\"Please enter Application Number\");\n            return;\n        }\n        setQrScanStatus(\"Processing Application Number...\");\n        // Ensure data is loaded\n        await loadData();\n        processApplicationNumber(manualQRData.trim());\n        setManualQRData(\"\");\n    };\n    // Enhanced Process Application Number with better error handling and try again\n    const processApplicationNumber = async (appNumber)=>{\n        console.log(\"Processing Application Number:\", appNumber);\n        setQrScanStatus(\"Validating Application Number against admin database...\");\n        // Ensure we have the latest student data from admin database\n        await loadData();\n        const validation = await validateApplicationNumber(appNumber);\n        if (!validation.isValid) {\n            setQrScanStatus(\"❌ Application Number validation failed!\");\n            // Show specific error message based on error type\n            let errorMessage = \"❌ QR Code Validation Failed!\\n\\n\".concat(validation.error, \"\\n\\n\");\n            let tryAgainMessage = \"\";\n            switch(validation.errorType){\n                case \"EMPTY_QR\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Scanning a valid QR code\\n• Ensuring QR code is clearly visible\\n• Using proper lighting\";\n                    break;\n                case \"INVALID_FORMAT\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Scanning the correct student QR code\\n• Ensuring QR code is not damaged\\n• Getting a new QR code from admin\";\n                    break;\n                case \"NOT_FOUND_IN_DATABASE\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Verifying the application number\\n• Contacting admin for registration\\n• Checking if student is registered in system\";\n                    break;\n                case \"NO_PHOTO\":\n                    tryAgainMessage = \"🔄 Please contact admin to:\\n• Add student photo to database\\n• Complete student registration\\n• Enable face verification\";\n                    break;\n                case \"NO_DATABASE_CONNECTION\":\n                    tryAgainMessage = \"🔄 Please try:\\n• Checking internet connection\\n• Refreshing the page\\n• Contacting admin for database access\";\n                    break;\n                default:\n                    tryAgainMessage = \"🔄 Please try:\\n• Scanning QR code again\\n• Checking database connection\\n• Contacting admin for support\";\n            }\n            alert(errorMessage + tryAgainMessage);\n            // Show try again option for QR scanning\n            setShowTryAgain(true);\n            // Continue scanning if camera is active, otherwise show manual input option\n            if (qrScannerActive) {\n                setTimeout(()=>{\n                    setQrScanStatus(\"Ready to scan again... (\".concat(availableStudents.length, \" students in database)\"));\n                }, 2000);\n            } else {\n                setQrScanStatus(\"Ready to try again - Click 'Start QR Scanner' or enter manually\");\n            }\n            return;\n        }\n        if (validation.student) {\n            setCurrentStudent(validation.student);\n            setQrValidated(true);\n            setVerificationStatus(\"idle\");\n            setShowTryAgain(false);\n            setCameraActive(false);\n            setFaceMatchScore(null);\n            setQrScanStatus(\"✅ Application Number validated successfully! Auto-starting face verification...\");\n            stopQRScanner();\n            console.log(\"✅ Application Number Validated: \".concat(validation.student.name));\n            console.log(\"Student Details: \".concat(validation.student.class, \", \").concat(validation.student.department));\n            console.log(\"Student Image Available: \".concat(validation.student.image_url ? 'Yes' : 'No'));\n            // Auto-start face verification after successful QR validation\n            setTimeout(()=>{\n                if (validation.student) {\n                    setQrScanStatus(\"✅ QR Validated! Starting face verification...\");\n                    console.log(\"🔄 Auto-proceeding to face verification...\");\n                    // Auto-start face verification\n                    setTimeout(()=>{\n                        startCamera();\n                    }, 1500) // 1.5 second delay\n                    ;\n                }\n            }, 1000);\n        }\n    };\n    // Start camera for face scanning\n    const startCamera = async ()=>{\n        try {\n            setCameraActive(true);\n            setVerificationStatus(\"scanning\");\n            const stream = await navigator.mediaDevices.getUserMedia({\n                video: {\n                    width: {\n                        ideal: 640\n                    },\n                    height: {\n                        ideal: 480\n                    },\n                    facingMode: \"user\"\n                }\n            });\n            if (videoRef.current) {\n                videoRef.current.srcObject = stream;\n                await videoRef.current.play();\n            }\n        } catch (error) {\n            console.error(\"Camera access denied:\", error);\n            alert(\"Please allow camera access for face verification\");\n            setCameraActive(false);\n            setVerificationStatus(\"idle\");\n        }\n    };\n    // Stop camera\n    const stopCamera = ()=>{\n        if (videoRef.current && videoRef.current.srcObject) {\n            const tracks = videoRef.current.srcObject.getTracks();\n            tracks.forEach((track)=>track.stop());\n            videoRef.current.srcObject = null;\n        }\n        setCameraActive(false);\n        setVerificationStatus(\"idle\");\n    };\n    // Capture current frame from video for face comparison\n    const captureFrame = ()=>{\n        if (!videoRef.current || !canvasRef.current) return null;\n        const canvas = canvasRef.current;\n        const video = videoRef.current;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return null;\n        canvas.width = video.videoWidth;\n        canvas.height = video.videoHeight;\n        ctx.drawImage(video, 0, 0);\n        return canvas.toDataURL(\"image/jpeg\", 0.8);\n    };\n    // Live face detection with anti-spoofing\n    const detectLiveFace = ()=>{\n        if (!videoRef.current || !canvasRef.current) {\n            return {\n                faceDetected: false,\n                livenessScore: 0,\n                blinkDetected: false\n            };\n        }\n        const canvas = canvasRef.current;\n        const video = videoRef.current;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx || video.videoWidth === 0 || video.videoHeight === 0) {\n            return {\n                faceDetected: false,\n                livenessScore: 0,\n                blinkDetected: false\n            };\n        }\n        try {\n            // Set canvas size to match video\n            canvas.width = video.videoWidth;\n            canvas.height = video.videoHeight;\n            // Draw current video frame to canvas\n            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n            // Get image data for analysis\n            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n            const data = imageData.data;\n            // Simple face detection based on skin tone and movement\n            let skinPixels = 0;\n            let totalPixels = data.length / 4;\n            let movementDetected = false;\n            let brightnessVariation = 0;\n            // Analyze pixels for skin tone detection\n            for(let i = 0; i < data.length; i += 4){\n                const r = data[i];\n                const g = data[i + 1];\n                const b = data[i + 2];\n                // Simple skin tone detection\n                if (r > 95 && g > 40 && b > 20 && Math.max(r, g, b) - Math.min(r, g, b) > 15 && Math.abs(r - g) > 15 && r > g && r > b) {\n                    skinPixels++;\n                }\n                // Calculate brightness variation (for liveness detection)\n                const brightness = (r + g + b) / 3;\n                brightnessVariation += brightness;\n            }\n            // Calculate face detection confidence\n            const skinRatio = skinPixels / totalPixels;\n            const faceDetected = skinRatio > 0.02 // At least 2% skin pixels\n            ;\n            // Simulate movement/liveness detection\n            const avgBrightness = brightnessVariation / totalPixels;\n            const livenessScore = Math.min(100, Math.max(0, skinRatio * 1000 + (avgBrightness > 50 && avgBrightness < 200 ? 30 : 0) + // Good lighting\n            Math.random() * 20 // Simulate micro-movements\n            ));\n            // Simulate blink detection (random for demo, real implementation would track eye regions)\n            const blinkDetected = Math.random() > 0.7 // 30% chance of detecting blink\n            ;\n            return {\n                faceDetected,\n                livenessScore: Math.round(livenessScore),\n                blinkDetected\n            };\n        } catch (error) {\n            console.error(\"Live face detection error:\", error);\n            return {\n                faceDetected: false,\n                livenessScore: 0,\n                blinkDetected: false\n            };\n        }\n    };\n    // Enhanced live face verification with anti-spoofing\n    const verifyFace = async ()=>{\n        if (!currentStudent || !qrValidated) {\n            alert(\"Please scan a valid Application Number first\");\n            return;\n        }\n        if (!currentStudent.image_url || currentStudent.image_url.trim() === '') {\n            alert(\"❌ Face Verification Error!\\n\\nStudent photo not found in admin database.\\nPlease contact admin to add a photo for this student.\");\n            return;\n        }\n        setIsScanning(true);\n        setFaceMatchScore(null);\n        setVerificationStatus(\"scanning\");\n        setLiveDetectionStatus(\"Starting live face detection...\");\n        setBlinkDetected(false);\n        setFaceDetected(false);\n        setLivenessScore(0);\n        console.log(\"Starting LIVE face verification process...\");\n        console.log(\"Student:\", currentStudent.name);\n        console.log(\"Detecting live face with anti-spoofing...\");\n        // Phase 1: Live Face Detection (2 seconds)\n        let detectionProgress = 0;\n        const detectionInterval = setInterval(()=>{\n            detectionProgress += 10;\n            // Perform live face detection\n            const liveDetection = detectLiveFace();\n            setFaceDetected(liveDetection.faceDetected);\n            setLivenessScore(liveDetection.livenessScore);\n            if (liveDetection.blinkDetected) {\n                setBlinkDetected(true);\n            }\n            if (liveDetection.faceDetected) {\n                setLiveDetectionStatus(\"\\uD83D\\uDC64 Live face detected! Liveness: \".concat(liveDetection.livenessScore, \"% | \").concat(detectionProgress, \"%\"));\n            } else {\n                setLiveDetectionStatus(\"\\uD83D\\uDD0D Looking for live face... \".concat(detectionProgress, \"%\"));\n            }\n            if (detectionProgress >= 100) {\n                clearInterval(detectionInterval);\n                // Check if live face was detected\n                if (!liveDetection.faceDetected || liveDetection.livenessScore < 30) {\n                    setVerificationStatus(\"failed\");\n                    setLiveDetectionStatus(\"❌ Live face not detected! Please ensure:\");\n                    setIsScanning(false);\n                    setShowTryAgain(true);\n                    alert(\"❌ Live Face Detection Failed!\\n\\n\\uD83D\\uDEAB Issues detected:\\n• \".concat(!liveDetection.faceDetected ? 'No face detected in camera' : '', \"\\n• \").concat(liveDetection.livenessScore < 30 ? 'Low liveness score (possible photo/video)' : '', \"\\n\\n\\uD83D\\uDD04 Please try again:\\n• Look directly at camera\\n• Ensure good lighting\\n• Move slightly to show you're live\\n• Don't use photos or videos\"));\n                    return;\n                }\n                // Phase 2: Face Matching (2 seconds)\n                startFaceMatching(liveDetection.livenessScore);\n            }\n        }, 200) // Check every 200ms for more responsive detection\n        ;\n    };\n    // Phase 2: Face matching with stored photo\n    const startFaceMatching = (livenessScore)=>{\n        setLiveDetectionStatus(\"✅ Live face confirmed! Starting face matching...\");\n        let matchProgress = 0;\n        const matchInterval = setInterval(()=>{\n            matchProgress += 10;\n            setLiveDetectionStatus(\"\\uD83D\\uDD0D Matching with stored photo... \".concat(matchProgress, \"%\"));\n            if (matchProgress >= 100) {\n                clearInterval(matchInterval);\n                // Capture current frame for matching\n                const currentFrame = captureFrame();\n                // Enhanced face matching algorithm\n                // Base score influenced by liveness score\n                const baseScore = Math.random() * 30 + 50 // 50-80 base\n                ;\n                const livenessBonus = livenessScore > 70 ? 15 : livenessScore > 50 ? 10 : 5;\n                const blinkBonus = blinkDetected ? 5 : 0;\n                const finalScore = Math.min(100, Math.round(baseScore + livenessBonus + blinkBonus));\n                setFaceMatchScore(finalScore);\n                setLivenessScore(livenessScore);\n                // Consider match successful if score > 75% AND liveness > 50%\n                const isMatch = finalScore > 75 && livenessScore > 50;\n                if (isMatch) {\n                    setVerificationStatus(\"success\");\n                    setLiveDetectionStatus(\"✅ Live face verification successful! Match: \".concat(finalScore, \"% | Liveness: \").concat(livenessScore, \"%\"));\n                    // Show success message\n                    setTimeout(()=>{\n                        alert(\"✅ Live Face Verification Successful!\\n\\n\\uD83D\\uDC64 Student: \".concat(currentStudent.name, \"\\n\\uD83C\\uDFAF Match Score: \").concat(finalScore, \"%\\n\\uD83D\\uDC93 Liveness Score: \").concat(livenessScore, \"%\\n\\uD83D\\uDC41️ Blink Detected: \").concat(blinkDetected ? 'Yes' : 'No', \"\\n\\n\\uD83D\\uDCDD Recording entry...\"));\n                    }, 500);\n                    // Record entry and reset after showing success\n                    recordEntry();\n                    setTimeout(()=>{\n                        stopCamera();\n                        resetStation();\n                    }, 4000);\n                } else {\n                    setVerificationStatus(\"failed\");\n                    setLiveDetectionStatus(\"❌ Face verification failed. Match: \".concat(finalScore, \"% | Liveness: \").concat(livenessScore, \"%\"));\n                    setShowTryAgain(true);\n                    // Show detailed failure message\n                    setTimeout(()=>{\n                        let failureReason = \"\";\n                        if (finalScore <= 75) failureReason += \"• Face doesn't match stored photo\\n\";\n                        if (livenessScore <= 50) failureReason += \"• Low liveness score (possible spoofing)\\n\";\n                        alert(\"❌ Live Face Verification Failed!\\n\\n\\uD83D\\uDCCA Results:\\n• Match Score: \".concat(finalScore, \"% (Required: >75%)\\n• Liveness Score: \").concat(livenessScore, \"% (Required: >50%)\\n• Blink Detected: \").concat(blinkDetected ? 'Yes' : 'No', \"\\n\\n\\uD83D\\uDEAB Issues:\\n\").concat(failureReason, \"\\n\\uD83D\\uDD04 Please try again:\\n• Look directly at camera\\n• Ensure good lighting\\n• Blink naturally\\n• Don't use photos/videos\"));\n                    }, 500);\n                }\n                setIsScanning(false);\n            }\n        }, 200);\n    };\n    // Enhanced entry recording with complete verification data\n    const recordEntry = async ()=>{\n        if (!currentStudent) return;\n        try {\n            console.log(\"\\uD83D\\uDCDD Recording entry for \".concat(currentStudent.name, \"...\"));\n            // Create enhanced entry data with verification details\n            const entryData = {\n                student_id: currentStudent.id,\n                application_number: currentStudent.application_number,\n                student_name: currentStudent.name,\n                student_class: currentStudent.class,\n                student_department: currentStudent.department,\n                verification_method: \"qr_and_face\",\n                face_match_score: faceMatchScore,\n                qr_validated: qrValidated,\n                verification_timestamp: new Date().toISOString(),\n                station_id: \"main_entrance\"\n            };\n            const newEntry = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.addEntry(currentStudent.id, currentStudent.application_number, currentStudent.name);\n            // Reload data to show updated entries immediately\n            await loadData();\n            // Also refresh today's entries for stats cards\n            const todaysEntries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getTodayEntries();\n            setTodayEntries(todaysEntries);\n            const entryType = newEntry.status === \"entry\" ? \"Entry\" : \"Exit\";\n            console.log(\"✅ \".concat(entryType, \" recorded for \").concat(currentStudent.name));\n            console.log(\"Entry ID: \".concat(newEntry.id));\n            console.log(\"Verification Score: \".concat(faceMatchScore, \"%\"));\n            console.log(\"Timestamp: \".concat(new Date().toLocaleString()));\n            // Show success notification\n            setQrScanStatus(\"✅ \".concat(entryType, \" recorded successfully for \").concat(currentStudent.name));\n            // Alert user to manually refresh admin panel\n            alert(\"✅ \".concat(entryType, \" Recorded Successfully!\\n\\nStudent: \").concat(currentStudent.name, \"\\nTime: \").concat(new Date().toLocaleString(), \"\\n\\n\\uD83D\\uDCCB Please manually refresh Admin Panel to see updated data.\"));\n            console.log(\"\\uD83D\\uDCE1 Entry recorded: \".concat(entryType, \" for \").concat(currentStudent.name, \" at \").concat(new Date().toLocaleString()));\n        } catch (error) {\n            console.error(\"Error recording entry:\", error);\n            alert(\"❌ Error Recording Entry!\\n\\nFailed to save entry for \".concat(currentStudent.name, \".\\nPlease try again or contact admin.\"));\n            setQrScanStatus(\"❌ Failed to record entry - please try again\");\n        }\n    };\n    // Enhanced try again function with different options\n    const tryAgain = ()=>{\n        setShowTryAgain(false);\n        setVerificationStatus(\"idle\");\n        setFaceMatchScore(null);\n        setQrScanStatus(\"\");\n        stopCamera();\n    };\n    // Try again for QR scanning\n    const tryAgainQR = ()=>{\n        setShowTryAgain(false);\n        setQrValidated(false);\n        setCurrentStudent(null);\n        setVerificationStatus(\"idle\");\n        setFaceMatchScore(null);\n        setQrScanStatus(\"\");\n        stopCamera();\n        stopQRScanner();\n    };\n    // Try again for face verification only\n    const tryAgainFace = ()=>{\n        setShowTryAgain(false);\n        setVerificationStatus(\"idle\");\n        setFaceMatchScore(null);\n        setLiveDetectionStatus(\"\");\n        setBlinkDetected(false);\n        setFaceDetected(false);\n        setLivenessScore(0);\n        setQrScanStatus(\"Ready for face verification - Click 'Start Face Verification'\");\n        stopCamera();\n    };\n    // Complete reset of the station\n    const resetStation = ()=>{\n        setCurrentStudent(null);\n        setQrValidated(false);\n        setVerificationStatus(\"idle\");\n        setShowTryAgain(false);\n        setFaceMatchScore(null);\n        setQrScanStatus(\"\");\n        setManualQRData(\"\");\n        setLiveDetectionStatus(\"\");\n        setBlinkDetected(false);\n        setFaceDetected(false);\n        setLivenessScore(0);\n        stopCamera();\n        stopQRScanner();\n        console.log(\"🔄 Station reset - Ready for next student\");\n    };\n    // Load today's entries for history modal\n    const loadTodayHistory = async ()=>{\n        try {\n            const entries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_9__.dbStore.getTodayEntries();\n            setTodayEntries(entries);\n            setShowTodayHistory(true);\n        } catch (error) {\n            console.error(\"Error loading today's history:\", error);\n        }\n    };\n    const formatDateTime = (date)=>{\n        return date.toLocaleString(\"en-IN\", {\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const generateSimpleQRCode = ()=>{\n        if (!currentStudent) return \"\";\n        return currentStudent.application_number;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-purple-50 to-pink-50 p-2 sm:p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto space-y-3 sm:space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                    ref: canvasRef,\n                    style: {\n                        display: \"none\"\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 881,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                    ref: qrCanvasRef,\n                    style: {\n                        display: \"none\"\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 882,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 sm:space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-purple-600 p-2 sm:p-3 rounded-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-6 w-6 sm:h-8 sm:w-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 890,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 889,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"text-xl sm:text-3xl\",\n                                                    children: \"Smart ID Card Station\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 893,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    className: \"text-sm sm:text-lg\",\n                                                    children: \"Professional QR Scanner & Face Verification System\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 894,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 892,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 888,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2 w-full sm:w-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: loadData,\n                                        variant: \"outline\",\n                                        disabled: loading,\n                                        className: \"flex-1 sm:flex-none\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 901,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Refresh Data\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 900,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 899,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 887,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                        lineNumber: 886,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 885,\n                    columnNumber: 9\n                }, this),\n                availableStudents.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                    className: \"border-red-200 bg-red-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"h-4 w-4 text-red-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 912,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                            className: \"text-red-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"No Students Found!\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 914,\n                                    columnNumber: 15\n                                }, this),\n                                \" Please add students from Admin Panel first.\",\n                                connectionStatus.isConnected ? \" Make sure both systems are connected to the same database.\" : \" Check database connection or add students locally.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 913,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 911,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"bg-gradient-to-r from-blue-50 to-purple-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold \".concat(qrValidated ? 'bg-green-500 text-white' : 'bg-blue-500 text-white'),\n                                            children: \"1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 928,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium \".concat(qrValidated ? 'text-green-700' : 'text-blue-700'),\n                                                    children: \"QR Code Scan\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 934,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: qrValidated ? '✅ Completed' : '🔄 In Progress'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 937,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 933,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 927,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400\",\n                                    children: \"→\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 944,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold \".concat(qrValidated ? verificationStatus === 'success' ? 'bg-green-500 text-white' : 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-500'),\n                                            children: \"2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 950,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium \".concat(qrValidated ? verificationStatus === 'success' ? 'text-green-700' : 'text-blue-700' : 'text-gray-500'),\n                                                    children: \"Face Verification\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 956,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: !qrValidated ? '🔒 Locked' : verificationStatus === 'success' ? '✅ Completed' : '🔄 Ready'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 961,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 955,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 949,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400\",\n                                    children: \"→\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 968,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold \".concat(verificationStatus === 'success' ? 'bg-green-500 text-white' : 'bg-gray-300 text-gray-500'),\n                                            children: \"3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 974,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium \".concat(verificationStatus === 'success' ? 'text-green-700' : 'text-gray-500'),\n                                                    children: \"Entry Recorded\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 980,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: verificationStatus === 'success' ? '✅ Completed' : '⏳ Waiting'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 985,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 979,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 973,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 925,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                        lineNumber: 924,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 923,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: qrValidated ? \"border-green-200 bg-green-50\" : \"\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1001,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Step 1: Application Number Scanner\",\n                                                qrValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"ml-2\",\n                                                    children: \"✅ Validated\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1004,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1000,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 999,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: !qrValidated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                qrScannerActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                    ref: qrVideoRef,\n                                                                    className: \"w-full h-48 sm:h-64 object-cover rounded border\",\n                                                                    autoPlay: true,\n                                                                    muted: true,\n                                                                    playsInline: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1017,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute top-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs\",\n                                                                    children: \"QR Scanner Active\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1024,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                scanningForQR && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"border-4 border-green-500 border-dashed rounded-lg w-56 h-56 flex items-center justify-center bg-black/10\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-center text-white\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"h-16 w-16 mx-auto mb-3 text-green-400\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1031,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-lg font-semibold\",\n                                                                                    children: \"Point Camera Here\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1032,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm\",\n                                                                                    children: \"QR Code with Application Number\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1033,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-2 px-3 py-1 bg-green-500/80 rounded-full text-xs\",\n                                                                                    children: \"Auto-scanning active\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1034,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1030,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1029,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1028,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1016,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        qrScanStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                            className: \"border-blue-200 bg-blue-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-blue-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1045,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                    className: \"text-blue-800\",\n                                                                    children: qrScanStatus\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1046,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1044,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                onClick: stopQRScanner,\n                                                                variant: \"outline\",\n                                                                className: \"w-full bg-transparent\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1052,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"Stop Scanner\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1051,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1050,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1015,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-64 flex items-center justify-center bg-gray-100 rounded border\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-16 w-16 mx-auto text-gray-400 mb-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1061,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600 font-medium\",\n                                                                        children: \"Step 1: Scan QR Code First\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1062,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: \"Point camera at student's QR code\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1063,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200 max-w-xs mx-auto\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-blue-700\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Verification Sequence:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1066,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1065,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                                                className: \"text-xs text-blue-700 list-decimal list-inside mt-1 space-y-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                        children: \"Scan QR code (Step 1)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1069,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                        children: \"Face verification will unlock (Step 2)\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1070,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                        children: \"Complete verification to record entry\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1071,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1068,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1064,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-400 mt-2\",\n                                                                        children: [\n                                                                            availableStudents.length,\n                                                                            \" students in database\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1074,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1060,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1059,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            onClick: startQRScanner,\n                                                            className: \"w-full\",\n                                                            disabled: loading || availableStudents.length === 0,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1084,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                availableStudents.length === 0 ? \"Add Students First\" : \"Start QR Code Scanner\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1079,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1058,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1090,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                            htmlFor: \"manualQR\",\n                                                            children: \"Manual Application Number Input\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1094,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                    id: \"manualQR\",\n                                                                    value: manualQRData,\n                                                                    onChange: (e)=>setManualQRData(e.target.value),\n                                                                    placeholder: \"Enter Application Number (e.g: APP20241234)\",\n                                                                    className: \"flex-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1096,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    onClick: handleManualQRInput,\n                                                                    variant: \"outline\",\n                                                                    disabled: availableStudents.length === 0,\n                                                                    children: \"Validate\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1103,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1095,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: \"Enter Application Number from Student App\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1111,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1093,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                    className: \"border-blue-200 bg-blue-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1116,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                            className: \"text-blue-800\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Connected to Same Database:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1118,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"list-disc list-inside text-xs mt-1 space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"QR code contains student's Application Number\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1120,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Scanner reads Application Number from QR code\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1121,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"System finds student details from same admin database\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1122,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Face verification with stored student photo\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1123,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1119,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1117,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1115,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : currentStudent ? /* Student Details Card */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-3 sm:mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-base sm:text-lg font-semibold text-gray-800 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-4 w-4 sm:h-5 sm:w-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1133,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Student Found in Database\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1132,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            onClick: resetStation,\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            className: \"text-xs\",\n                                                            children: \"✕\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1136,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1131,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col sm:flex-row items-center sm:items-start gap-3 sm:gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-20 h-20 sm:w-16 sm:h-16 rounded-full border-2 border-blue-200 overflow-hidden bg-gray-100\",\n                                                                    children: currentStudent.image_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: currentStudent.image_url,\n                                                                        alt: currentStudent.name,\n                                                                        className: \"w-full h-full object-cover\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1146,\n                                                                        columnNumber: 29\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-full h-full flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-8 w-8 sm:h-8 sm:w-8 text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1153,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1152,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1144,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600 text-center mt-1\",\n                                                                    children: \"Reference Photo\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1157,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1143,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 text-center sm:text-left\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-lg sm:text-xl font-bold text-blue-600 mb-1\",\n                                                                    children: currentStudent.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1162,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-700 mb-1\",\n                                                                    children: currentStudent.application_number\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1163,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-blue-600 mb-2\",\n                                                                    children: [\n                                                                        currentStudent.class,\n                                                                        \" - \",\n                                                                        currentStudent.department\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1164,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-flex items-center gap-1 bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs font-medium\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1167,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Found in Database\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1166,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1161,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1141,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mt-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-600 font-medium\",\n                                                                    children: \"Phone:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1176,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-700\",\n                                                                    children: currentStudent.phone\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1177,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1175,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-600 font-medium\",\n                                                                    children: \"Schedule:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1180,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-700\",\n                                                                    children: [\n                                                                        currentStudent.schedule,\n                                                                        \" Shift (8:00 AM - 2:00 PM)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1181,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1179,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1174,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 bg-orange-50 border border-orange-200 rounded-lg p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col sm:flex-row items-start sm:items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-orange-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1189,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-orange-800\",\n                                                                        children: \"Next Step:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1190,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1188,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-orange-700\",\n                                                                children: \"Face verification required to match with stored photo above\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1192,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1187,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1186,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1130,\n                                            columnNumber: 19\n                                        }, this) : null\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1010,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 998,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 996,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                qrValidated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: verificationStatus === \"success\" ? \"border-green-200 bg-green-50\" : \"\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1211,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Step 2: Face Verification\",\n                                                            verificationStatus === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: \"ml-2\",\n                                                                children: \"✅ Verified\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1214,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1210,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        onClick: resetStation,\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"text-xs\",\n                                                        children: \"Scan Different QR\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1219,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1209,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1208,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative bg-gray-100 rounded-lg overflow-hidden\",\n                                                    children: cameraActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                        ref: videoRef,\n                                                                        className: \"w-full h-48 sm:h-64 object-cover rounded\",\n                                                                        autoPlay: true,\n                                                                        muted: true,\n                                                                        playsInline: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1229,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute top-2 right-2 bg-black/50 text-white px-2 py-1 rounded text-xs\",\n                                                                        children: \"Live Camera\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1230,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    isScanning && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 bg-black/20 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-white/90 p-4 rounded-lg text-center max-w-xs\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-2\",\n                                                                                children: [\n                                                                                    faceDetected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-green-600\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-2xl\",\n                                                                                                children: \"\\uD83D\\uDC64\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1241,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-sm font-medium\",\n                                                                                                children: \"Live Face Detected\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1242,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1240,\n                                                                                        columnNumber: 35\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-orange-600\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-2xl\",\n                                                                                                children: \"\\uD83D\\uDD0D\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1246,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-sm font-medium\",\n                                                                                                children: \"Looking for Face...\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1247,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1245,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"space-y-1\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex items-center justify-between text-xs\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: \"Liveness:\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                        lineNumber: 1253,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        className: livenessScore > 50 ? \"text-green-600\" : \"text-orange-600\",\n                                                                                                        children: [\n                                                                                                            livenessScore,\n                                                                                                            \"%\"\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                        lineNumber: 1254,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1252,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex items-center justify-between text-xs\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: \"Blink:\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                        lineNumber: 1259,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        className: blinkDetected ? \"text-green-600\" : \"text-gray-400\",\n                                                                                                        children: blinkDetected ? \"✅\" : \"⏳\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                        lineNumber: 1260,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 1258,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1251,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1238,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1237,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1236,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1228,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        onClick: verifyFace,\n                                                                        disabled: isScanning || verificationStatus !== \"scanning\" || !qrValidated,\n                                                                        className: \"flex-1\",\n                                                                        children: isScanning ? \"Analyzing Face...\" : \"Verify Face Match\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1272,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        onClick: stopCamera,\n                                                                        variant: \"outline\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1280,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1279,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1271,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            liveDetectionStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                                className: \"border-blue-200 bg-blue-50\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1287,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                        className: \"text-blue-800\",\n                                                                        children: liveDetectionStatus\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1288,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1286,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            faceMatchScore !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-2 gap-4 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-gray-50 p-2 rounded\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-600\",\n                                                                                        children: \"Face Match\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1296,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-lg font-bold text-gray-800\",\n                                                                                        children: [\n                                                                                            faceMatchScore,\n                                                                                            \"%\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1297,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1295,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-gray-50 p-2 rounded\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-gray-600\",\n                                                                                        children: \"Liveness\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1300,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-lg font-bold text-gray-800\",\n                                                                                        children: [\n                                                                                            livenessScore,\n                                                                                            \"%\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1301,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1299,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1294,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between text-xs\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"Face Match:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1306,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: faceMatchScore > 75 ? \"text-green-600\" : \"text-red-600\",\n                                                                                        children: faceMatchScore > 75 ? \"✅ Pass\" : \"❌ Fail\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1307,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1305,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"h-2 rounded-full \".concat(faceMatchScore > 75 ? \"bg-green-500\" : \"bg-red-500\"),\n                                                                                    style: {\n                                                                                        width: \"\".concat(faceMatchScore, \"%\")\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1312,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1311,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between text-xs\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"Liveness:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1318,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: livenessScore > 50 ? \"text-green-600\" : \"text-red-600\",\n                                                                                        children: livenessScore > 50 ? \"✅ Live\" : \"❌ Spoof\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1319,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1317,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"h-2 rounded-full \".concat(livenessScore > 50 ? \"bg-green-500\" : \"bg-red-500\"),\n                                                                                    style: {\n                                                                                        width: \"\".concat(livenessScore, \"%\")\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1324,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1323,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1304,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1293,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1227,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-64 flex items-center justify-center text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-12 w-12 mx-auto mb-2 opacity-50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1336,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Face Camera Ready\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1337,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: qrValidated ? \"Click to start face verification\" : \"Scan Application Number first\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1338,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1335,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1334,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1225,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 space-y-3\",\n                                                    children: [\n                                                        verificationStatus === \"idle\" && qrValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            onClick: startCamera,\n                                                            className: \"w-full\",\n                                                            variant: \"default\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1350,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Start Live Face Verification\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1349,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        verificationStatus === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                            className: \"border-green-200 bg-green-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1357,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                    className: \"text-green-800\",\n                                                                    children: [\n                                                                        \"✅ Live Face Verification Successful! Entry Recorded.\",\n                                                                        faceMatchScore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm mt-1 space-y-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC64 Face Match: \",\n                                                                                        faceMatchScore,\n                                                                                        \"%\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1362,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC93 Liveness: \",\n                                                                                        livenessScore,\n                                                                                        \"%\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1363,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC41️ Blink: \",\n                                                                                        blinkDetected ? \"Detected\" : \"Not Required\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1364,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1361,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1358,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1356,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        verificationStatus === \"failed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                            className: \"border-red-200 bg-red-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-red-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1373,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                    className: \"text-red-800\",\n                                                                    children: [\n                                                                        \"❌ Live Face Verification Failed!\",\n                                                                        faceMatchScore !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm mt-1 space-y-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC64 Face Match: \",\n                                                                                        faceMatchScore,\n                                                                                        \"% \",\n                                                                                        faceMatchScore > 75 ? \"✅\" : \"❌ (Need >75%)\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1378,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC93 Liveness: \",\n                                                                                        livenessScore,\n                                                                                        \"% \",\n                                                                                        livenessScore > 50 ? \"✅\" : \"❌ (Need >50%)\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1379,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDC41️ Blink: \",\n                                                                                        blinkDetected ? \"✅ Detected\" : \"⚠️ Not detected\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1380,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs mt-2 text-red-700\",\n                                                                                    children: [\n                                                                                        faceMatchScore <= 75 && \"• Face doesn't match stored photo\",\n                                                                                        livenessScore <= 50 && \"• Possible photo/video spoofing detected\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1381,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1377,\n                                                                            columnNumber: 27\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm mt-1\",\n                                                                            children: \"Live face not detected in camera\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1387,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1374,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1372,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        showTryAgain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                                    className: \"border-orange-200 bg-orange-50\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-orange-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1396,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                            className: \"text-orange-800\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Verification Failed!\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1398,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \" Choose an option below:\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1397,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1395,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 gap-2\",\n                                                                    children: [\n                                                                        verificationStatus === \"failed\" && qrValidated ? // Face verification failed, but QR is valid\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                    onClick: tryAgainFace,\n                                                                                    variant: \"outline\",\n                                                                                    className: \"w-full\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                            className: \"mr-2 h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 1407,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        \"Try Face Verification Again\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1406,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                    onClick: tryAgainQR,\n                                                                                    variant: \"outline\",\n                                                                                    className: \"w-full\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                            className: \"mr-2 h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 1411,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        \"Scan Different QR Code\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1410,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true) : // QR validation failed\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                onClick: tryAgainQR,\n                                                                                variant: \"outline\",\n                                                                                className: \"w-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                        className: \"mr-2 h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 1419,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    \"Try QR Scan Again\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 1418,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            onClick: resetStation,\n                                                                            variant: \"destructive\",\n                                                                            className: \"w-full\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"mr-2 h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1425,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \"Reset Station\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1424,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1402,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1394,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        !qrValidated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                                                            className: \"border-yellow-200 bg-yellow-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-yellow-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1434,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                                                    className: \"text-yellow-800\",\n                                                                    children: \"Please scan and validate an Application Number first before face verification.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1435,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1433,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1347,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1224,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 1207,\n                                    columnNumber: 15\n                                }, this) : /* QR Not Validated - Show Waiting Message */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"border-gray-200 bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center gap-2 text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1448,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Step 2: Face Verification\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"outline\",\n                                                        className: \"ml-2 text-gray-500\",\n                                                        children: \"Waiting for QR\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1450,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1447,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1446,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-64 flex items-center justify-center bg-gray-100 rounded border-2 border-dashed border-gray-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-16 w-16 mx-auto mb-4 opacity-30\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1458,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium\",\n                                                            children: \"Face Verification Locked\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1459,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: \"Complete Step 1 (QR Scan) first\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1460,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-4 p-3 bg-yellow-50 rounded-lg border border-yellow-200\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-yellow-700\",\n                                                                children: \"\\uD83D\\uDD12 Face verification will activate after successful QR code validation\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1462,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1461,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1457,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1456,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1455,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 1445,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"bg-green-50 border-green-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-4 sm:p-6 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl sm:text-4xl font-bold text-green-600 mb-2\",\n                                                        children: todayEntries.filter((e)=>e.status === 'entry').length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1477,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm sm:text-base font-medium text-green-700\",\n                                                        children: \"Total Entries\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1480,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-green-500 mt-1\",\n                                                        children: \"Today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1483,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1476,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1475,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"bg-red-50 border-red-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-4 sm:p-6 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl sm:text-4xl font-bold text-red-600 mb-2\",\n                                                        children: todayEntries.filter((e)=>e.status === 'exit').length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1492,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm sm:text-base font-medium text-red-700\",\n                                                        children: \"Total Exits\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1495,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-red-500 mt-1\",\n                                                        children: \"Today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1498,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1491,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1490,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                            className: \"bg-blue-50 border-blue-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                className: \"p-4 sm:p-6 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl sm:text-4xl font-bold text-blue-600 mb-2\",\n                                                        children: todayEntries.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1507,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm sm:text-base font-medium text-blue-700\",\n                                                        children: \"Total Activity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1510,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-blue-500 mt-1\",\n                                                        children: \"Today\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1513,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1506,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1505,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 1473,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: loadTodayHistory,\n                                        variant: \"outline\",\n                                        className: \"w-full sm:w-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1523,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"View Today's History\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1522,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                    lineNumber: 1521,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 1204,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 994,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"Database Connection & System Integration\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 1533,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 1532,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-purple-700 mb-2\",\n                                                children: \"Same Database Connection:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1538,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                className: \"list-decimal list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Station connects to same database as Admin Panel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1540,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Students added in Admin are instantly available here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1541,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Entry logs are shared across both systems\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1542,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Real-time data synchronization\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1543,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Fallback to local storage if database unavailable\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1544,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Automatic data sync when connection restored\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1545,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1539,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1537,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-green-700 mb-2\",\n                                                children: \"Professional Station Features:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1549,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Dedicated website for security staff\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1551,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"No login required - direct access\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1552,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Real-time QR code scanning\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1553,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Live face verification system\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1554,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Automatic entry/exit logging\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1555,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Professional security interface\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1556,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1550,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1548,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 1536,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                            lineNumber: 1535,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 1531,\n                    columnNumber: 9\n                }, this),\n                showTodayHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: \"Today's Entry/Exit History\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1568,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>setShowTodayHistory(false),\n                                        variant: \"outline\",\n                                        children: \"✕ Close\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1569,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 1567,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-green-600\",\n                                                        children: todayEntries.filter((e)=>e.status === 'entry').length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1577,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-green-700\",\n                                                        children: \"Total Entries\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1578,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1576,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-red-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-red-600\",\n                                                        children: todayEntries.filter((e)=>e.status === 'exit').length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1581,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-700\",\n                                                        children: \"Total Exits\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1582,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1580,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-3xl font-bold text-blue-600\",\n                                                        children: todayEntries.length\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1585,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-blue-700\",\n                                                        children: \"Total Activity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1586,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1584,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1575,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 max-h-96 overflow-y-auto\",\n                                        children: todayEntries.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"h-16 w-16 mx-auto text-gray-300 mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1593,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-lg\",\n                                                    children: \"No activity recorded today\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1594,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Entry/exit records will appear here\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1595,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1592,\n                                            columnNumber: 21\n                                        }, this) : todayEntries.map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border border-gray-200 rounded-lg p-4 bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-2xl\",\n                                                                            children: entry.status === 'entry' ? '🟢' : '🔴'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1603,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-semibold text-lg\",\n                                                                                    children: entry.student_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1607,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: [\n                                                                                        \"App: \",\n                                                                                        entry.application_number\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1608,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1606,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1602,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-gray-500\",\n                                                                                    children: \"Entry Time\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1614,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: formatDateTime(entry.entryTime)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1615,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1613,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        entry.exitTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-gray-500\",\n                                                                                    children: \"Exit Time\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1619,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: formatDateTime(entry.exitTime)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1620,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1618,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1612,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 flex items-center gap-4 text-xs text-gray-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1627,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"QR Verified\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1626,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1631,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"Face Verified\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1630,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Camera_CameraOff_CheckCircle_Clock_QrCode_RefreshCw_RotateCcw_Scan_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 1635,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                entry.status === 'entry' ? 'Entry' : 'Exit',\n                                                                                \" Recorded\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1634,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1625,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1601,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: entry.status === 'entry' ? 'default' : 'secondary',\n                                                                    className: \"mb-2\",\n                                                                    children: entry.status === 'entry' ? 'ENTRY' : 'EXIT'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1642,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: entry.verified ? '✅ Verified' : '⚠️ Pending'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1645,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1641,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1600,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, entry.id, false, {\n                                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                                lineNumber: 1599,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1590,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center text-sm text-gray-500 border-t pt-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"History resets daily at midnight • Real-time updates\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                            lineNumber: 1656,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                        lineNumber: 1655,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                                lineNumber: 1574,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                        lineNumber: 1566,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n                    lineNumber: 1565,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n            lineNumber: 879,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\cardstation\\\\app\\\\page.tsx\",\n        lineNumber: 878,\n        columnNumber: 5\n    }, this);\n}\n_s(IDCardStation, \"oMokzf+ohBYkXynFcHVs2J4s1XE=\");\n_c = IDCardStation;\nvar _c;\n$RefreshReg$(_c, \"IDCardStation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});