import { NextRequest, NextResponse } from "next/server"
import { dbStore } from "@/lib/database-store"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const date = searchParams.get('date')
    
    if (date) {
      // Get entries for specific date
      const entries = await dbStore.getEntriesByDate(date)
      return NextResponse.json(entries)
    } else {
      // Get all entries
      const entries = await dbStore.getAllEntries()
      return NextResponse.json(entries)
    }
  } catch (error) {
    console.error("Error fetching entries:", error)
    return NextResponse.json(
      { error: "Failed to fetch entries" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { student_id, student_name, application_number, status, face_match_score } = body
    
    const entry = await dbStore.recordEntry({
      student_id,
      student_name,
      application_number,
      status,
      face_match_score,
      entryTime: new Date(),
    })
    
    return NextResponse.json(entry)
  } catch (error) {
    console.error("Error recording entry:", error)
    return NextResponse.json(
      { error: "Failed to record entry" },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    
    if (!id) {
      return NextResponse.json(
        { error: "Entry ID is required" },
        { status: 400 }
      )
    }
    
    await dbStore.deleteEntry(id)
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting entry:", error)
    return NextResponse.json(
      { error: "Failed to delete entry" },
      { status: 500 }
    )
  }
}
