hoistPattern:
  - '*'
hoistedDependencies:
  '@mongodb-js/saslprep@1.3.0':
    '@mongodb-js/saslprep': private
  '@types/webidl-conversions@7.0.3':
    '@types/webidl-conversions': private
  '@types/whatwg-url@11.0.5':
    '@types/whatwg-url': private
  bson@6.10.4:
    bson: private
  memory-pager@1.5.0:
    memory-pager: private
  mongodb-connection-string-url@3.0.2:
    mongodb-connection-string-url: private
  punycode@2.3.1:
    punycode: private
  sparse-bitfield@3.0.3:
    sparse-bitfield: private
  tr46@5.1.1:
    tr46: private
  webidl-conversions@7.0.0:
    webidl-conversions: private
  whatwg-url@14.2.0:
    whatwg-url: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.1
pendingBuilds: []
prunedAt: Tue, 08 Jul 2025 11:54:48 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped: []
storeDir: D:\.pnpm-store\v10
virtualStoreDir: D:\idcard\node_modules\.pnpm
virtualStoreDirMaxLength: 60
