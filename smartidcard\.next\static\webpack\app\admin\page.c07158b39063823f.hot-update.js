"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./app/admin/page.tsx":
/*!****************************!*\
  !*** ./app/admin/page.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Search,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Search,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Search,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Search,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Search,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Search,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Search,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Search,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Search,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Search,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Search,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Search,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Search,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Search,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Search,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Camera,Check,Copy,Database,Edit,ImageIcon,LogOut,RefreshCw,Save,Search,Trash2,Upload,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _lib_database_store__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/database-store */ \"(app-pages-browser)/./lib/database-store.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AdminPanel() {\n    _s();\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingStudent, setEditingStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [copiedText, setCopiedText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastUpdated, setLastUpdated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dataSource, setDataSource] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"local\");\n    const [databaseConnected, setDatabaseConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [storageInfo, setStorageInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        mode: \"Local\",\n        studentsCount: 0,\n        entriesCount: 0\n    });\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalStudents: 0,\n        todayEntries: 0,\n        todayExits: 0,\n        totalEntries: 0\n    });\n    const [newStudent, setNewStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        phone: \"\",\n        email: \"\",\n        class: \"\",\n        department: \"\",\n        schedule: \"\",\n        image: \"\"\n    });\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [imagePreview, setImagePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageFile, setImageFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPanel.useEffect\": ()=>{\n            // Check if admin is logged in\n            if (true) {\n                const adminLoggedIn = localStorage.getItem(\"adminLoggedIn\");\n                if (!adminLoggedIn) {\n                    router.push(\"/\");\n                    return;\n                }\n            }\n            setIsAuthenticated(true);\n            checkDatabaseConnection();\n            loadData();\n        }\n    }[\"AdminPanel.useEffect\"], [\n        router\n    ]);\n    // Separate function to refresh only stats (not full page)\n    const refreshStats = async ()=>{\n        try {\n            console.log(\"🔄 Refreshing stats from shared MongoDB database...\");\n            let allEntries = [];\n            let todayEntries = [];\n            let studentsData = [];\n            let dataSource = \"mongodb\";\n            try {\n                // Use local API which connects to shared MongoDB\n                console.log(\"🔍 Fetching from shared MongoDB via local API...\");\n                const [localStudentsRes, localEntriesRes] = await Promise.all([\n                    fetch('/api/students'),\n                    fetch('/api/entries')\n                ]);\n                if (localStudentsRes.ok && localEntriesRes.ok) {\n                    studentsData = await localStudentsRes.json();\n                    allEntries = await localEntriesRes.json();\n                    dataSource = \"mongodb\";\n                    console.log(\"✅ Data fetched from shared MongoDB database\");\n                } else {\n                    throw new Error(\"MongoDB API not available\");\n                }\n            } catch (apiError) {\n                console.log(\"⚠️ API not available, using database store fallback...\");\n                // Fallback to database store\n                allEntries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getAllEntries();\n                todayEntries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getTodayEntries();\n                studentsData = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getStudents();\n                dataSource = \"fallback\";\n            }\n            // Filter today's entries if we got data from API\n            if (dataSource !== \"fallback\") {\n                const today = new Date().toDateString();\n                todayEntries = allEntries.filter((entry)=>{\n                    const entryDate = new Date(entry.entryTime || entry.entry_time).toDateString();\n                    return entryDate === today;\n                });\n            }\n            console.log(\"📊 Raw data:\", {\n                source: dataSource,\n                allEntries: allEntries.length,\n                todayEntries: todayEntries.length,\n                todayEntriesData: todayEntries,\n                students: studentsData.length\n            });\n            // Debug: Show sample entry data\n            if (allEntries.length > 0) {\n                console.log(\"📝 Sample entry:\", allEntries[0]);\n            }\n            if (todayEntries.length > 0) {\n                console.log(\"📅 Sample today entry:\", todayEntries[0]);\n            }\n            const entryCount = todayEntries.filter((e)=>e.status === 'entry').length;\n            const exitCount = todayEntries.filter((e)=>e.status === 'exit').length;\n            setStats({\n                totalStudents: studentsData.length,\n                todayEntries: entryCount,\n                todayExits: exitCount,\n                totalEntries: allEntries.length\n            });\n            setDataSource(dataSource);\n            setLastUpdated(new Date());\n            console.log(\"✅ Stats refreshed:\", {\n                source: dataSource,\n                totalStudents: studentsData.length,\n                todayEntries: entryCount,\n                todayExits: exitCount,\n                totalActivity: entryCount + exitCount,\n                allEntries: allEntries.length\n            });\n        } catch (error) {\n            console.error(\"❌ Error refreshing stats:\", error);\n        }\n    };\n    // Auto-reload only stats every 5 seconds (not full page)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPanel.useEffect\": ()=>{\n            if (!isAuthenticated) return;\n            const interval = setInterval({\n                \"AdminPanel.useEffect.interval\": ()=>{\n                    refreshStats() // Only refresh stats, not full page\n                    ;\n                }\n            }[\"AdminPanel.useEffect.interval\"], 5000) // 5 seconds\n            ;\n            return ({\n                \"AdminPanel.useEffect\": ()=>clearInterval(interval)\n            })[\"AdminPanel.useEffect\"];\n        }\n    }[\"AdminPanel.useEffect\"], [\n        isAuthenticated\n    ]);\n    const checkDatabaseConnection = async ()=>{\n        try {\n            // Check if we can connect to MongoDB via API\n            const studentsRes = await fetch('/api/students');\n            const entriesRes = await fetch('/api/entries');\n            if (studentsRes.ok && entriesRes.ok) {\n                const students = await studentsRes.json();\n                const entries = await entriesRes.json();\n                setDatabaseConnected(true);\n                setStorageInfo({\n                    mode: \"MongoDB Cloud\",\n                    studentsCount: students.length,\n                    entriesCount: entries.length\n                });\n                console.log(\"✅ MongoDB connection verified\");\n            } else {\n                throw new Error(\"API not responding\");\n            }\n        } catch (error) {\n            console.log(\"⚠️ MongoDB not available, using local storage\");\n            setDatabaseConnected(false);\n            const storageInfo = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getStorageInfo();\n            setStorageInfo(storageInfo);\n        }\n    };\n    const loadData = async ()=>{\n        try {\n            setLoading(true);\n            // Load students from local database (for admin management)\n            const studentsData = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getStudents();\n            setStudents(studentsData);\n            // Load stats from cardstation if available\n            await refreshStats();\n            const storageInfo = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.getStorageInfo();\n            setStorageInfo(storageInfo);\n        } catch (error) {\n            console.error(\"Error loading data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleRefresh = ()=>{\n        loadData() // Full page refresh\n        ;\n    };\n    const handleLogout = ()=>{\n        if (true) {\n            localStorage.removeItem(\"adminLoggedIn\");\n            localStorage.removeItem(\"adminUsername\");\n        }\n        router.push(\"/\");\n    };\n    // Filter students based on search query\n    const filteredStudents = students.filter((student)=>student.name.toLowerCase().includes(searchQuery.toLowerCase()) || student.application_number.toLowerCase().includes(searchQuery.toLowerCase()) || student.class.toLowerCase().includes(searchQuery.toLowerCase()) || student.department.toLowerCase().includes(searchQuery.toLowerCase()) || student.phone.includes(searchQuery));\n    // Handle image file selection\n    const handleImageSelect = (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (!file) return;\n        // Validate file type\n        if (!file.type.startsWith(\"image/\")) {\n            alert(\"Please select a valid image file (JPG, PNG, GIF, etc.)\");\n            return;\n        }\n        // Validate file size (max 5MB)\n        if (file.size > 5 * 1024 * 1024) {\n            alert(\"Image size should be less than 5MB\");\n            return;\n        }\n        setImageFile(file);\n        // Create preview\n        const reader = new FileReader();\n        reader.onload = (e)=>{\n            var _e_target;\n            const result = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n            setImagePreview(result);\n            setNewStudent({\n                ...newStudent,\n                image: result\n            });\n        };\n        reader.readAsDataURL(file);\n    };\n    // Remove selected image\n    const removeImage = ()=>{\n        setImageFile(null);\n        setImagePreview(null);\n        setNewStudent({\n            ...newStudent,\n            image: \"\"\n        });\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    // Take photo using camera\n    const takePhoto = async ()=>{\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({\n                video: true\n            });\n            // Create a video element to capture the stream\n            const video = document.createElement(\"video\");\n            video.srcObject = stream;\n            video.autoplay = true;\n            // Create a modal or popup to show camera feed\n            const modal = document.createElement(\"div\");\n            modal.style.cssText = \"\\n        position: fixed;\\n        top: 0;\\n        left: 0;\\n        width: 100%;\\n        height: 100%;\\n        background: rgba(0,0,0,0.8);\\n        display: flex;\\n        justify-content: center;\\n        align-items: center;\\n        z-index: 1000;\\n      \";\n            const container = document.createElement(\"div\");\n            container.style.cssText = \"\\n        background: white;\\n        padding: 20px;\\n        border-radius: 10px;\\n        text-align: center;\\n      \";\n            const canvas = document.createElement(\"canvas\");\n            const captureBtn = document.createElement(\"button\");\n            captureBtn.textContent = \"Capture Photo\";\n            captureBtn.style.cssText = \"\\n        background: #3b82f6;\\n        color: white;\\n        padding: 10px 20px;\\n        border: none;\\n        border-radius: 5px;\\n        margin: 10px;\\n        cursor: pointer;\\n      \";\n            const cancelBtn = document.createElement(\"button\");\n            cancelBtn.textContent = \"Cancel\";\n            cancelBtn.style.cssText = \"\\n        background: #6b7280;\\n        color: white;\\n        padding: 10px 20px;\\n        border: none;\\n        border-radius: 5px;\\n        margin: 10px;\\n        cursor: pointer;\\n      \";\n            container.appendChild(video);\n            container.appendChild(document.createElement(\"br\"));\n            container.appendChild(captureBtn);\n            container.appendChild(cancelBtn);\n            modal.appendChild(container);\n            document.body.appendChild(modal);\n            // Capture photo\n            captureBtn.onclick = ()=>{\n                canvas.width = video.videoWidth;\n                canvas.height = video.videoHeight;\n                const ctx = canvas.getContext(\"2d\");\n                ctx === null || ctx === void 0 ? void 0 : ctx.drawImage(video, 0, 0);\n                const imageData = canvas.toDataURL(\"image/jpeg\", 0.8);\n                setImagePreview(imageData);\n                setNewStudent({\n                    ...newStudent,\n                    image: imageData\n                });\n                // Stop camera and close modal\n                stream.getTracks().forEach((track)=>track.stop());\n                document.body.removeChild(modal);\n            };\n            // Cancel\n            cancelBtn.onclick = ()=>{\n                stream.getTracks().forEach((track)=>track.stop());\n                document.body.removeChild(modal);\n            };\n        } catch (error) {\n            alert(\"Camera access denied or not available\");\n        }\n    };\n    const validateForm = ()=>{\n        if (!newStudent.name.trim()) {\n            alert(\"Student name is required\");\n            return false;\n        }\n        if (!newStudent.phone.trim()) {\n            alert(\"Phone number is required\");\n            return false;\n        }\n        if (newStudent.phone.length !== 10 || !/^\\d+$/.test(newStudent.phone)) {\n            alert(\"Phone number must be exactly 10 digits\");\n            return false;\n        }\n        if (!newStudent.class) {\n            alert(\"Class selection is required\");\n            return false;\n        }\n        if (!newStudent.image) {\n            alert(\"Student photo is required. Please upload an image or take a photo.\");\n            return false;\n        }\n        return true;\n    };\n    const handleAddStudent = async ()=>{\n        if (!validateForm()) return;\n        // Check if phone number already exists\n        const existingStudent = students.find((s)=>s.phone === newStudent.phone);\n        if (existingStudent) {\n            alert(\"Phone number already exists!\");\n            return;\n        }\n        setLoading(true);\n        try {\n            const applicationNumber = _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.generateApplicationNumber();\n            const student = await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.addStudent({\n                ...newStudent,\n                application_number: applicationNumber,\n                image_url: newStudent.image\n            });\n            await loadData();\n            resetForm();\n            alert(\"Student Added Successfully!\\n\\nName: \".concat(student.name, \"\\nApplication Number: \").concat(applicationNumber, \"\\nPhone: \").concat(student.phone, \"\\n\\nPlease provide Application Number and Phone Number to the student for login.\\n\\nData saved in \").concat(storageInfo.mode, \" storage.\"));\n        } catch (error) {\n            alert(\"Error adding student. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleEditStudent = (student)=>{\n        setEditingStudent(student);\n        setNewStudent({\n            name: student.name,\n            phone: student.phone,\n            email: student.email || \"\",\n            class: student.class,\n            department: student.department || \"\",\n            schedule: student.schedule || \"\",\n            image: student.image_url || \"\"\n        });\n        setImagePreview(student.image_url || null);\n        setShowAddForm(false);\n    };\n    const handleUpdateStudent = async ()=>{\n        if (!validateForm() || !editingStudent) return;\n        // Check if phone number already exists (excluding current student)\n        const existingStudent = students.find((s)=>s.phone === newStudent.phone && s.id !== editingStudent.id);\n        if (existingStudent) {\n            alert(\"Phone number already exists!\");\n            return;\n        }\n        setLoading(true);\n        try {\n            await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.updateStudent(editingStudent.id, {\n                name: newStudent.name,\n                phone: newStudent.phone,\n                email: newStudent.email || null,\n                class: newStudent.class,\n                department: newStudent.department || null,\n                schedule: newStudent.schedule || null,\n                image_url: newStudent.image\n            });\n            await loadData();\n            resetForm();\n            alert(\"Student updated successfully!\\n\\nData saved in \".concat(storageInfo.mode, \" storage.\"));\n        } catch (error) {\n            alert(\"Error updating student. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDeleteStudent = async (student)=>{\n        if (confirm(\"Are you sure you want to delete \".concat(student.name, \"?\\n\\nThis action cannot be undone.\"))) {\n            try {\n                setLoading(true);\n                await _lib_database_store__WEBPACK_IMPORTED_MODULE_11__.dbStore.deleteStudent(student.id);\n                await loadData();\n                alert(\"Student deleted successfully!\\n\\nData updated in \".concat(storageInfo.mode, \" storage.\"));\n            } catch (error) {\n                alert(\"Error deleting student. Please try again.\");\n            } finally{\n                setLoading(false);\n            }\n        }\n    };\n    const copyToClipboard = async (text, type)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n            setCopiedText(\"\".concat(type, \"-\").concat(text));\n            setTimeout(()=>setCopiedText(null), 2000);\n        } catch (error) {\n            alert(\"Failed to copy to clipboard\");\n        }\n    };\n    const resetForm = ()=>{\n        setNewStudent({\n            name: \"\",\n            phone: \"\",\n            email: \"\",\n            class: \"\",\n            department: \"\",\n            schedule: \"\",\n            image: \"\"\n        });\n        setImagePreview(null);\n        setImageFile(null);\n        setShowAddForm(false);\n        setEditingStudent(null);\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    const today = new Date().toISOString().slice(0, 10);\n    const totalStudents = students.length;\n    // Replace the following with your actual attendance/logs array if available\n    // For demonstration, using an empty array as placeholder\n    const logs = [] // Replace with actual logs source\n    ;\n    const todaysEntries = logs.filter((e)=>e.type === \"entry\" && e.timestamp.slice(0, 10) === today).length;\n    const todaysExits = logs.filter((e)=>e.type === \"exit\" && e.timestamp.slice(0, 10) === today).length;\n    const totalEntries = logs.filter((e)=>e.type === \"entry\").length;\n    const remainingStudents = totalStudents - todaysExits;\n    if (!isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 544,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-3xl\",\n                                            children: \"Admin Panel\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            className: \"text-lg\",\n                                            children: [\n                                                \"Student Management System - \",\n                                                storageInfo.mode,\n                                                \" Storage\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 554,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleRefresh,\n                                            variant: \"outline\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Refresh\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleLogout,\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Logout\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 560,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 553,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 552,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 551,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.Alert, {\n                    className: databaseConnected ? \"border-green-200 bg-green-50\" : \"border-yellow-200 bg-yellow-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            className: \"h-4 w-4 \".concat(databaseConnected ? \"text-green-600\" : \"text-yellow-600\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 576,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.AlertDescription, {\n                            className: databaseConnected ? \"text-green-800\" : \"text-yellow-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: [\n                                        storageInfo.mode,\n                                        \" Storage Active:\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 578,\n                                    columnNumber: 13\n                                }, this),\n                                \" \",\n                                databaseConnected ? \"Data syncs across all devices automatically\" : \"Data saved locally on this device (\".concat(storageInfo.studentsCount, \" students, \").concat(storageInfo.entriesCount, \" entries)\")\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 577,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 575,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"bg-blue-50 border-blue-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 sm:p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl sm:text-4xl font-bold text-blue-600 mb-2\",\n                                        children: stats.totalStudents\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 590,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm sm:text-base font-medium text-blue-700\",\n                                        children: \"Total Students\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-blue-500 mt-1\",\n                                        children: \"Registered\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 588,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"bg-green-50 border-green-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 sm:p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl sm:text-4xl font-bold text-green-600 mb-2\",\n                                        children: stats.todayEntries\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm sm:text-base font-medium text-green-700\",\n                                        children: \"Total Entries\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 608,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-green-500 mt-1\",\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 604,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 603,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"bg-red-50 border-red-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 sm:p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl sm:text-4xl font-bold text-red-600 mb-2\",\n                                        children: stats.todayExits\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 620,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm sm:text-base font-medium text-red-700\",\n                                        children: \"Total Exits\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-red-500 mt-1\",\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 619,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 618,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"bg-purple-50 border-purple-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 sm:p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl sm:text-4xl font-bold text-purple-600 mb-2\",\n                                        children: stats.todayEntries + stats.todayExits\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 635,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm sm:text-base font-medium text-purple-700\",\n                                        children: \"Total Activity\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 638,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-purple-500 mt-1\",\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 634,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 633,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 586,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"inline-flex items-center gap-2 text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 651,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Auto-refreshing every 5 seconds\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 652,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs px-2 py-1 rounded \".concat(dataSource === 'mongodb' ? 'bg-green-100 text-green-700' : 'bg-yellow-100 text-yellow-700'),\n                                children: dataSource === 'mongodb' ? '�️ Shared MongoDB' : '💾 Local Fallback'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 653,\n                                columnNumber: 13\n                            }, this),\n                            lastUpdated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-400\",\n                                children: [\n                                    \"• \",\n                                    lastUpdated.toLocaleTimeString()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 661,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 650,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 649,\n                    columnNumber: 9\n                }, this),\n                !showAddForm && !editingStudent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>setShowAddForm(true),\n                            className: \"w-full h-16 text-lg\",\n                            disabled: loading,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"mr-2 h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 674,\n                                    columnNumber: 17\n                                }, this),\n                                \"Add New Student\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 673,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 672,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 671,\n                    columnNumber: 11\n                }, this),\n                (showAddForm || editingStudent) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: editingStudent ? \"Edit Student\" : \"Add New Student\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 685,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: [\n                                        editingStudent ? \"Update student information\" : \"Fill required fields to register a new student\",\n                                        \" - Data will be saved in \",\n                                        storageInfo.mode,\n                                        \" storage\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 686,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 684,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            className: \"text-base font-semibold\",\n                                            children: \"Student Photo *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 694,\n                                            columnNumber: 17\n                                        }, this),\n                                        imagePreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: imagePreview || \"/placeholder.svg\",\n                                                            alt: \"Student preview\",\n                                                            className: \"w-32 h-32 rounded-full border-4 border-blue-200 object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 700,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: removeImage,\n                                                            size: \"sm\",\n                                                            variant: \"destructive\",\n                                                            className: \"absolute -top-2 -right-2 h-6 w-6 rounded-full p-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 711,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 705,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 699,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-green-600\",\n                                                            children: \"✅ Photo uploaded successfully\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 715,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: ()=>{\n                                                                var _fileInputRef_current;\n                                                                return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                            },\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 717,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Change Photo\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 716,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 714,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 698,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-16 w-16 mx-auto text-gray-400 mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 724,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4\",\n                                                    children: \"Upload student photo (Required)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 725,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: ()=>{\n                                                                var _fileInputRef_current;\n                                                                return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                            },\n                                                            variant: \"outline\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 728,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Upload Photo\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 727,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: takePhoto,\n                                                            variant: \"outline\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 732,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Take Photo\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 731,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 726,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-2\",\n                                                    children: \"Supported formats: JPG, PNG, GIF (Max 5MB)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 736,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 723,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            ref: fileInputRef,\n                                            type: \"file\",\n                                            accept: \"image/*\",\n                                            onChange: handleImageSelect,\n                                            className: \"hidden\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 741,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 693,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_9__.Separator, {}, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 750,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"name\",\n                                                    children: \"Student Name *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 755,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"name\",\n                                                    value: newStudent.name,\n                                                    onChange: (e)=>setNewStudent({\n                                                            ...newStudent,\n                                                            name: e.target.value\n                                                        }),\n                                                    placeholder: \"Enter full name\",\n                                                    disabled: loading\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 756,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 754,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"phone\",\n                                                    children: \"Phone Number *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 765,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"phone\",\n                                                    value: newStudent.phone,\n                                                    onChange: (e)=>setNewStudent({\n                                                            ...newStudent,\n                                                            phone: e.target.value\n                                                        }),\n                                                    placeholder: \"10-digit phone number\",\n                                                    maxLength: 10,\n                                                    disabled: loading\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 766,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 764,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"email\",\n                                                    children: \"Email Address\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 776,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"email\",\n                                                    type: \"email\",\n                                                    value: newStudent.email,\n                                                    onChange: (e)=>setNewStudent({\n                                                            ...newStudent,\n                                                            email: e.target.value\n                                                        }),\n                                                    placeholder: \"<EMAIL>\",\n                                                    disabled: loading\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 777,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 775,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"class\",\n                                                    children: \"Class *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 787,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: newStudent.class,\n                                                    onValueChange: (value)=>setNewStudent({\n                                                            ...newStudent,\n                                                            class: value\n                                                        }),\n                                                    disabled: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select class\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 794,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 793,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"10th-A\",\n                                                                    children: \"10th A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 797,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"10th-B\",\n                                                                    children: \"10th B\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 798,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"10th-C\",\n                                                                    children: \"10th C\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 799,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"11th-A\",\n                                                                    children: \"11th A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 800,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"11th-B\",\n                                                                    children: \"11th B\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 801,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"11th-C\",\n                                                                    children: \"11th C\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 802,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"12th-A\",\n                                                                    children: \"12th A\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 803,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"12th-B\",\n                                                                    children: \"12th B\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 804,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"12th-C\",\n                                                                    children: \"12th C\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 805,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 796,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 788,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 786,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"department\",\n                                                    children: \"Department\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 810,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: newStudent.department,\n                                                    onValueChange: (value)=>setNewStudent({\n                                                            ...newStudent,\n                                                            department: value\n                                                        }),\n                                                    disabled: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select department\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 817,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 816,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Science\",\n                                                                    children: \"Science\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 820,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Commerce\",\n                                                                    children: \"Commerce\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 821,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Arts\",\n                                                                    children: \"Arts\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 822,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Computer Science\",\n                                                                    children: \"Computer Science\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 823,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 819,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 811,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 809,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"schedule\",\n                                                    children: \"Time Schedule\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 828,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: newStudent.schedule,\n                                                    onValueChange: (value)=>setNewStudent({\n                                                            ...newStudent,\n                                                            schedule: value\n                                                        }),\n                                                    disabled: loading,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select schedule\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 835,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 834,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Morning Shift (8:00 AM - 2:00 PM)\",\n                                                                    children: \"Morning Shift (8:00 AM - 2:00 PM)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 838,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Afternoon Shift (2:00 PM - 8:00 PM)\",\n                                                                    children: \"Afternoon Shift (2:00 PM - 8:00 PM)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 841,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"Full Day (8:00 AM - 4:00 PM)\",\n                                                                    children: \"Full Day (8:00 AM - 4:00 PM)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 844,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 837,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 829,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 827,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 753,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_9__.Separator, {}, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 850,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        editingStudent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleUpdateStudent,\n                                            className: \"flex-1\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 855,\n                                                    columnNumber: 21\n                                                }, this),\n                                                loading ? \"Updating...\" : \"Update Student\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 854,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleAddStudent,\n                                            className: \"flex-1\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 860,\n                                                    columnNumber: 21\n                                                }, this),\n                                                loading ? \"Adding...\" : \"Add Student\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 859,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: resetForm,\n                                            variant: \"outline\",\n                                            className: \"flex-1 bg-transparent\",\n                                            disabled: loading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 865,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Cancel\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 864,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 852,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 691,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 683,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: [\n                                                    \"Registered Students (\",\n                                                    filteredStudents.length,\n                                                    searchQuery && \" of \".concat(students.length),\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 878,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: [\n                                                    \"All registered students with their login credentials - Stored in \",\n                                                    storageInfo.mode,\n                                                    \" storage\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 881,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 877,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-72\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 887,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    type: \"text\",\n                                                    placeholder: \"Search by name, app number, class...\",\n                                                    value: searchQuery,\n                                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                                    className: \"pl-10 pr-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 888,\n                                                    columnNumber: 19\n                                                }, this),\n                                                searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>setSearchQuery(\"\"),\n                                                    className: \"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 902,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 896,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 886,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 885,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 876,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 875,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: students.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"h-16 w-16 mx-auto text-gray-300 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 912,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-500 mb-2\",\n                                        children: \"No students registered yet\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 913,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: 'Click \"Add New Student\" to get started'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 914,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 911,\n                                columnNumber: 15\n                            }, this) : filteredStudents.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-16 w-16 mx-auto text-gray-300 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 918,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-500 mb-2\",\n                                        children: \"No students found\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 919,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400\",\n                                        children: \"Try adjusting your search terms\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 920,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>setSearchQuery(\"\"),\n                                        className: \"mt-4\",\n                                        children: \"Clear Search\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 921,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 917,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: filteredStudents.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: student.image_url || \"/placeholder.svg?height=60&width=60\",\n                                                        alt: student.name,\n                                                        className: \"w-12 h-12 rounded-full border-2 border-gray-200 object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 934,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-lg\",\n                                                                children: student.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 940,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: [\n                                                                    student.class,\n                                                                    \" \",\n                                                                    student.department && \"- \".concat(student.department)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 941,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: student.phone\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 944,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            student.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: student.email\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 945,\n                                                                columnNumber: 43\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 939,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 933,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"font-mono text-xs\",\n                                                                        children: [\n                                                                            \"App: \",\n                                                                            student.application_number\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 953,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"ghost\",\n                                                                        onClick: ()=>copyToClipboard(student.application_number, \"app\"),\n                                                                        className: \"h-6 w-6 p-0\",\n                                                                        children: copiedText === \"app-\".concat(student.application_number) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 963,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 965,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 956,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 952,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                        variant: \"secondary\",\n                                                                        className: \"text-xs\",\n                                                                        children: [\n                                                                            \"Phone: \",\n                                                                            student.phone\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 970,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"ghost\",\n                                                                        onClick: ()=>copyToClipboard(student.phone, \"phone\"),\n                                                                        className: \"h-6 w-6 p-0\",\n                                                                        children: copiedText === \"phone-\".concat(student.phone) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 980,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 982,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 973,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 969,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 951,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleEditStudent(student),\n                                                                disabled: loading,\n                                                                className: \"h-8 w-8 p-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 997,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 990,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"destructive\",\n                                                                onClick: ()=>handleDeleteStudent(student),\n                                                                disabled: loading,\n                                                                className: \"h-8 w-8 p-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 1006,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 999,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 989,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 949,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, student.id, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 932,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 930,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 909,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 874,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: [\n                                    \"Admin Instructions - \",\n                                    storageInfo.mode,\n                                    \" Storage\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 1020,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 1019,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-blue-700 mb-2\",\n                                                children: \"Required Fields:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 1025,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Student Name (Full name required)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1027,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Phone Number (10 digits, unique)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1028,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Class Selection (from dropdown)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1029,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Student Photo (Upload or camera)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1030,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCDD Email (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1031,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCDD Department (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1032,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCDD Schedule (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1033,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 1026,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 1024,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-green-700 mb-2\",\n                                                children: \"Photo Requirements:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 1037,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc list-inside space-y-1 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Clear face photo required\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1039,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 JPG, PNG, GIF formats supported\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1040,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Maximum file size: 5MB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1041,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Upload from device or take with camera\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1042,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Used for face verification at station\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1043,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDCF8 Can be changed during editing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 1044,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 1038,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 1036,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 1023,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 1022,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 1018,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 549,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 548,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPanel, \"D96C9b84L8M3NtWSfKV6dSWNtVI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AdminPanel;\nfunction StatCard(param) {\n    let { icon, value, label, color } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow p-6 flex items-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-\".concat(color, \"-500 text-3xl mr-4\"),\n                children: icon\n            }, void 0, false, {\n                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 1058,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-2xl font-bold\",\n                        children: value\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 1060,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-600\",\n                        children: label\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 1061,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 1059,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 1057,\n        columnNumber: 5\n    }, this);\n}\n_c1 = StatCard;\nconst UserIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n        className: \"h-6 w-6 text-blue-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 1067,\n        columnNumber: 24\n    }, undefined);\n_c2 = UserIcon;\nconst EntryIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n        className: \"h-6 w-6 text-green-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 1068,\n        columnNumber: 25\n    }, undefined);\n_c3 = EntryIcon;\nconst ExitIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n        className: \"h-6 w-6 text-red-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 1069,\n        columnNumber: 24\n    }, undefined);\n_c4 = ExitIcon;\nconst TotalIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Camera_Check_Copy_Database_Edit_ImageIcon_LogOut_RefreshCw_Save_Search_Trash2_Upload_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n        className: \"h-6 w-6 text-purple-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 1070,\n        columnNumber: 25\n    }, undefined);\n_c5 = TotalIcon;\nconst RemainIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n        className: \"h-6 w-6 text-orange-600\"\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 1071,\n        columnNumber: 26\n    }, undefined);\n_c6 = RemainIcon;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"AdminPanel\");\n$RefreshReg$(_c1, \"StatCard\");\n$RefreshReg$(_c2, \"UserIcon\");\n$RefreshReg$(_c3, \"EntryIcon\");\n$RefreshReg$(_c4, \"ExitIcon\");\n$RefreshReg$(_c5, \"TotalIcon\");\n$RefreshReg$(_c6, \"RemainIcon\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/page.tsx\n"));

/***/ })

});