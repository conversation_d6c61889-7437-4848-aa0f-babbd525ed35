/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/entries/route";
exports.ids = ["app/api/entries/route"];
exports.modules = {

/***/ "(rsc)/./app/api/entries/route.ts":
/*!**********************************!*\
  !*** ./app/api/entries/route.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./lib/mongodb.ts\");\n\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const date = searchParams.get('date');\n        const studentId = searchParams.get('studentId');\n        if (!_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"]) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Database not available\"\n            }, {\n                status: 503\n            });\n        }\n        const client = await _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n        if (!client) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Database connection failed\"\n            }, {\n                status: 503\n            });\n        }\n        const db = client.db(\"idcard\");\n        const entries = db.collection(\"entry_logs\");\n        let query = {};\n        if (studentId) {\n            query.student_id = studentId;\n        }\n        if (date) {\n            const targetDate = new Date(date);\n            targetDate.setHours(0, 0, 0, 0);\n            const nextDate = new Date(targetDate);\n            nextDate.setDate(nextDate.getDate() + 1);\n            query.entry_time = {\n                $gte: targetDate,\n                $lt: nextDate\n            };\n        }\n        const results = await entries.find(query).sort({\n            entry_time: -1\n        }).toArray();\n        const data = results.map((e)=>({\n                ...e,\n                id: e._id.toString()\n            }));\n        console.log(`✅ Fetched ${data.length} entries from MongoDB`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"Error fetching entries:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch entries\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { student_id, student_name, application_number, status, face_match_score } = body;\n        const entry = await dbStore.recordEntry({\n            student_id,\n            student_name,\n            application_number,\n            status,\n            face_match_score,\n            entryTime: new Date()\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(entry);\n    } catch (error) {\n        console.error(\"Error recording entry:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to record entry\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get('id');\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Entry ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        await dbStore.deleteEntry(id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true\n        });\n    } catch (error) {\n        console.error(\"Error deleting entry:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to delete entry\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/entries/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/mongodb.ts":
/*!************************!*\
  !*** ./lib/mongodb.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_0__);\n\nconst uri = process.env.MONGODB_URI || \"\";\nconst options = {};\nlet client;\nlet clientPromise;\nif (!process.env.MONGODB_URI) {\n    throw new Error(\"Please add your MongoDB URI to .env\");\n}\nclient = new mongodb__WEBPACK_IMPORTED_MODULE_0__.MongoClient(uri, options);\nclientPromise = client.connect();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (clientPromise);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvbW9uZ29kYi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBcUM7QUFFckMsTUFBTUMsTUFBTUMsUUFBUUMsR0FBRyxDQUFDQyxXQUFXLElBQUk7QUFDdkMsTUFBTUMsVUFBVSxDQUFDO0FBRWpCLElBQUlDO0FBQ0osSUFBSUM7QUFFSixJQUFJLENBQUNMLFFBQVFDLEdBQUcsQ0FBQ0MsV0FBVyxFQUFFO0lBQzVCLE1BQU0sSUFBSUksTUFBTTtBQUNsQjtBQUVBRixTQUFTLElBQUlOLGdEQUFXQSxDQUFDQyxLQUFLSTtBQUM5QkUsZ0JBQWdCRCxPQUFPRyxPQUFPO0FBRTlCLGlFQUFlRixhQUFhQSxFQUFBIiwic291cmNlcyI6WyJEOlxcaWRjYXJkXFxzbWFydGlkY2FyZFxcbGliXFxtb25nb2RiLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE1vbmdvQ2xpZW50IH0gZnJvbSBcIm1vbmdvZGJcIlxyXG5cclxuY29uc3QgdXJpID0gcHJvY2Vzcy5lbnYuTU9OR09EQl9VUkkgfHwgXCJcIlxyXG5jb25zdCBvcHRpb25zID0ge31cclxuXHJcbmxldCBjbGllbnRcclxubGV0IGNsaWVudFByb21pc2U6IFByb21pc2U8TW9uZ29DbGllbnQ+XHJcblxyXG5pZiAoIXByb2Nlc3MuZW52Lk1PTkdPREJfVVJJKSB7XHJcbiAgdGhyb3cgbmV3IEVycm9yKFwiUGxlYXNlIGFkZCB5b3VyIE1vbmdvREIgVVJJIHRvIC5lbnZcIilcclxufVxyXG5cclxuY2xpZW50ID0gbmV3IE1vbmdvQ2xpZW50KHVyaSwgb3B0aW9ucylcclxuY2xpZW50UHJvbWlzZSA9IGNsaWVudC5jb25uZWN0KClcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGNsaWVudFByb21pc2UiXSwibmFtZXMiOlsiTW9uZ29DbGllbnQiLCJ1cmkiLCJwcm9jZXNzIiwiZW52IiwiTU9OR09EQl9VUkkiLCJvcHRpb25zIiwiY2xpZW50IiwiY2xpZW50UHJvbWlzZSIsIkVycm9yIiwiY29ubmVjdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fentries%2Froute&page=%2Fapi%2Fentries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fentries%2Froute.ts&appDir=D%3A%5Cidcard%5Csmartidcard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Csmartidcard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fentries%2Froute&page=%2Fapi%2Fentries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fentries%2Froute.ts&appDir=D%3A%5Cidcard%5Csmartidcard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Csmartidcard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_idcard_smartidcard_app_api_entries_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/entries/route.ts */ \"(rsc)/./app/api/entries/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/entries/route\",\n        pathname: \"/api/entries\",\n        filename: \"route\",\n        bundlePath: \"app/api/entries/route\"\n    },\n    resolvedPagePath: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\api\\\\entries\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_idcard_smartidcard_app_api_entries_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fentries%2Froute&page=%2Fapi%2Fentries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fentries%2Froute.ts&appDir=D%3A%5Cidcard%5Csmartidcard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Csmartidcard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "mongodb":
/*!**************************!*\
  !*** external "mongodb" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongodb");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fentries%2Froute&page=%2Fapi%2Fentries%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fentries%2Froute.ts&appDir=D%3A%5Cidcard%5Csmartidcard%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cidcard%5Csmartidcard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();