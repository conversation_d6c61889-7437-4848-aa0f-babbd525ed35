"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/student/page",{

/***/ "(app-pages-browser)/./app/student/page.tsx":
/*!******************************!*\
  !*** ./app/student/page.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentApp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp,Clock,Copy,Home,Info,LogOut,QrCode,RefreshCw,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _lib_database_store__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/database-store */ \"(app-pages-browser)/./lib/database-store.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction StudentApp() {\n    _s();\n    const [currentStudent, setCurrentStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [studentEntries, setStudentEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [copiedQR, setCopiedQR] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentApp.useEffect\": ()=>{\n            if (true) {\n                const studentLoggedIn = localStorage.getItem(\"studentLoggedIn\");\n                const studentId = localStorage.getItem(\"studentId\");\n                if (!studentLoggedIn || !studentId) {\n                    router.push(\"/\");\n                    return;\n                }\n                loadStudentData(studentId);\n            }\n        }\n    }[\"StudentApp.useEffect\"], [\n        router\n    ]);\n    // Auto-refresh student entries every 3 seconds for real-time updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentApp.useEffect\": ()=>{\n            if (!isAuthenticated || !currentStudent) return;\n            const interval = setInterval({\n                \"StudentApp.useEffect.interval\": ()=>{\n                    console.log(\"🔄 Auto-refreshing student entries...\");\n                    loadStudentEntries();\n                }\n            }[\"StudentApp.useEffect.interval\"], 3000) // 3 seconds for faster updates\n            ;\n            return ({\n                \"StudentApp.useEffect\": ()=>clearInterval(interval)\n            })[\"StudentApp.useEffect\"];\n        }\n    }[\"StudentApp.useEffect\"], [\n        isAuthenticated,\n        currentStudent\n    ]);\n    const loadStudentEntries = async ()=>{\n        if (!currentStudent) return;\n        try {\n            setRefreshing(true);\n            console.log(\"\\uD83D\\uDD0D Fetching entries for student: \".concat(currentStudent.name, \" (\").concat(currentStudent.application_number, \")\"));\n            // Try to get all entries and filter for this student\n            const entriesRes = await fetch('/api/entries');\n            if (entriesRes.ok) {\n                const allEntries = await entriesRes.json();\n                console.log(\"\\uD83D\\uDCCA Total entries in database: \".concat(allEntries.length));\n                // Filter entries for this student by both student_id and application_number\n                const studentEntries = allEntries.filter((entry)=>{\n                    const matchesId = entry.student_id === currentStudent.id;\n                    const matchesAppNumber = entry.application_number === currentStudent.application_number;\n                    const matchesName = entry.student_name === currentStudent.name;\n                    return matchesId || matchesAppNumber || matchesName;\n                });\n                // Sort by entry time (newest first)\n                studentEntries.sort((a, b)=>{\n                    const dateA = new Date(a.entry_time || a.entryTime);\n                    const dateB = new Date(b.entry_time || b.entryTime);\n                    return dateB.getTime() - dateA.getTime();\n                });\n                setStudentEntries(studentEntries);\n                setLastRefresh(new Date());\n                console.log(\"✅ Found \".concat(studentEntries.length, \" entries for \").concat(currentStudent.name, \":\"), studentEntries);\n            } else {\n                console.error(\"❌ API error: \".concat(entriesRes.status));\n            }\n        } catch (error) {\n            console.error(\"❌ Error refreshing entries:\", error);\n        } finally{\n            setRefreshing(false);\n        }\n    };\n    const loadStudentData = async (studentId)=>{\n        try {\n            setLoading(true);\n            setIsAuthenticated(true);\n            // Get student data from shared MongoDB via API\n            const studentsRes = await fetch('/api/students');\n            if (!studentsRes.ok) throw new Error('Failed to fetch students');\n            const students = await studentsRes.json();\n            const student = students.find((s)=>s.id === studentId);\n            if (student) {\n                setCurrentStudent(student);\n                // Get student's entry history from shared MongoDB\n                try {\n                    const entriesRes = await fetch(\"/api/entries?studentId=\".concat(student.id));\n                    if (entriesRes.ok) {\n                        const allEntries = await entriesRes.json();\n                        // Filter entries for this student\n                        const studentEntries = allEntries.filter((entry)=>entry.student_id === student.id || entry.application_number === student.application_number);\n                        setStudentEntries(studentEntries);\n                        console.log(\"✅ Loaded \".concat(studentEntries.length, \" entries for student \").concat(student.name));\n                    } else {\n                        console.log(\"⚠️ Could not fetch entries from API, using fallback\");\n                        const entries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_8__.dbStore.getStudentEntries(student.id);\n                        setStudentEntries(entries);\n                    }\n                } catch (entriesError) {\n                    console.log(\"⚠️ API error, using database fallback for entries\");\n                    const entries = await _lib_database_store__WEBPACK_IMPORTED_MODULE_8__.dbStore.getStudentEntries(student.id);\n                    setStudentEntries(entries);\n                }\n            } else {\n                handleLogout();\n            }\n        } catch (error) {\n            console.error(\"Error loading student data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleLogout = ()=>{\n        if (true) {\n            localStorage.removeItem(\"studentLoggedIn\");\n            localStorage.removeItem(\"studentId\");\n            localStorage.removeItem(\"studentAppNumber\");\n        }\n        router.push(\"/\");\n    };\n    const handleRefresh = ()=>{\n        if (currentStudent) {\n            loadStudentData(currentStudent.id);\n        }\n    };\n    const generateSimpleQRCode = ()=>{\n        if (!currentStudent) return \"\";\n        return currentStudent.application_number;\n    };\n    const copyQRData = async ()=>{\n        try {\n            const qrData = generateSimpleQRCode();\n            await navigator.clipboard.writeText(qrData);\n            setCopiedQR(true);\n            setTimeout(()=>setCopiedQR(false), 2000);\n        } catch (error) {\n            alert(\"Failed to copy QR data\");\n        }\n    };\n    const formatDateTime = (date)=>{\n        return date.toLocaleString(\"en-IN\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const formatTime = (date)=>{\n        return date.toLocaleString(\"en-IN\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const formatDate = (date)=>{\n        return date.toLocaleString(\"en-IN\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\"\n        });\n    };\n    const toggleSection = (section)=>{\n        setActiveSection(activeSection === section ? null : section);\n    };\n    if (!isAuthenticated || !currentStudent) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-blue-50 p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-700\",\n                        children: \"Loading student data...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                lineNumber: 201,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n            lineNumber: 200,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-green-50 to-blue-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"shadow-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: (currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.image_url) || \"/placeholder.svg?height=50&width=50\",\n                                            alt: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name,\n                                            className: \"w-10 h-10 rounded-full border-2 border-green-200 object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: [\n                                                        \"Welcome, \",\n                                                        currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        \"App No: \",\n                                                        currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.application_number\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 w-full sm:w-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleRefresh,\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            disabled: loading,\n                                            className: \"flex-1 sm:flex-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"mr-1 h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only sm:not-sr-only\",\n                                                    children: \"Refresh\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleLogout,\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"flex-1 sm:flex-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-1 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only sm:not-sr-only\",\n                                                    children: \"Logout\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: ()=>router.push(\"/\"),\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"flex-1 sm:flex-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"mr-1 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only sm:not-sr-only\",\n                                                    children: \"Home\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"border border-blue-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>toggleSection(\"idCard\"),\n                                    className: \"w-full p-4 flex items-center justify-between text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-100 p-2 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-5 w-5 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Digital ID Card\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Show your QR code at security stations\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, this),\n                                        activeSection === \"idCard\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 13\n                                }, this),\n                                activeSection === \"idCard\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"pt-0 px-4 pb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl p-5 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-xl font-bold\",\n                                                                children: \"College Identity Card\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-blue-100 text-sm\",\n                                                                children: \"Official Identification Document\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-blue-200\",\n                                                                children: \"Valid Until\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold\",\n                                                                children: \"31/12/2025\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col md:flex-row gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white border-2 border-white rounded-lg overflow-hidden\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: (currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.image_url) || \"/placeholder.svg?height=100&width=80\",\n                                                                            alt: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name,\n                                                                            className: \"w-20 h-24 object-cover\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                            lineNumber: 306,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 305,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                                className: \"text-lg font-bold\",\n                                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 314,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2 space-y-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-xs text-blue-200\",\n                                                                                                children: \"Application Number\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                                lineNumber: 317,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"font-mono font-bold\",\n                                                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.application_number\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                                lineNumber: 318,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 316,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"text-xs text-blue-200\",\n                                                                                                children: \"Department\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                                lineNumber: 321,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"font-bold\",\n                                                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.department\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                                lineNumber: 322,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                        lineNumber: 320,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 315,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 313,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 grid grid-cols-2 gap-3 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-blue-200\",\n                                                                                children: \"Class\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 330,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-bold\",\n                                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.class\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 331,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 329,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-blue-200\",\n                                                                                children: \"Phone\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 334,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-bold\",\n                                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.phone\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                                lineNumber: 335,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 333,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-white p-2 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: \"https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=\".concat(encodeURIComponent(generateSimpleQRCode())),\n                                                                    alt: \"Student QR Code\",\n                                                                    className: \"w-32 h-32\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2 text-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-mono text-xs bg-blue-400/20 px-2 py-1 rounded\",\n                                                                    children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.application_number\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 349,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_9__.Input, {\n                                                                value: generateSimpleQRCode(),\n                                                                readOnly: true,\n                                                                className: \"bg-white/10 border-white/20 text-white placeholder-white/50 text-center font-mono text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                onClick: copyQRData,\n                                                                size: \"sm\",\n                                                                className: \"absolute top-1 right-1 h-6 px-2 bg-white/20 hover:bg-white/30\",\n                                                                children: copiedQR ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 37\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 69\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-blue-200 mt-1 text-center\",\n                                                        children: \"Copy application number for manual entry at station\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"border border-green-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>toggleSection(\"details\"),\n                                    className: \"w-full p-4 flex items-center justify-between text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-100 p-2 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-5 w-5 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Personal Details\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"View your registration information\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 15\n                                        }, this),\n                                        activeSection === \"details\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 13\n                                }, this),\n                                activeSection === \"details\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"pt-0 px-4 pb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col md:flex-row gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: (currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.image_url) || \"/placeholder.svg?height=120&width=120\",\n                                                        alt: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name,\n                                                        className: \"w-24 h-24 rounded-full border-4 border-green-200 object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"mt-2 text-lg font-semibold\",\n                                                        children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"mt-1\",\n                                                        children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.class\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-gray-500 text-sm\",\n                                                                        children: \"Phone\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 418,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.phone\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 419,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 417,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-gray-500 text-sm\",\n                                                                        children: \"Email\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 422,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: (currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.email) || \"Not provided\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 423,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-gray-500 text-sm\",\n                                                                        children: \"Department\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 426,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.department\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 427,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-gray-500 text-sm\",\n                                                                        children: \"Schedule\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 430,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium\",\n                                                                        children: (currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.schedule) || \"Not assigned\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                        lineNumber: 431,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 429,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                className: \"text-gray-500 text-sm\",\n                                                                children: \"Application Number\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 438,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                className: \"font-mono mt-1\",\n                                                                children: currentStudent === null || currentStudent === void 0 ? void 0 : currentStudent.application_number\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"border border-amber-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>toggleSection(\"history\"),\n                                    className: \"w-full p-4 flex items-center justify-between text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-amber-100 p-2 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5 text-amber-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"Entry/Exit History\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"View your campus access records\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 15\n                                        }, this),\n                                        activeSection === \"history\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 13\n                                }, this),\n                                activeSection === \"history\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"pt-0 px-4 pb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 max-h-80 overflow-y-auto\",\n                                        children: studentEntries.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-12 w-12 mx-auto text-gray-300 mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500\",\n                                                    children: \"No entries recorded yet\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-400 mt-2\",\n                                                    children: \"Show your digital ID at security station to record entries\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 21\n                                        }, this) : studentEntries.map((entry)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium capitalize flex items-center gap-1\",\n                                                                children: entry.status === \"entry\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-600\",\n                                                                    children: \"\\uD83D\\uDFE2 Entry\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 489,\n                                                                    columnNumber: 31\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-600\",\n                                                                    children: \"\\uD83D\\uDD34 Exit\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                    lineNumber: 491,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 487,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: formatDate(entry.entryTime)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 494,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: [\n                                                                    entry.status === \"entry\" ? \"In: \" : \"Out: \",\n                                                                    formatTime(entry.entryTime)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 495,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            entry.exitTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: [\n                                                                    \"Out: \",\n                                                                    formatTime(entry.exitTime)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: entry.status === \"entry\" ? \"default\" : \"secondary\",\n                                                        children: entry.verified ? \"✅ Verified\" : \"⚠️ Pending\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, entry.id, true, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 450,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"border border-blue-100\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            className: \"pb-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-5 w-5 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"How to Use Your Digital ID Card\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 517,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-50 p-4 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-blue-700 mb-2 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"QR Code Scanning\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                    className: \"list-decimal list-inside space-y-1 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Show your QR code to station operator\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Operator will scan with the camera\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 532,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Hold QR code steady in front of camera\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"System retrieves your details automatically\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Proceed to face verification\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-50 p-4 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-green-700 mb-2 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_Clock_Copy_Home_Info_LogOut_QrCode_RefreshCw_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Manual Input Option\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"list-disc list-inside space-y-1 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Copy your Application Number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: 'Go to station\\'s \"Manual Entry\" section'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Paste your Application Number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: 'Click \"Validate\" to retrieve details'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"Continue with face verification\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.Alert, {\n                                    className: \"mt-4 bg-yellow-50 border-yellow-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.AlertDescription, {\n                                        className: \"text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold\",\n                                                children: \"Important:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" Your digital ID card is for official use only. Do not share it with unauthorized persons. Report lost cards immediately.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                                    lineNumber: 553,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                            lineNumber: 523,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n                    lineNumber: 516,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n            lineNumber: 211,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\idcard\\\\smartidcard\\\\app\\\\student\\\\page.tsx\",\n        lineNumber: 210,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentApp, \"Iv1Gi/gT7Xk86B1IrcJkbbIwQFw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = StudentApp;\nvar _c;\n$RefreshReg$(_c, \"StudentApp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/student/page.tsx\n"));

/***/ })

});